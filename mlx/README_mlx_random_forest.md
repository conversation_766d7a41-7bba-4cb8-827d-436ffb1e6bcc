# MLX Random Forest Implementation

This notebook demonstrates a complete Random Forest classifier implementation using Apple's MLX framework, optimized for Apple Silicon.

## 🚀 Features

- **Native Apple Silicon Optimization**: Leverages MLX for Metal Performance Shaders acceleration
- **Complete Random Forest Algorithm**: 
  - Bootstrap sampling with replacement
  - Random feature selection at each split
  - Gini impurity for splitting criteria
  - Majority voting for ensemble predictions
- **Performance Comparison**: Side-by-side comparison with scikit-learn
- **Comprehensive Visualizations**: Accuracy plots, timing comparisons, and parameter sensitivity analysis
- **Educational Content**: Clear explanations and step-by-step implementation

## 📊 What's Included

### 1. Core Implementation
- `TreeNode`: Decision tree node structure
- `MLXDecisionTree`: Individual decision tree with MLX operations
- `MLXRandomForest`: Complete Random Forest ensemble

### 2. Performance Analysis
- Accuracy comparison with scikit-learn
- Training and prediction time benchmarks
- Per-class accuracy analysis
- Parameter sensitivity testing

### 3. Visualizations
- Accuracy comparison charts
- Training time analysis
- Prediction time benchmarks
- Parameter sensitivity plots

## 🎯 Results Summary

On the Iris dataset (150 samples, 4 features, 3 classes):

| Metric | MLX Random Forest | Scikit-learn RF |
|--------|------------------|------------------|
| **Accuracy** | 100.0% | ~97-100% |
| **Training Time** | ~0.3-1.7s | ~0.01-0.05s |
| **Prediction Time** | ~0.2-1.0s | ~0.001-0.005s |

*Note: Times vary based on number of estimators and system load*

## 🔧 Hardware Requirements

- **Apple Silicon Mac** (M1, M2, M3, etc.)
- **MLX Framework** installed
- **Python 3.8+** with required dependencies

## 📦 Dependencies

```python
mlx-core
numpy
pandas
matplotlib
seaborn
scikit-learn
```

## 🚀 Quick Start

1. **Open the notebook**: `notebooks/mlx_random_forest.ipynb`
2. **Run all cells**: Execute cells sequentially to see the full implementation
3. **Explore results**: View performance comparisons and visualizations

## 💡 Key Insights

### Advantages of MLX Implementation:
- **Memory Efficient**: Lazy evaluation reduces memory usage
- **Apple Silicon Optimized**: Native Metal acceleration
- **Educational Value**: Clear, readable implementation
- **Extensible**: Easy to modify for regression or add features

### Current Limitations:
- **Performance**: Slower than highly optimized scikit-learn for small datasets
- **Feature Set**: Basic implementation without advanced optimizations
- **Platform Specific**: Requires Apple Silicon hardware

## 🔬 Technical Details

### Algorithm Implementation:
1. **Bootstrap Sampling**: Uses `mlx.random.randint` for sampling with replacement
2. **Feature Selection**: Random permutation with `mlx.random.permutation`
3. **Splitting Criteria**: Gini impurity calculation with numpy for unique operations
4. **Tree Building**: Recursive tree construction with MLX arrays
5. **Prediction**: Majority voting across all trees

### Performance Optimizations:
- Hybrid MLX/numpy approach for operations not yet supported in MLX
- Efficient array operations where possible
- Memory-conscious tree building

## 📈 Use Cases

### Ideal For:
- **Research and Education**: Understanding Random Forest internals
- **Apple Silicon Optimization**: ML pipelines targeting Apple hardware
- **Memory-Constrained Environments**: Efficient memory usage
- **Prototyping**: Quick experimentation with ensemble methods

### Not Ideal For:
- **Production Systems**: Use scikit-learn for production workloads
- **Large Datasets**: Current implementation may be slow
- **Non-Apple Hardware**: Requires Apple Silicon for optimal performance

## 🛠️ Future Improvements

- **Performance Optimization**: Further MLX integration as framework matures
- **Regression Support**: Extend to Random Forest Regressor
- **Feature Importance**: Add feature importance calculation
- **Parallel Processing**: Optimize tree building parallelization
- **Advanced Features**: Add out-of-bag scoring, partial fitting

## 📚 Learning Outcomes

After working through this notebook, you'll understand:
- How Random Forests work internally
- MLX framework capabilities and limitations
- Performance trade-offs in ML framework choice
- Implementation details of ensemble methods
- Apple Silicon optimization techniques

## 🤝 Contributing

Feel free to extend this implementation with:
- Additional splitting criteria (entropy, etc.)
- Regression capabilities
- Performance optimizations
- Additional visualizations
- More comprehensive benchmarks

---

*This implementation serves as both a functional Random Forest and an educational resource for understanding ensemble methods and MLX framework capabilities.*
