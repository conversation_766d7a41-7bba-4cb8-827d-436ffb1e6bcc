"""
Test script to verify the MLX Random Forest notebook implementation works correctly.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the working implementation
from mlx_random_forest import MLXRandomForest
import mlx.core as mlx
import numpy as np
from sklearn.datasets import load_iris
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
import time

def test_mlx_random_forest():
    """Test the MLX Random Forest implementation"""
    print("🧪 Testing MLX Random Forest Implementation")
    print("=" * 50)
    
    # Load Iris dataset
    iris = load_iris()
    X = mlx.array(iris.data.astype(np.float32))
    y = mlx.array(iris.target.astype(np.int32))
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(
        iris.data, iris.target, test_size=0.3, random_state=42
    )
    
    X_train = mlx.array(X_train.astype(np.float32))
    X_test = mlx.array(X_test.astype(np.float32))
    y_train = mlx.array(y_train.astype(np.int32))
    y_test = mlx.array(y_test.astype(np.int32))
    
    print(f"Dataset shape: {X.shape}")
    print(f"Training set: {X_train.shape}")
    print(f"Test set: {X_test.shape}")
    
    # Test with different parameters
    test_configs = [
        {"n_estimators": 10, "max_depth": 3, "name": "Small Forest"},
        {"n_estimators": 25, "max_depth": 5, "name": "Medium Forest"},
        {"n_estimators": 50, "max_depth": 7, "name": "Large Forest"},
    ]
    
    results = []
    
    for config in test_configs:
        print(f"\n🌳 Testing {config['name']}...")
        
        start_time = time.time()
        rf = MLXRandomForest(
            n_estimators=config["n_estimators"],
            max_depth=config["max_depth"],
            max_features='sqrt',
            random_state=42
        )
        
        rf.fit(X_train, y_train)
        train_time = time.time() - start_time
        
        start_time = time.time()
        predictions = rf.predict(X_test)
        pred_time = time.time() - start_time
        
        accuracy = rf.score(X_test, y_test)
        
        result = {
            "name": config["name"],
            "n_estimators": config["n_estimators"],
            "max_depth": config["max_depth"],
            "accuracy": accuracy,
            "train_time": train_time,
            "pred_time": pred_time
        }
        results.append(result)
        
        print(f"   ✅ Accuracy: {accuracy:.4f}")
        print(f"   ⏱️  Training Time: {train_time:.3f}s")
        print(f"   ⚡ Prediction Time: {pred_time:.4f}s")
    
    # Create visualization
    print(f"\n📊 Creating Performance Visualization...")
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    names = [r["name"] for r in results]
    accuracies = [r["accuracy"] for r in results]
    train_times = [r["train_time"] for r in results]
    
    # Accuracy plot
    bars1 = ax1.bar(names, accuracies, color=['#FF6B6B', '#4ECDC4', '#45B7D1'], alpha=0.8)
    ax1.set_ylabel('Accuracy')
    ax1.set_title('Accuracy by Forest Size')
    ax1.set_ylim(0, 1.1)
    ax1.grid(axis='y', alpha=0.3)
    
    for bar, acc in zip(bars1, accuracies):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                 f'{acc:.3f}', ha='center', va='bottom', fontweight='bold')
    
    # Training time plot
    bars2 = ax2.bar(names, train_times, color=['#FF6B6B', '#4ECDC4', '#45B7D1'], alpha=0.8)
    ax2.set_ylabel('Training Time (seconds)')
    ax2.set_title('Training Time by Forest Size')
    ax2.grid(axis='y', alpha=0.3)
    
    for bar, time_val in zip(bars2, train_times):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + max(train_times)*0.01,
                 f'{time_val:.2f}s', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('mlx_rf_test_results.png', dpi=300, bbox_inches='tight')
    print(f"   📈 Plot saved as 'mlx_rf_test_results.png'")
    
    # Summary
    print(f"\n🎯 Test Summary:")
    print(f"   ✅ All configurations tested successfully")
    print(f"   📊 Best accuracy: {max(accuracies):.4f}")
    print(f"   ⚡ Fastest training: {min(train_times):.3f}s")
    print(f"   🖥️  MLX Metal available: {mlx.metal.is_available()}")
    
    if mlx.metal.is_available():
        device_info = mlx.metal.device_info()
        print(f"   💻 Device: {device_info.get('device_name', 'Unknown')}")
    
    return results

if __name__ == "__main__":
    try:
        results = test_mlx_random_forest()
        print(f"\n🎉 All tests passed successfully!")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
