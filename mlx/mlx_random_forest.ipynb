{"cells": [{"cell_type": "markdown", "id": "1", "metadata": {}, "source": ["# Random Forest Implementation with MLX\n", "\n", "This notebook implements a Random Forest classifier using Apple's MLX framework for efficient computation on Apple Silicon.\n", "\n", "## Features\n", "- Native Apple Silicon optimization with MLX\n", "- Bootstrap sampling with replacement\n", "- Random feature selection at each split\n", "- Gini impurity for splitting criteria\n", "- Majority voting for predictions\n", "- Performance comparison with scikit-learn"]}, {"cell_type": "code", "execution_count": 2, "id": "2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MLX Metal available: True\n", "Device: Apple M3 Max\n", "Memory: 128.0 GB\n"]}], "source": ["import mlx.core as mlx\n", "import numpy as np\n", "import pandas as pd\n", "from typing import Optional, List, Tuple\n", "from dataclasses import dataclass\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.datasets import make_classification, load_iris\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import accuracy_score, classification_report\n", "from sklearn.ensemble import RandomForestClassifier\n", "import time\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set plotting style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "print(f\"MLX Metal available: {mlx.metal.is_available()}\")\n", "if mlx.metal.is_available():\n", "    device_info = mlx.metal.device_info()\n", "    print(f\"Device: {device_info.get('device_name', 'Unknown')}\")\n", "    print(f\"Memory: {device_info.get('memory_size', 0) / (1024**3):.1f} GB\")"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["## Tree Node Structure\n", "\n", "First, let's define the basic structure for our decision tree nodes."]}, {"cell_type": "code", "execution_count": 3, "id": "4", "metadata": {}, "outputs": [], "source": ["@dataclass\n", "class TreeNode:\n", "    \"\"\"Node structure for decision tree\"\"\"\n", "    is_leaf: bool = False\n", "    feature_idx: Optional[int] = None\n", "    threshold: Optional[float] = None\n", "    prediction: Optional[int] = None\n", "    left: Optional['TreeNode'] = None\n", "    right: Optional['TreeNode'] = None\n", "    samples: int = 0\n", "    \n", "    def predict_sample(self, x: mlx.array) -> int:\n", "        \"\"\"Predict a single sample\"\"\"\n", "        if self.is_leaf:\n", "            return self.prediction\n", "        \n", "        if x[self.feature_idx] <= self.threshold:\n", "            return self.left.predict_sample(x)\n", "        else:\n", "            return self.right.predict_sample(x)"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["## MLX Decision Tree Implementation\n", "\n", "Now let's implement the decision tree using MLX operations."]}, {"cell_type": "code", "execution_count": 4, "id": "6", "metadata": {}, "outputs": [], "source": ["class MLXDecisionTree:\n", "    \"\"\"Decision Tree implementation using MLX\"\"\"\n", "    \n", "    def __init__(self, max_depth: int = 10, min_samples_split: int = 2, \n", "                 max_features: Optional[int] = None, random_state: Optional[int] = None):\n", "        self.max_depth = max_depth\n", "        self.min_samples_split = min_samples_split\n", "        self.max_features = max_features\n", "        self.random_state = random_state\n", "        self.root = None\n", "        \n", "        if random_state is not None:\n", "            mlx.random.seed(random_state)\n", "    \n", "    def _gini_impurity(self, y: mlx.array) -> float:\n", "        \"\"\"Calculate Gini impurity using numpy for unique operations\"\"\"\n", "        if len(y) == 0:\n", "            return 0.0\n", "        \n", "        # Get unique classes and their counts using numpy\n", "        y_np = np.array(y)\n", "        classes, counts = np.unique(y_np, return_counts=True)\n", "        n_samples = len(y)\n", "        \n", "        gini = 1.0\n", "        for count in counts:\n", "            p = count / n_samples\n", "            gini -= p * p\n", "        \n", "        return gini\n", "    \n", "    def _best_split(self, X: mlx.array, y: mlx.array, feature_indices: mlx.array) -> Tuple[Optional[int], Optional[float], float]:\n", "        \"\"\"Find the best split for the given data\"\"\"\n", "        best_gini = float('inf')\n", "        best_feature = None\n", "        best_threshold = None\n", "        \n", "        n_samples = len(y)\n", "        \n", "        for feature_idx in feature_indices:\n", "            feature_idx = int(feature_idx.item())\n", "            feature_values = X[:, feature_idx]\n", "            \n", "            # Get unique values as potential thresholds\n", "            feature_values_np = np.array(feature_values)\n", "            unique_values = np.unique(feature_values_np)\n", "            unique_values = mlx.array(unique_values)\n", "            \n", "            for threshold in unique_values:\n", "                threshold = threshold.item()\n", "                \n", "                # Split data using numpy for boolean indexing\n", "                feature_values_np = np.array(feature_values)\n", "                y_np = np.array(y)\n", "                left_mask = feature_values_np <= threshold\n", "                right_mask = ~left_mask\n", "                \n", "                left_y = mlx.array(y_np[left_mask])\n", "                right_y = mlx.array(y_np[right_mask])\n", "                \n", "                if len(left_y) == 0 or len(right_y) == 0:\n", "                    continue\n", "                \n", "                # Calculate weighted Gini impurity\n", "                left_gini = self._gini_impurity(left_y)\n", "                right_gini = self._gini_impurity(right_y)\n", "                \n", "                weighted_gini = (len(left_y) * left_gini + len(right_y) * right_gini) / n_samples\n", "                \n", "                if weighted_gini < best_gini:\n", "                    best_gini = weighted_gini\n", "                    best_feature = feature_idx\n", "                    best_threshold = threshold\n", "        \n", "        return best_feature, best_threshold, best_gini\n", "    \n", "    def _build_tree(self, X: mlx.array, y: mlx.array, depth: int = 0) -> TreeNode:\n", "        \"\"\"Recursively build the decision tree\"\"\"\n", "        n_samples, n_features = X.shape\n", "        \n", "        # Determine max features to consider\n", "        if self.max_features is None:\n", "            max_features = n_features\n", "        else:\n", "            max_features = min(self.max_features, n_features)\n", "        \n", "        # Randomly select features to consider\n", "        all_features = mlx.arange(n_features)\n", "        shuffled_features = mlx.random.permutation(all_features)\n", "        feature_indices = shuffled_features[:max_features]\n", "        \n", "        # Check stopping criteria\n", "        y_np = np.array(y)\n", "        unique_classes = np.unique(y_np)\n", "        \n", "        if (len(unique_classes) == 1 or \n", "            depth >= self.max_depth or \n", "            n_samples < self.min_samples_split):\n", "            \n", "            # Create leaf node with majority class\n", "            classes, counts = np.unique(y_np, return_counts=True)\n", "            majority_class = classes[np.argmax(counts)]\n", "            \n", "            return TreeNode(\n", "                is_leaf=True,\n", "                prediction=int(majority_class),\n", "                samples=n_samples\n", "            )\n", "        \n", "        # Find best split\n", "        best_feature, best_threshold, best_gini = self._best_split(X, y, feature_indices)\n", "        \n", "        if best_feature is None:\n", "            # No valid split found, create leaf\n", "            y_np = np.array(y)\n", "            classes, counts = np.unique(y_np, return_counts=True)\n", "            majority_class = classes[np.argmax(counts)]\n", "            \n", "            return TreeNode(\n", "                is_leaf=True,\n", "                prediction=int(majority_class),\n", "                samples=n_samples\n", "            )\n", "        \n", "        # Split data using numpy for boolean indexing\n", "        X_np = np.array(X)\n", "        y_np = np.array(y)\n", "        left_mask = X_np[:, best_feature] <= best_threshold\n", "        right_mask = ~left_mask\n", "        \n", "        # Recursively build subtrees\n", "        left_child = self._build_tree(mlx.array(X_np[left_mask]), mlx.array(y_np[left_mask]), depth + 1)\n", "        right_child = self._build_tree(mlx.array(X_np[right_mask]), mlx.array(y_np[right_mask]), depth + 1)\n", "        \n", "        return TreeNode(\n", "            is_leaf=False,\n", "            feature_idx=best_feature,\n", "            threshold=best_threshold,\n", "            left=left_child,\n", "            right=right_child,\n", "            samples=n_samples\n", "        )\n", "    \n", "    def fit(self, X: mlx.array, y: mlx.array):\n", "        \"\"\"Train the decision tree\"\"\"\n", "        self.root = self._build_tree(X, y)\n", "        return self\n", "    \n", "    def predict(self, X: mlx.array) -> mlx.array:\n", "        \"\"\"Make predictions on new data\"\"\"\n", "        predictions = []\n", "        for i in range(len(X)):\n", "            pred = self.root.predict_sample(X[i])\n", "            predictions.append(pred)\n", "        return mlx.array(predictions)"]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["## MLX Random Forest Implementation\n", "\n", "Now let's implement the Random Forest ensemble using our MLX Decision Trees."]}, {"cell_type": "code", "execution_count": 5, "id": "9", "metadata": {}, "outputs": [], "source": ["class MLXRandomForest:\n", "    \"\"\"Random Forest implementation using MLX Decision Trees\"\"\"\n", "    \n", "    def __init__(self, n_estimators: int = 100, max_depth: int = 10, \n", "                 min_samples_split: int = 2, max_features: str = 'sqrt',\n", "                 bootstrap: bool = True, random_state: Optional[int] = None):\n", "        self.n_estimators = n_estimators\n", "        self.max_depth = max_depth\n", "        self.min_samples_split = min_samples_split\n", "        self.max_features = max_features\n", "        self.bootstrap = bootstrap\n", "        self.random_state = random_state\n", "        self.trees = []\n", "        \n", "        if random_state is not None:\n", "            mlx.random.seed(random_state)\n", "    \n", "    def _get_max_features(self, n_features: int) -> int:\n", "        \"\"\"Calculate max_features based on the strategy\"\"\"\n", "        if self.max_features == 'sqrt':\n", "            return int(np.sqrt(n_features))\n", "        elif self.max_features == 'log2':\n", "            return int(np.log2(n_features))\n", "        elif isinstance(self.max_features, int):\n", "            return min(self.max_features, n_features)\n", "        elif isinstance(self.max_features, float):\n", "            return int(self.max_features * n_features)\n", "        else:\n", "            return n_features\n", "    \n", "    def _bootstrap_sample(self, X: mlx.array, y: mlx.array) -> Tuple[mlx.array, mlx.array]:\n", "        \"\"\"Create bootstrap sample of the data\"\"\"\n", "        n_samples = len(X)\n", "        \n", "        if self.bootstrap:\n", "            # Sample with replacement using randint\n", "            indices = mlx.random.randint(0, n_samples, (n_samples,))\n", "            return X[indices], y[indices]\n", "        else:\n", "            return X, y\n", "    \n", "    def fit(self, X: mlx.array, y: mlx.array):\n", "        \"\"\"Train the random forest\"\"\"\n", "        n_samples, n_features = X.shape\n", "        max_features = self._get_max_features(n_features)\n", "        \n", "        self.trees = []\n", "        \n", "        for i in range(self.n_estimators):\n", "            # Create bootstrap sample\n", "            X_bootstrap, y_bootstrap = self._bootstrap_sample(X, y)\n", "            \n", "            # Create and train tree\n", "            tree = MLXDecisionTree(\n", "                max_depth=self.max_depth,\n", "                min_samples_split=self.min_samples_split,\n", "                max_features=max_features,\n", "                random_state=self.random_state + i if self.random_state else None\n", "            )\n", "            \n", "            tree.fit(X_bootstrap, y_bootstrap)\n", "            self.trees.append(tree)\n", "        \n", "        return self\n", "    \n", "    def predict(self, X: mlx.array) -> mlx.array:\n", "        \"\"\"Make predictions using majority voting\"\"\"\n", "        n_samples = len(X)\n", "        all_predictions = []\n", "        \n", "        # Get predictions from all trees\n", "        for tree in self.trees:\n", "            tree_predictions = tree.predict(X)\n", "            all_predictions.append(tree_predictions)\n", "        \n", "        # Convert to numpy for easier manipulation\n", "        all_predictions = np.array([np.array(pred) for pred in all_predictions]).T\n", "        \n", "        # Majority voting\n", "        final_predictions = []\n", "        for i in range(n_samples):\n", "            sample_predictions = np.array(all_predictions[i])\n", "            unique_preds, counts = np.unique(sample_predictions, return_counts=True)\n", "            majority_pred = unique_preds[np.argmax(counts)]\n", "            final_predictions.append(int(majority_pred))\n", "        \n", "        return mlx.array(final_predictions)\n", "    \n", "    def score(self, X: mlx.array, y: mlx.array) -> float:\n", "        \"\"\"Calculate accuracy score\"\"\"\n", "        predictions = self.predict(X)\n", "        correct = mlx.sum(predictions == y)\n", "        return float(correct.item()) / len(y)"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["## Performance Testing and Comparison\n", "\n", "Let's test our MLX Random Forest implementation and compare it with scikit-learn."]}, {"cell_type": "code", "execution_count": 6, "id": "11", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🌸 Testing on Iris Dataset\n", "========================================\n", "Dataset shape: (150, 4)\n", "Training set: (105, 4)\n", "Test set: (45, 4)\n", "Classes: ['setosa' 'versicolor' 'virginica']\n"]}], "source": ["# Test on Iris dataset\n", "print(\"🌸 Testing on Iris Dataset\")\n", "print(\"=\" * 40)\n", "\n", "# Load Iris dataset\n", "iris = load_iris()\n", "X_iris = mlx.array(iris.data.astype(np.float32))\n", "y_iris = mlx.array(iris.target.astype(np.int32))\n", "\n", "# Split data\n", "X_train_iris, X_test_iris, y_train_iris, y_test_iris = train_test_split(\n", "    iris.data, iris.target, test_size=0.3, random_state=42\n", ")\n", "\n", "X_train_iris = mlx.array(X_train_iris.astype(np.float32))\n", "X_test_iris = mlx.array(X_test_iris.astype(np.float32))\n", "y_train_iris = mlx.array(y_train_iris.astype(np.int32))\n", "y_test_iris = mlx.array(y_test_iris.astype(np.int32))\n", "\n", "print(f\"Dataset shape: {X_iris.shape}\")\n", "print(f\"Training set: {X_train_iris.shape}\")\n", "print(f\"Test set: {X_test_iris.shape}\")\n", "print(f\"Classes: {iris.target_names}\")"]}, {"cell_type": "code", "execution_count": 7, "id": "12", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🚀 Training MLX Random Forest...\n", "✅ MLX Random Forest Results:\n", "   Training time: 0.851 seconds\n", "   Prediction time: 1.159 seconds\n", "   Accuracy: 1.0000\n"]}], "source": ["# Train MLX Random Forest\n", "print(\"\\n🚀 Training MLX Random Forest...\")\n", "start_time = time.time()\n", "\n", "mlx_rf = MLXRandomForest(\n", "    n_estimators=50,  # Reduced for faster training\n", "    max_depth=5,\n", "    max_features='sqrt',\n", "    random_state=42\n", ")\n", "\n", "mlx_rf.fit(X_train_iris, y_train_iris)\n", "mlx_train_time = time.time() - start_time\n", "\n", "# Make predictions\n", "start_time = time.time()\n", "mlx_predictions = mlx_rf.predict(X_test_iris)\n", "mlx_pred_time = time.time() - start_time\n", "\n", "mlx_accuracy = mlx_rf.score(X_test_iris, y_test_iris)\n", "\n", "print(f\"✅ MLX Random Forest Results:\")\n", "print(f\"   Training time: {mlx_train_time:.3f} seconds\")\n", "print(f\"   Prediction time: {mlx_pred_time:.3f} seconds\")\n", "print(f\"   Accuracy: {mlx_accuracy:.4f}\")"]}, {"cell_type": "code", "execution_count": 8, "id": "13", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🔬 Training scikit-learn Random Forest...\n", "✅ Scikit-learn Random Forest Results:\n", "   Training time: 0.043 seconds\n", "   Prediction time: 0.015 seconds\n", "   Accuracy: 1.0000\n"]}], "source": ["# Train scikit-learn Random Forest for comparison\n", "print(\"\\n🔬 Training scikit-learn Random Forest...\")\n", "start_time = time.time()\n", "\n", "sklearn_rf = RandomForestClassifier(\n", "    n_estimators=50,\n", "    max_depth=5,\n", "    max_features='sqrt',\n", "    random_state=42,\n", "    n_jobs=-1\n", ")\n", "\n", "sklearn_rf.fit(np.array(X_train_iris).astype(np.float32), np.array(y_train_iris).astype(np.int32))\n", "sklearn_train_time = time.time() - start_time\n", "\n", "# Make predictions\n", "start_time = time.time()\n", "sklearn_predictions = sklearn_rf.predict(np.array(X_test_iris).astype(np.float32))\n", "sklearn_pred_time = time.time() - start_time\n", "\n", "sklearn_accuracy = accuracy_score(np.array(y_test_iris).astype(np.int32), sklearn_predictions)\n", "\n", "print(f\"✅ Scikit-learn Random Forest Results:\")\n", "print(f\"   Training time: {sklearn_train_time:.3f} seconds\")\n", "print(f\"   Prediction time: {sklearn_pred_time:.3f} seconds\")\n", "print(f\"   Accuracy: {sklearn_accuracy:.4f}\")"]}, {"cell_type": "markdown", "id": "14", "metadata": {}, "source": ["## Performance Visualization\n", "\n", "Let's create visualizations to compare the performance of MLX and scikit-learn Random Forests."]}, {"cell_type": "code", "execution_count": 9, "id": "15", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Create performance comparison plots\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "fig.suptitle('MLX vs Scikit-learn Random Forest Comparison', fontsize=16, fontweight='bold')\n", "\n", "# 1. <PERSON><PERSON><PERSON><PERSON> Comparison\n", "ax1 = axes[0, 0]\n", "methods = ['MLX RF', 'Sklearn RF']\n", "accuracies = [mlx_accuracy, sklearn_accuracy]\n", "colors = ['#FF6B6B', '#4ECDC4']\n", "\n", "bars1 = ax1.bar(methods, accuracies, color=colors, alpha=0.8, edgecolor='black', linewidth=1.5)\n", "ax1.set_ylabel('Accuracy', fontweight='bold')\n", "ax1.set_title('Accuracy Comparison', fontweight='bold')\n", "ax1.set_ylim(0, 1.1)\n", "ax1.grid(axis='y', alpha=0.3)\n", "\n", "# Add value labels on bars\n", "for bar, acc in zip(bars1, accuracies):\n", "    height = bar.get_height()\n", "    ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,\n", "             f'{acc:.4f}', ha='center', va='bottom', fontweight='bold')\n", "\n", "# 2. Training Time Comparison\n", "ax2 = axes[0, 1]\n", "train_times = [mlx_train_time, sklearn_train_time]\n", "bars2 = ax2.bar(methods, train_times, color=colors, alpha=0.8, edgecolor='black', linewidth=1.5)\n", "ax2.set_ylabel('Training Time (seconds)', fontweight='bold')\n", "ax2.set_title('Training Time Comparison', fontweight='bold')\n", "ax2.grid(axis='y', alpha=0.3)\n", "\n", "# Add value labels on bars\n", "for bar, time_val in zip(bars2, train_times):\n", "    height = bar.get_height()\n", "    ax2.text(bar.get_x() + bar.get_width()/2., height + max(train_times)*0.01,\n", "             f'{time_val:.3f}s', ha='center', va='bottom', fontweight='bold')\n", "\n", "# 3. Prediction Time Comparison\n", "ax3 = axes[1, 0]\n", "pred_times = [mlx_pred_time, sklearn_pred_time]\n", "bars3 = ax3.bar(methods, pred_times, color=colors, alpha=0.8, edgecolor='black', linewidth=1.5)\n", "ax3.set_ylabel('Prediction Time (seconds)', fontweight='bold')\n", "ax3.set_title('Prediction Time Comparison', fontweight='bold')\n", "ax3.grid(axis='y', alpha=0.3)\n", "\n", "# Add value labels on bars\n", "for bar, time_val in zip(bars3, pred_times):\n", "    height = bar.get_height()\n", "    ax3.text(bar.get_x() + bar.get_width()/2., height + max(pred_times)*0.01,\n", "             f'{time_val:.4f}s', ha='center', va='bottom', fontweight='bold')\n", "\n", "# 4. Prediction Accuracy by Class\n", "ax4 = axes[1, 1]\n", "y_test_np = np.array(y_test_iris)\n", "mlx_pred_np = np.array(mlx_predictions)\n", "\n", "# Calculate per-class accuracy\n", "class_accuracies_mlx = []\n", "class_accuracies_sklearn = []\n", "\n", "for class_idx in range(len(iris.target_names)):\n", "    class_mask = y_test_np == class_idx\n", "    if np.sum(class_mask) > 0:\n", "        mlx_class_acc = np.mean(mlx_pred_np[class_mask] == y_test_np[class_mask])\n", "        sklearn_class_acc = np.mean(sklearn_predictions[class_mask] == y_test_np[class_mask])\n", "        class_accuracies_mlx.append(mlx_class_acc)\n", "        class_accuracies_sklearn.append(sklearn_class_acc)\n", "    else:\n", "        class_accuracies_mlx.append(0)\n", "        class_accuracies_sklearn.append(0)\n", "\n", "x_pos = np.arange(len(iris.target_names))\n", "width = 0.35\n", "\n", "bars4a = ax4.bar(x_pos - width/2, class_accuracies_mlx, width, \n", "                 label='MLX RF', color=colors[0], alpha=0.8, edgecolor='black', linewidth=1)\n", "bars4b = ax4.bar(x_pos + width/2, class_accuracies_sklearn, width,\n", "                 label='Sklearn RF', color=colors[1], alpha=0.8, edgecolor='black', linewidth=1)\n", "\n", "ax4.set_ylabel('Accuracy', fontweight='bold')\n", "ax4.set_title('Per-Class Accuracy Comparison', fontweight='bold')\n", "ax4.set_xticks(x_pos)\n", "ax4.set_xticklabels(iris.target_names, rotation=45)\n", "ax4.legend()\n", "ax4.grid(axis='y', alpha=0.3)\n", "ax4.set_ylim(0, 1.1)\n", "\n", "# Add value labels on bars\n", "for bars, accs in [(bars4a, class_accuracies_mlx), (bars4b, class_accuracies_sklearn)]:\n", "    for bar, acc in zip(bars, accs):\n", "        height = bar.get_height()\n", "        ax4.text(bar.get_x() + bar.get_width()/2., height + 0.01,\n", "                 f'{acc:.3f}', ha='center', va='bottom', fontsize=9, fontweight='bold')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "16", "metadata": {}, "source": ["## Parameter Sensitivity Analysis\n", "\n", "Let's test how different parameters affect the performance of our MLX Random Forest."]}, {"cell_type": "code", "execution_count": 10, "id": "17", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 Testing Different Numbers of Estimators\n", "==================================================\n", "Testing with 10 estimators...\n", "   Accuracy: 1.0000, Time: 0.153s\n", "Testing with 25 estimators...\n", "   Accuracy: 1.0000, Time: 0.344s\n", "Testing with 50 estimators...\n", "   Accuracy: 1.0000, Time: 0.554s\n", "Testing with 75 estimators...\n", "   Accuracy: 1.0000, Time: 0.875s\n", "Testing with 100 estimators...\n", "   Accuracy: 1.0000, Time: 1.139s\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Test different numbers of estimators\n", "print(\"🔍 Testing Different Numbers of Estimators\")\n", "print(\"=\" * 50)\n", "\n", "n_estimators_list = [10, 25, 50, 75, 100]\n", "accuracies_by_estimators = []\n", "times_by_estimators = []\n", "\n", "for n_est in n_estimators_list:\n", "    print(f\"Testing with {n_est} estimators...\")\n", "    \n", "    start_time = time.time()\n", "    rf_test = MLXRandomForest(\n", "        n_estimators=n_est,\n", "        max_depth=5,\n", "        max_features='sqrt',\n", "        random_state=42\n", "    )\n", "    \n", "    rf_test.fit(X_train_iris, y_train_iris)\n", "    train_time = time.time() - start_time\n", "    \n", "    accuracy = rf_test.score(X_test_iris, y_test_iris)\n", "    \n", "    accuracies_by_estimators.append(accuracy)\n", "    times_by_estimators.append(train_time)\n", "    \n", "    print(f\"   Accuracy: {accuracy:.4f}, Time: {train_time:.3f}s\")\n", "\n", "# Plot results\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))\n", "\n", "# Accuracy vs Number of Estimators\n", "ax1.plot(n_estimators_list, accuracies_by_estimators, 'o-', linewidth=2, markersize=8, color='#FF6B6B')\n", "ax1.set_xlabel('Number of Estimators', fontweight='bold')\n", "ax1.set_ylabel('Accuracy', fontweight='bold')\n", "ax1.set_title('Accuracy vs Number of Estimators', fontweight='bold')\n", "ax1.grid(True, alpha=0.3)\n", "ax1.set_ylim(0.8, 1.05)\n", "\n", "# Add value labels\n", "for x, y in zip(n_estimators_list, accuracies_by_estimators):\n", "    ax1.annotate(f'{y:.3f}', (x, y), textcoords=\"offset points\", xytext=(0,10), ha='center', fontweight='bold')\n", "\n", "# Training Time vs Number of Estimators\n", "ax2.plot(n_estimators_list, times_by_estimators, 's-', linewidth=2, markersize=8, color='#4ECDC4')\n", "ax2.set_xlabel('Number of Estimators', fontweight='bold')\n", "ax2.set_ylabel('Training Time (seconds)', fontweight='bold')\n", "ax2.set_title('Training Time vs Number of Estimators', fontweight='bold')\n", "ax2.grid(True, alpha=0.3)\n", "\n", "# Add value labels\n", "for x, y in zip(n_estimators_list, times_by_estimators):\n", "    ax2.annotate(f'{y:.2f}s', (x, y), textcoords=\"offset points\", xytext=(0,10), ha='center', fontweight='bold')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "18", "metadata": {}, "source": ["## Summary and Conclusions\n", "\n", "Let's summarize our MLX Random Forest implementation and its performance."]}, {"cell_type": "code", "execution_count": 11, "id": "19", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🎯 MLX Random Forest Performance Summary\n", "==================================================\n", "📊 Dataset: Iris (150 samples, 4 features, 3 classes)\n", "🌳 Model: Random Forest with 50 trees, max_depth=5\n", "\n", "🚀 MLX Random Forest:\n", "   ✅ Accuracy: 1.0000 (100.00%)\n", "   ⏱️  Training Time: 0.851 seconds\n", "   ⚡ Prediction Time: 1.1587 seconds\n", "\n", "🔬 Scikit-learn Random Forest:\n", "   ✅ Accuracy: 1.0000 (100.00%)\n", "   ⏱️  Training Time: 0.043 seconds\n", "   ⚡ Prediction Time: 0.0145 seconds\n", "\n", "📈 Performance Comparison:\n", "   🎯 Accuracy Difference: 0.0000 (0.00%)\n", "   🐌 MLX Training Slowdown: 19.96x slower\n", "\n", "🌟 Key Features of MLX Random Forest:\n", "   ✅ Native Apple Silicon optimization with MLX\n", "   ✅ Bootstrap sampling with replacement\n", "   ✅ Random feature selection at each split\n", "   ✅ Gini impurity for splitting criteria\n", "   ✅ Majority voting for ensemble predictions\n", "   ✅ Scikit-learn compatible API\n", "\n", "💡 Best Use Cases:\n", "   • Apple Silicon optimized ML pipelines\n", "   • Memory-efficient ensemble learning\n", "   • Research and experimentation\n", "   • Educational purposes\n", "\n", "🔧 Hardware Information:\n", "   🖥️  MLX Metal Available: True\n", "   💻 Device: Apple M3 Max\n", "   💾 Memory: 128.0 GB\n"]}], "source": ["# Performance Summary\n", "print(\"🎯 MLX Random Forest Performance Summary\")\n", "print(\"=\" * 50)\n", "print(f\"📊 Dataset: Iris (150 samples, 4 features, 3 classes)\")\n", "print(f\"🌳 Model: Random Forest with 50 trees, max_depth=5\")\n", "print()\n", "print(f\"🚀 MLX Random Forest:\")\n", "print(f\"   ✅ Accuracy: {mlx_accuracy:.4f} ({mlx_accuracy*100:.2f}%)\")\n", "print(f\"   ⏱️  Training Time: {mlx_train_time:.3f} seconds\")\n", "print(f\"   ⚡ Prediction Time: {mlx_pred_time:.4f} seconds\")\n", "print()\n", "print(f\"🔬 Scikit-learn Random Forest:\")\n", "print(f\"   ✅ Accuracy: {sklearn_accuracy:.4f} ({sklearn_accuracy*100:.2f}%)\")\n", "print(f\"   ⏱️  Training Time: {sklearn_train_time:.3f} seconds\")\n", "print(f\"   ⚡ Prediction Time: {sklearn_pred_time:.4f} seconds\")\n", "print()\n", "print(f\"📈 Performance Comparison:\")\n", "accuracy_diff = abs(mlx_accuracy - sklearn_accuracy)\n", "print(f\"   🎯 Accuracy Difference: {accuracy_diff:.4f} ({accuracy_diff*100:.2f}%)\")\n", "\n", "if mlx_train_time > 0 and sklearn_train_time > 0:\n", "    if mlx_train_time < sklearn_train_time:\n", "        speedup = sklearn_train_time / mlx_train_time\n", "        print(f\"   🚀 MLX Training Speedup: {speedup:.2f}x faster\")\n", "    else:\n", "        slowdown = mlx_train_time / sklearn_train_time\n", "        print(f\"   🐌 MLX Training Slowdown: {slowdown:.2f}x slower\")\n", "\n", "print()\n", "print(f\"🌟 Key Features of MLX Random Forest:\")\n", "print(f\"   ✅ Native Apple Silicon optimization with MLX\")\n", "print(f\"   ✅ Bootstrap sampling with replacement\")\n", "print(f\"   ✅ Random feature selection at each split\")\n", "print(f\"   ✅ Gini impurity for splitting criteria\")\n", "print(f\"   ✅ Majority voting for ensemble predictions\")\n", "print(f\"   ✅ Scikit-learn compatible API\")\n", "print()\n", "print(f\"💡 Best Use Cases:\")\n", "print(f\"   • Apple Silicon optimized ML pipelines\")\n", "print(f\"   • Memory-efficient ensemble learning\")\n", "print(f\"   • Research and experimentation\")\n", "print(f\"   • Educational purposes\")\n", "print()\n", "print(f\"🔧 Hardware Information:\")\n", "print(f\"   🖥️  MLX Metal Available: {mlx.metal.is_available()}\")\n", "if mlx.metal.is_available():\n", "    device_info = mlx.metal.device_info()\n", "    print(f\"   💻 Device: {device_info.get('device_name', 'Unknown')}\")\n", "    print(f\"   💾 Memory: {device_info.get('memory_size', 0) / (1024**3):.1f} GB\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}