"""
MLX Random Forest Implementation

A Random Forest classifier implementation using Apple's MLX framework
for efficient computation on Apple Silicon.
"""

import mlx.core as mlx
import numpy as np
from typing import Optional, List, Tuple
from dataclasses import dataclass


@dataclass
class TreeNode:
    """Node structure for decision tree"""
    is_leaf: bool = False
    feature_idx: Optional[int] = None
    threshold: Optional[float] = None
    prediction: Optional[int] = None
    left: Optional['TreeNode'] = None
    right: Optional['TreeNode'] = None
    samples: int = 0
    
    def predict_sample(self, x: mlx.array) -> int:
        """Predict a single sample"""
        if self.is_leaf:
            return self.prediction
        
        if x[self.feature_idx] <= self.threshold:
            return self.left.predict_sample(x)
        else:
            return self.right.predict_sample(x)


class MLXDecisionTree:
    """Decision Tree implementation using MLX for efficient computation"""
    
    def __init__(self, max_depth: int = 10, min_samples_split: int = 2, 
                 min_samples_leaf: int = 1, max_features: Optional[int] = None):
        self.max_depth = max_depth
        self.min_samples_split = min_samples_split
        self.min_samples_leaf = min_samples_leaf
        self.max_features = max_features
        self.root = None
        
    def _gini_impurity(self, y: mlx.array) -> float:
        """Calculate Gini impurity using MLX operations"""
        if len(y) == 0:
            return 0.0

        # Get unique classes and their counts using numpy
        y_np = np.array(y)
        classes, counts = np.unique(y_np, return_counts=True)
        n_samples = len(y)

        gini = 1.0
        for count in counts:
            p = count / n_samples
            gini -= p * p

        return gini
    
    def _best_split(self, X: mlx.array, y: mlx.array, feature_indices: mlx.array) -> Tuple[int, float, float]:
        """Find the best split using MLX operations"""
        best_gini = float('inf')
        best_feature = None
        best_threshold = None
        
        n_samples = len(y)
        
        for feature_idx in feature_indices:
            feature_idx = int(feature_idx.item())
            feature_values = X[:, feature_idx]
            
            # Get unique values as potential thresholds
            feature_values_np = np.array(feature_values)
            unique_values = np.unique(feature_values_np)
            unique_values = mlx.array(unique_values)
            
            for i in range(len(unique_values) - 1):
                threshold = (unique_values[i] + unique_values[i + 1]) / 2
                threshold = threshold.item()
                
                # Split data using numpy for boolean indexing
                feature_values_np = np.array(feature_values)
                y_np = np.array(y)
                left_mask = feature_values_np <= threshold
                right_mask = ~left_mask

                left_y = mlx.array(y_np[left_mask])
                right_y = mlx.array(y_np[right_mask])
                
                if len(left_y) < self.min_samples_leaf or len(right_y) < self.min_samples_leaf:
                    continue
                
                # Calculate weighted Gini impurity
                left_gini = self._gini_impurity(left_y)
                right_gini = self._gini_impurity(right_y)
                
                weighted_gini = (len(left_y) * left_gini + len(right_y) * right_gini) / n_samples
                
                if weighted_gini < best_gini:
                    best_gini = weighted_gini
                    best_feature = feature_idx
                    best_threshold = threshold
        
        return best_feature, best_threshold, best_gini
    
    def _build_tree(self, X: mlx.array, y: mlx.array, depth: int = 0) -> TreeNode:
        """Recursively build the decision tree"""
        n_samples, n_features = X.shape
        
        # Determine number of features to consider
        if self.max_features is None:
            max_features = n_features
        else:
            max_features = min(self.max_features, n_features)
        
        # Randomly select features to consider
        all_features = mlx.arange(n_features)
        shuffled_features = mlx.random.permutation(all_features)
        feature_indices = shuffled_features[:max_features]
        
        # Check stopping criteria
        y_np = np.array(y)
        unique_classes = np.unique(y_np)

        if (len(unique_classes) == 1 or
            depth >= self.max_depth or
            n_samples < self.min_samples_split):

            # Create leaf node with majority class
            classes, counts = np.unique(y_np, return_counts=True)
            majority_class = classes[np.argmax(counts)]
            
            return TreeNode(
                is_leaf=True,
                prediction=int(majority_class),
                samples=n_samples
            )
        
        # Find best split
        best_feature, best_threshold, best_gini = self._best_split(X, y, feature_indices)
        
        if best_feature is None:
            # No valid split found, create leaf
            y_np = np.array(y)
            classes, counts = np.unique(y_np, return_counts=True)
            majority_class = classes[np.argmax(counts)]

            return TreeNode(
                is_leaf=True,
                prediction=int(majority_class),
                samples=n_samples
            )
        
        # Split data using numpy for boolean indexing
        X_np = np.array(X)
        y_np = np.array(y)
        left_mask = X_np[:, best_feature] <= best_threshold
        right_mask = ~left_mask

        # Recursively build subtrees
        left_child = self._build_tree(mlx.array(X_np[left_mask]), mlx.array(y_np[left_mask]), depth + 1)
        right_child = self._build_tree(mlx.array(X_np[right_mask]), mlx.array(y_np[right_mask]), depth + 1)
        
        return TreeNode(
            is_leaf=False,
            feature_idx=best_feature,
            threshold=best_threshold,
            left=left_child,
            right=right_child,
            samples=n_samples
        )
    
    def fit(self, X: mlx.array, y: mlx.array):
        """Train the decision tree"""
        self.root = self._build_tree(X, y)
        return self
    
    def predict(self, X: mlx.array) -> mlx.array:
        """Make predictions for multiple samples"""
        predictions = []
        for i in range(len(X)):
            pred = self.root.predict_sample(X[i])
            predictions.append(pred)
        return mlx.array(predictions)


class MLXRandomForest:
    """Random Forest implementation using MLX for efficient computation"""
    
    def __init__(self, n_estimators: int = 100, max_depth: int = 10, 
                 min_samples_split: int = 2, min_samples_leaf: int = 1,
                 max_features: str = 'sqrt', bootstrap: bool = True,
                 random_state: Optional[int] = None):
        self.n_estimators = n_estimators
        self.max_depth = max_depth
        self.min_samples_split = min_samples_split
        self.min_samples_leaf = min_samples_leaf
        self.max_features = max_features
        self.bootstrap = bootstrap
        self.random_state = random_state
        self.trees = []
        
        if random_state is not None:
            mlx.random.seed(random_state)
    
    def _get_max_features(self, n_features: int) -> int:
        """Calculate number of features to consider at each split"""
        if self.max_features == 'sqrt':
            return int(np.sqrt(n_features))
        elif self.max_features == 'log2':
            return int(np.log2(n_features))
        elif isinstance(self.max_features, int):
            return min(self.max_features, n_features)
        elif isinstance(self.max_features, float):
            return int(self.max_features * n_features)
        else:
            return n_features
    
    def _bootstrap_sample(self, X: mlx.array, y: mlx.array) -> Tuple[mlx.array, mlx.array]:
        """Create bootstrap sample of the data"""
        n_samples = len(X)

        if self.bootstrap:
            # Sample with replacement using randint
            indices = mlx.random.randint(0, n_samples, (n_samples,))
            return X[indices], y[indices]
        else:
            return X, y
    
    def fit(self, X: mlx.array, y: mlx.array):
        """Train the random forest"""
        n_samples, n_features = X.shape
        max_features = self._get_max_features(n_features)
        
        self.trees = []
        
        for i in range(self.n_estimators):
            # Create bootstrap sample
            X_bootstrap, y_bootstrap = self._bootstrap_sample(X, y)
            
            # Create and train tree
            tree = MLXDecisionTree(
                max_depth=self.max_depth,
                min_samples_split=self.min_samples_split,
                min_samples_leaf=self.min_samples_leaf,
                max_features=max_features
            )
            
            tree.fit(X_bootstrap, y_bootstrap)
            self.trees.append(tree)
        
        return self
    
    def predict(self, X: mlx.array) -> mlx.array:
        """Make predictions using majority voting"""
        n_samples = len(X)
        all_predictions = []
        
        # Get predictions from all trees
        for tree in self.trees:
            tree_predictions = tree.predict(X)
            all_predictions.append(tree_predictions)
        
        # Stack predictions
        all_predictions = mlx.stack(all_predictions, axis=1)  # shape: (n_samples, n_estimators)
        
        # Majority voting
        final_predictions = []
        for i in range(n_samples):
            sample_predictions = np.array(all_predictions[i])
            unique_preds, counts = np.unique(sample_predictions, return_counts=True)
            majority_pred = unique_preds[np.argmax(counts)]
            final_predictions.append(int(majority_pred))

        return mlx.array(final_predictions)
    
    def score(self, X: mlx.array, y: mlx.array) -> float:
        """Calculate accuracy score"""
        predictions = self.predict(X)
        accuracy = mlx.mean(predictions == y)
        return accuracy.item()


if __name__ == "__main__":
    # Quick test
    from sklearn.datasets import make_classification
    from sklearn.model_selection import train_test_split
    
    # Generate test data
    X_np, y_np = make_classification(n_samples=500, n_features=10, n_classes=2, random_state=42)
    X = mlx.array(X_np.astype(np.float32))
    y = mlx.array(y_np.astype(np.int32))
    
    # Split data
    X_train_np, X_test_np, y_train_np, y_test_np = train_test_split(X_np, y_np, test_size=0.2, random_state=42)
    X_train = mlx.array(X_train_np.astype(np.float32))
    X_test = mlx.array(X_test_np.astype(np.float32))
    y_train = mlx.array(y_train_np.astype(np.int32))
    y_test = mlx.array(y_test_np.astype(np.int32))
    
    # Train and test
    rf = MLXRandomForest(n_estimators=10, max_depth=5, random_state=42)
    rf.fit(X_train, y_train)
    accuracy = rf.score(X_test, y_test)
    
    print(f"MLX Random Forest Accuracy: {accuracy:.4f}")
    print(f"MLX Metal available: {mlx.metal.is_available()}")
