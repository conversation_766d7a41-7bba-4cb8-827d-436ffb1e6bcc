{"cells": [{"cell_type": "code", "execution_count": 2, "id": "704251b6", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 3, "id": "ca3259d8", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>sentiment</th>\n", "      <th>review</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>5814_8</td>\n", "      <td>1</td>\n", "      <td>With all this stuff going down at the moment w...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2381_9</td>\n", "      <td>1</td>\n", "      <td>\\The Classic War of the Worlds\\\" by <PERSON>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>7759_3</td>\n", "      <td>0</td>\n", "      <td>The film starts with a manager (<PERSON>)...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3630_4</td>\n", "      <td>0</td>\n", "      <td>It must be assumed that those who praised this...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>9495_8</td>\n", "      <td>1</td>\n", "      <td>Superbly trashy and wondrously unpretentious 8...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       id  sentiment                                             review\n", "0  5814_8          1  With all this stuff going down at the moment w...\n", "1  2381_9          1  \\The Classic War of the Worlds\\\" by <PERSON>...\n", "2  7759_3          0  The film starts with a manager (<PERSON>)...\n", "3  3630_4          0  It must be assumed that those who praised this...\n", "4  9495_8          1  Superbly trashy and wondrously unpretentious 8..."]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["train = pd.read_csv(\"../.inputs/word2vec/labeledTrainData.tsv\", delimiter=\"\\t\")\n", "test = pd.read_csv(\"../.inputs/word2vec/testData.tsv\", delimiter=\"\\t\")\n", "train.head()"]}, {"cell_type": "code", "execution_count": 4, "id": "5583a86d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>review</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>12311_10</td>\n", "      <td>Naturally in a film who's main themes are of m...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>8348_2</td>\n", "      <td>This movie is a disaster within a disaster fil...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>5828_4</td>\n", "      <td>All in all, this is a movie for kids. We saw i...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>7186_2</td>\n", "      <td>A<PERSON>id of the Dark left me with the impression...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>12128_7</td>\n", "      <td>A very accurate depiction of small time mob li...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         id                                             review\n", "0  12311_10  Naturally in a film who's main themes are of m...\n", "1    8348_2  This movie is a disaster within a disaster fil...\n", "2    5828_4  All in all, this is a movie for kids. We saw i...\n", "3    7186_2  A<PERSON><PERSON> of the <PERSON> left me with the impression...\n", "4   12128_7  A very accurate depiction of small time mob li..."]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["test.head()"]}, {"cell_type": "code", "execution_count": 5, "id": "2c033953", "metadata": {}, "outputs": [{"data": {"text/plain": ["((25000, 3), (25000, 2))"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["train.shape, test.shape"]}, {"cell_type": "code", "execution_count": 6, "id": "9fc1df30", "metadata": {}, "outputs": [], "source": ["import re\n", "\n", "def review_preprocess(\n", "        review: str,\n", ") -> str:\n", "    ## Remove HTML tags\n", "    review = re.sub(r\"<br />\", \" \", review)\n", "    review = re.sub(r\"[^a-zA-Z0-9]\", \" \", review)\n", "    review = review.lower()\n", "    return review"]}, {"cell_type": "code", "execution_count": 9, "id": "f7ad3630", "metadata": {}, "outputs": [], "source": ["iis_ys = train[\"sentiment\"].values\n", "iis_Xs = train[\"review\"].apply(review_preprocess).values\n", "oos_Xs = test[\"review\"].apply(review_preprocess).values"]}, {"cell_type": "code", "execution_count": 10, "id": "ad98beeb", "metadata": {}, "outputs": [{"data": {"text/plain": ["((20000,), (5000,), (20000,), (5000,))"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["from sklearn.model_selection import train_test_split\n", "\n", "X_train, X_test, y_train, y_test = train_test_split(iis_Xs, iis_ys, test_size=0.2, random_state=42)\n", "X_train.shape, X_test.shape, y_train.shape, y_test.shape"]}, {"cell_type": "code", "execution_count": 11, "id": "89f6138e", "metadata": {}, "outputs": [], "source": ["from sklearn.feature_extraction.text import CountVectorizer\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "\n", "vectorizer = CountVectorizer()\n", "X_train_count = vectorizer.fit_transform(X_train)\n", "X_test_count = vectorizer.transform(X_test)\n", "\n", "tfidf_vector = TfidfVectorizer()\n", "X_train_tfidf = tfidf_vector.fit_transform(X_train)\n", "X_test_tfidf = tfidf_vector.transform(X_test)"]}, {"cell_type": "code", "execution_count": 12, "id": "7b24867b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.8476\n", "[[2177  304]\n", " [ 458 2061]]\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.83      0.88      0.85      2481\n", "           1       0.87      0.82      0.84      2519\n", "\n", "    accuracy                           0.85      5000\n", "   macro avg       0.85      0.85      0.85      5000\n", "weighted avg       0.85      0.85      0.85      5000\n", "\n"]}], "source": ["from sklearn.naive_bayes import MultinomialNB\n", "from sklearn.metrics import accuracy_score, confusion_matrix, classification_report\n", "\n", "model = MultinomialNB()\n", "model.fit(X_train_count, y_train)\n", "y_pred = model.predict(X_test_count)\n", "print(accuracy_score(y_test, y_pred))\n", "print(confusion_matrix(y_test, y_pred))\n", "print(classification_report(y_test, y_pred))"]}, {"cell_type": "code", "execution_count": 13, "id": "204bd3ce", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.8598\n", "[[2186  295]\n", " [ 406 2113]]\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.84      0.88      0.86      2481\n", "           1       0.88      0.84      0.86      2519\n", "\n", "    accuracy                           0.86      5000\n", "   macro avg       0.86      0.86      0.86      5000\n", "weighted avg       0.86      0.86      0.86      5000\n", "\n"]}], "source": ["model.fit(X_train_tfidf, y_train)\n", "y_pred = model.predict(X_test_tfidf)\n", "print(accuracy_score(y_test, y_pred))\n", "print(confusion_matrix(y_test, y_pred))\n", "print(classification_report(y_test, y_pred))"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}