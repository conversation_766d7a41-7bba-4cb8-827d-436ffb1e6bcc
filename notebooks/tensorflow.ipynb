import tensorflow as tf
import numpy as np
from tensorflow import keras

print(tf.__version__)
print(tf.config.list_physical_devices('GPU'))
print(keras.__version__)

from tensorflow.python.client import device_lib
print(device_lib.list_local_devices())

a = tf.constant(3)
print(f"your tensor rank is {tf.rank(a)}")
a

b = tf.constant([[[1, 2], [0, 0]]])
print(f"your tensor rank is {tf.rank(b)}")
b

c = tf.constant(np.random.randn(2, 2))
print(f"your tensor rank is {tf.rank(c)}")
c

x = tf.Variable(3, name='x', dtype=tf.float32)
x

x = tf.Variable(a)
x

x.assign(0)

b = tf.constant([[1, 2], [3, 4]])
b

b = tf.cast(b, dtype=tf.float64)
b

x = tf.Variable(2)
y = tf.Variable(7)
f = x ** 2 * y + y + 2
f

tensor_test = tf.constant([[1, 2], [3, 4]])
tensor_test

tensor_test.shape

tensor_test.numpy()

tensor_test = tensor_test + 6
tensor_test

tensor_test = tf.constant([[1, 2], [3, 4]])
tensor_test = tf.square(tensor_test)
tensor_test

tensor_test = tf.constant([[1, 4], [9, 16]], dtype=tf.float32)
tensor_test = tf.sqrt(tensor_test)
tensor_test

tensor_test = tf.matmul(tensor_test, tensor_test)
tensor_test

tensor_test = tf.constant([[1, 2], [3, 4]])
np.sqrt(tensor_test)

x = tf.Variable([2.])

with tf.GradientTape(persistent=False, watch_accessed_variables=True) as tape:
    f = x ** 2

print("The gradient of df/dx where f(x) = x^2 is\n", tape.gradient(f, x))

x = tf.constant([2.])

with tf.GradientTape(persistent=False, watch_accessed_variables=True) as tape:
    f = x ** 2

print("The gradient of df/dx where f(x) = x^2 is\n", tape.gradient(f, x))

x = tf.Variable([2])  ## auto diff only applies to floats

with tf.GradientTape(persistent=False, watch_accessed_variables=True) as tape:
    f = x ** 2

print("The gradient of df/dx where f(x) = x^2 is\n", tape.gradient(f, x))

x = tf.Variable(2.)  ## auto diff only applies to floats
print(x)

with tf.GradientTape(persistent=False, watch_accessed_variables=True) as tape:
    f = x ** 2

print("The gradient of df/dx where f(x) = x^2 is\n", tape.gradient(f, x))

x = tf.Variable([2.])
y = tf.Variable([3.])

with tf.GradientTape(persistent=True, watch_accessed_variables=True) as tape:
    f = x ** 2
    h = y ** 3

print("The gradient of df/dx where f(x) = x^2 is\n", tape.gradient(f, x))
print("The gradient of dh/dy where h(y) = y^3 is\n", tape.gradient(h, y))

print("The gradient of df/dy is\n", tape.gradient(f, y))

x = tf.Variable([2.])
y = tf.Variable([3.])

with tf.GradientTape(persistent=True, watch_accessed_variables=False) as tape:
    f = x ** 2
    h = y ** 3

print("The gradient of df/dx where f(x) = x^2 is\n", tape.gradient(f, x))
print("The gradient of dh/dy where h(y) = y^3 is\n", tape.gradient(h, y))

x = tf.Variable([2.])
y = tf.Variable([3.])

with tf.GradientTape(persistent=True, watch_accessed_variables=False) as tape:
    ##
    tape.watch(x)
    f = x ** 2
    h = y ** 3

print("The gradient of df/dx where f(x) = x^2 is\n", tape.gradient(f, x))
print("The gradient of dh/dy where h(y) = y^3 is\n", tape.gradient(h, y))

x = tf.Variable([2.])
y = tf.Variable([3.])

with tf.GradientTape(persistent=True, watch_accessed_variables=False) as tape:
    ##
    tape.watch(y)
    f = x ** 2
    h = y ** 3

print("The gradient of df/dx where f(x) = x^2 is\n", tape.gradient(f, x))
print("The gradient of dh/dy where h(y) = y^3 is\n", tape.gradient(h, y))

x = tf.Variable([2.])
y = tf.Variable([3.])

with tf.GradientTape(persistent=True, watch_accessed_variables=False) as tape:
    ##
    tape.watch([x, y])
    f = x ** 2
    h = y ** 3

print("The gradient of df/dx where f(x) = x^2 is\n", tape.gradient(f, x))
print("The gradient of dh/dy where h(y) = y^3 is\n", tape.gradient(h, y))

x = tf.Variable([2.])
y = tf.Variable([3.])

with tf.GradientTape(persistent=True, watch_accessed_variables=True) as tape:
    ##
    f = x ** 2 * y + y + 2

## df / fx = 2xy
## df / fy = x^2 + 1
print("The gradient of df/dx and df/dy where f(x) = x^2 * y + y + 2 is\n", tape.gradient(f, [x, y]))

x = tf.Variable([2.])
y = tf.Variable([3.])

with tf.GradientTape(persistent=True, watch_accessed_variables=True) as tape:
    ##
    f = x ** 2 * y + y + 2

## df / fx = 2xy
## df / fy = x^2 + 1
print("The gradient df/dx where f(x, y) = x^2 * y + y + 2 is\n", tape.gradient(f, x))
print("The gradient df/dy where f(x, y) = x^2 * y + y + 2 is\n", tape.gradient(f, y))

import matplotlib.pyplot as plt
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error as mse
from sklearn.metrics import r2_score
from sklearn.preprocessing import StandardScaler, OneHotEncoder
from sklearn.model_selection import train_test_split

def f(x):
    """
    input: x
    output: y = 0.7x - 42
    """
    return 0.7 * x - 42

N = 100
noise_level = 0.8

iis_Xs = np.linspace(165, 190, N)
np.random.shuffle(iis_Xs)
iis_ys = f(iis_Xs) + noise_level * np.random.randn(N)

learning_rate = .1
training_epochs = 300
display_step = 50

plt.scatter(iis_Xs, iis_ys, label="Original data")
plt.show()

def linear_regression_forward(x, w, b):
    ##
    return w * x + b


def loss_function(y, yh):
    ##
    return tf.reduce_mean(tf.square(y - yh))


def data_loader(Xs, ys, batch_size, shaffle=True):
    ##
    indices = list(range(len(Xs)))

    ##
    if shaffle:
        np.random.shuffle(indices)

    ##
    for i in range(0, len(indices), batch_size):
        idx = np.array(indices[i : min(i + batch_size, len(Xs))])
        yield Xs[idx], ys[idx]

w = tf.Variable(np.random.randn(), name="weight")
b = tf.Variable(np.random.randn(), name="bias")
batch_size = 32

iis_Xs = np.reshape(iis_Xs, (-1, 1))
iis_ys = np.reshape(iis_ys, (-1, 1))

##
iis_Xs, oos_Xs, iis_ys, oos_ys = train_test_split(iis_Xs, iis_ys, test_size=0.2, random_state=1)
iis_Xs.shape, iis_ys.shape, oos_Xs.shape, oos_ys.shape

w.numpy(), b.numpy()

Xs_scalar = StandardScaler()
ys_scalar = StandardScaler()

iis_Xs = Xs_scalar.fit_transform(iis_Xs)
iis_ys = ys_scalar.fit_transform(iis_ys)

plt.scatter(iis_Xs, iis_ys, label="Original data")
plt.plot(iis_Xs, linear_regression_forward(iis_Xs, w, b), c="y", label="Fitted line")
plt.legend()

for epoch in range(training_epochs):
    ##
    for Xs, ys in data_loader(iis_Xs, iis_ys, batch_size):
        with tf.GradientTape(persistent=True, watch_accessed_variables=True) as tape:
            ##
            loss = loss_function(ys, linear_regression_forward(Xs, w, b))
        ##
        dw, db = tape.gradient(loss, [w, b])
        w.assign_sub(learning_rate * dw)
        b.assign_sub(learning_rate * db)
    ##
    train_loss = loss_function(iis_ys, linear_regression_forward(iis_Xs, w, b))
    print(f"Epoch {epoch + 1}: train loss = {tf.reduce_mean(train_loss)}")

w, b

processed_x = Xs_scalar.transform(np.reshape(oos_Xs, (-1, 1)))
predicted_y = ys_scalar.inverse_transform(linear_regression_forward(processed_x, w, b))

r2_score(oos_ys, predicted_y)

plt.scatter(iis_Xs, iis_ys, label="Original data")
plt.plot(iis_Xs, linear_regression_forward(iis_Xs, w, b), c="y", label="Fitted line")
plt.legend()
plt.show()

w, b

print(w * ys_scalar.scale_ / Xs_scalar.scale_)
print(- w * ys_scalar.scale_ / Xs_scalar.scale_ * Xs_scalar.mean_ + ys_scalar.scale_ * b + ys_scalar.mean_)

model = tf.keras.Sequential([
    tf.keras.layers.Input(shape=(1,)),
    tf.keras.layers.Dense(1),
])
model.summary()

model.compile(optimizer="sgd", loss="mse", metrics=["mse"])
hisory = model.fit(iis_Xs, iis_ys, epochs=300, steps_per_epoch=8)

processed_x = Xs_scalar.transform(np.reshape(oos_Xs, (-1, 1)))
predicted_y = ys_scalar.inverse_transform(model(processed_x.astype(np.float32)))

r2_score(oos_ys, predicted_y)

model.weights

model.get_weights()