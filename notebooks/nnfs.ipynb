{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[4.8, 1.21, 2.385]\n"]}], "source": ["inputs = [1.0, 2.0, 3.0, 2.5]\n", "\n", "weights1 = [0.2, 0.8, -0.5, 1.0]\n", "weights2 = [0.5, -0.91, 0.26, -0.5]\n", "weights3 = [-0.26, -0.27, 0.17, 0.87]\n", "\n", "bias1 = 2.0\n", "bias2 = 3.0\n", "bias3 = 0.5\n", "\n", "outputs = [\n", "    inputs[0] * weights1[0] + inputs[1] * weights1[1] + inputs[2] * weights1[2] + inputs[3] * weights1[3] + bias1,\n", "    inputs[0] * weights2[0] + inputs[1] * weights2[1] + inputs[2] * weights2[2] + inputs[3] * weights2[3] + bias2,\n", "    inputs[0] * weights3[0] + inputs[1] * weights3[1] + inputs[2] * weights3[2] + inputs[3] * weights3[3] + bias3,\n", "]\n", "\n", "print(outputs)\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[4.8   1.21  2.385]\n"]}], "source": ["inputs = [1.0, 2.0, 3.0, 2.5]\n", "\n", "weights = [\n", "    [0.2, 0.8, -0.5, 1.0],\n", "    [0.5, -0.91, 0.26, -0.5],\n", "    [-0.26, -0.27, 0.17, 0.87]\n", "]\n", "\n", "biases = [2.0, 3.0, 0.5]\n", "\n", "outputs = np.dot(weights, inputs) + biases\n", "\n", "print(outputs)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[ 0.5031  -1.04185 -2.03875]\n", " [ 0.2434  -2.7332  -5.7633 ]\n", " [-0.99314  1.41254 -0.35655]]\n"]}], "source": ["import numpy as np\n", "\n", "inputs = [\n", "    [1.0, 2.0, 3.0, 2.5],\n", "    [2.0, 5.0, -1.0, 2.0],\n", "    [-1.5, 2.7, 3.3, -0.8]\n", "]\n", "\n", "weights = [\n", "    [0.2, 0.8, -0.5, 1.0],\n", "    [0.5, -0.91, 0.26, -0.5],\n", "    [-0.26, -0.27, 0.17, 0.87]\n", "]\n", "\n", "biases = [2.0, 3.0, 0.5]\n", "\n", "weights2 = [\n", "    [0.1, -0.14, 0.5],\n", "    [-0.5, 0.12, -0.33],\n", "    [-0.44, 0.73,-0.13]\n", "]\n", "\n", "biases2 = [-1, 2, -0.5]\n", "\n", "layer1_outputs = np.dot(inputs, np.array(weights).T) + biases\n", "layer2_outputs = np.dot(layer1_outputs, np.array(weights2).T) + biases2\n", "\n", "print(layer2_outputs)\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from nnfs.datasets import spiral_data"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import nnfs\n", "\n", "nnfs.init()\n", "\n", "import matplotlib.pyplot as plt\n", "X, y = spiral_data(samples=100, classes=3)\n", "plt.scatter(X[:, 0], X[:, 1])\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.scatter(X[:, 0], X[:, 1], c=y, cmap='brg')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[ 0.0000000e+00  0.0000000e+00  0.0000000e+00]\n", " [-1.1117104e-03 -5.1100750e-04 -1.1209981e-03]\n", " [ 2.9925766e-05 -2.6912661e-03 -1.4516510e-03]\n", " [ 8.9510187e-04 -4.3044225e-03 -1.6807980e-03]\n", " [-3.4989377e-03 -3.0720837e-03 -4.3300288e-03]]\n"]}], "source": ["class Layer_Dense:\n", "    def __init__(self, n_inputs, n_neurons):\n", "        self.weights = 0.10 * np.random.randn(n_inputs, n_neurons)\n", "        self.biases = np.zeros((1, n_neurons))\n", "\n", "    def forward(self, inputs):\n", "        self.output = np.dot(inputs, self.weights) + self.biases\n", "\n", "x, y = spiral_data(samples=100, classes=3)\n", "\n", "dense1 = Layer_Dense(2, 3)\n", "\n", "dense1.forward(x)\n", "\n", "print(dense1.output[:5])"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[-0.00110489, -0.016607  ,  0.00115148],\n", "       [-0.00379148, -0.01742356, -0.01303243]], dtype=float32)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["w = 0.01 * np.random.randn(2, 3)\n", "w"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([-3.1840387e-05, -2.3968275e-04, -6.9367066e-05], dtype=float32)"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["np.dot(x[1], w)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 2}