{"cells": [{"cell_type": "code", "execution_count": null, "id": "4871a625", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "\n", "from sklearn.model_selection import train_test_split"]}, {"cell_type": "code", "execution_count": 3, "id": "52442515", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 21613 entries, 0 to 21612\n", "Data columns (total 21 columns):\n", " #   Column         Non-Null Count  Dtype  \n", "---  ------         --------------  -----  \n", " 0   id             21613 non-null  int64  \n", " 1   date           21613 non-null  object \n", " 2   price          21613 non-null  float64\n", " 3   bedrooms       21613 non-null  int64  \n", " 4   bathrooms      21613 non-null  float64\n", " 5   sqft_living    21613 non-null  int64  \n", " 6   sqft_lot       21613 non-null  int64  \n", " 7   floors         21613 non-null  float64\n", " 8   waterfront     21613 non-null  int64  \n", " 9   view           21613 non-null  int64  \n", " 10  condition      21613 non-null  int64  \n", " 11  grade          21613 non-null  int64  \n", " 12  sqft_above     21613 non-null  int64  \n", " 13  sqft_basement  21613 non-null  int64  \n", " 14  yr_built       21613 non-null  int64  \n", " 15  yr_renovated   21613 non-null  int64  \n", " 16  zipcode        21613 non-null  int64  \n", " 17  lat            21613 non-null  float64\n", " 18  long           21613 non-null  float64\n", " 19  sqft_living15  21613 non-null  int64  \n", " 20  sqft_lot15     21613 non-null  int64  \n", "dtypes: float64(5), int64(15), object(1)\n", "memory usage: 3.5+ MB\n"]}], "source": ["kc_house_data = pd.read_csv(\"../.inputs/kc_house_data/kc_house_data.csv\")\n", "kc_house_data.info()"]}, {"cell_type": "code", "execution_count": 4, "id": "189ceddd", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>date</th>\n", "      <th>price</th>\n", "      <th>bedrooms</th>\n", "      <th>bathrooms</th>\n", "      <th>sqft_living</th>\n", "      <th>sqft_lot</th>\n", "      <th>floors</th>\n", "      <th>waterfront</th>\n", "      <th>view</th>\n", "      <th>condition</th>\n", "      <th>grade</th>\n", "      <th>sqft_above</th>\n", "      <th>sqft_basement</th>\n", "      <th>yr_built</th>\n", "      <th>yr_renovated</th>\n", "      <th>zipcode</th>\n", "      <th>lat</th>\n", "      <th>long</th>\n", "      <th>sqft_living15</th>\n", "      <th>sqft_lot15</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>7129300520</td>\n", "      <td>20141013T000000</td>\n", "      <td>221900.0</td>\n", "      <td>3</td>\n", "      <td>1.00</td>\n", "      <td>1180</td>\n", "      <td>5650</td>\n", "      <td>1.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>7</td>\n", "      <td>1180</td>\n", "      <td>0</td>\n", "      <td>1955</td>\n", "      <td>0</td>\n", "      <td>98178</td>\n", "      <td>47.5112</td>\n", "      <td>-122.257</td>\n", "      <td>1340</td>\n", "      <td>5650</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>6414100192</td>\n", "      <td>20141209T000000</td>\n", "      <td>538000.0</td>\n", "      <td>3</td>\n", "      <td>2.25</td>\n", "      <td>2570</td>\n", "      <td>7242</td>\n", "      <td>2.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>7</td>\n", "      <td>2170</td>\n", "      <td>400</td>\n", "      <td>1951</td>\n", "      <td>1991</td>\n", "      <td>98125</td>\n", "      <td>47.7210</td>\n", "      <td>-122.319</td>\n", "      <td>1690</td>\n", "      <td>7639</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>5631500400</td>\n", "      <td>20150225T000000</td>\n", "      <td>180000.0</td>\n", "      <td>2</td>\n", "      <td>1.00</td>\n", "      <td>770</td>\n", "      <td>10000</td>\n", "      <td>1.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>6</td>\n", "      <td>770</td>\n", "      <td>0</td>\n", "      <td>1933</td>\n", "      <td>0</td>\n", "      <td>98028</td>\n", "      <td>47.7379</td>\n", "      <td>-122.233</td>\n", "      <td>2720</td>\n", "      <td>8062</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2487200875</td>\n", "      <td>20141209T000000</td>\n", "      <td>604000.0</td>\n", "      <td>4</td>\n", "      <td>3.00</td>\n", "      <td>1960</td>\n", "      <td>5000</td>\n", "      <td>1.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>5</td>\n", "      <td>7</td>\n", "      <td>1050</td>\n", "      <td>910</td>\n", "      <td>1965</td>\n", "      <td>0</td>\n", "      <td>98136</td>\n", "      <td>47.5208</td>\n", "      <td>-122.393</td>\n", "      <td>1360</td>\n", "      <td>5000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1954400510</td>\n", "      <td>20150218T000000</td>\n", "      <td>510000.0</td>\n", "      <td>3</td>\n", "      <td>2.00</td>\n", "      <td>1680</td>\n", "      <td>8080</td>\n", "      <td>1.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>8</td>\n", "      <td>1680</td>\n", "      <td>0</td>\n", "      <td>1987</td>\n", "      <td>0</td>\n", "      <td>98074</td>\n", "      <td>47.6168</td>\n", "      <td>-122.045</td>\n", "      <td>1800</td>\n", "      <td>7503</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           id             date     price  ...     long  sqft_living15  sqft_lot15\n", "0  7129300520  20141013T000000  221900.0  ... -122.257           1340        5650\n", "1  6414100192  20141209T000000  538000.0  ... -122.319           1690        7639\n", "2  5631500400  20150225T000000  180000.0  ... -122.233           2720        8062\n", "3  2487200875  20141209T000000  604000.0  ... -122.393           1360        5000\n", "4  1954400510  20150218T000000  510000.0  ... -122.045           1800        7503\n", "\n", "[5 rows x 21 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["kc_house_data.head()"]}, {"cell_type": "code", "execution_count": 32, "id": "4d342702", "metadata": {}, "outputs": [], "source": ["Xs_df = kc_house_data[[\"bedrooms\", \"bathrooms\", \"sqft_living\", \"floors\"]]\n", "Xs = Xs_df.values\n", "ys_df = kc_house_data[\"price\"]\n", "ys = ys_df.values"]}, {"cell_type": "code", "execution_count": 22, "id": "12cd0821", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.scatter(Xs_df[\"sqft_living\"], ys_df)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 11, "id": "263ad53f", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAkgAAAGdCAYAAADpBYyuAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjEsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvc2/+5QAAAAlwSFlzAAAPYQAAD2EBqD+naQAAKjhJREFUeJzt3Ql0FFXa//EnCSHs+7AN64jDvhkEQWBUlrDogDIqisAogiAoi38QRsCwKBAWWQVRAT2CAmcEEZBFkH1HkFXEIwojA7zKTiAEUv/z3PdUv103QBLM0kl/P+c0TXXd7nQ9qe78+ta91SGO4zgCAAAAn9D/+y8AAAAUAQkAAMBCQAIAALAQkAAAACwEJAAAAAsBCQAAwEJAAgAAsBCQAAAALNkkiCUkJMjJkyclb968EhISktFPBwAAJIOe4/rSpUtSsmRJCQ1Nm76eoA5IGo5Kly6d0U8DAADchRMnTkipUqUkLQR1QNKeI7fA+fLlS7J9fHy8rFq1Spo3by7h4eES7KiHF/Xwoh6JURMv6uFFPZJfj4sXL5oODvfveFoI6oDkHlbTcJTcgJQrVy7Tlp2Xetiohxf1SIyaeFEPL+qR8nqk5fAYBmkDAABYCEgAAAAWAhIAAICFgAQAAGAhIAEAAFgISAAAABYCEgAAgIWABAAAYCEgAQAAWAhIAAAAFgISAACAhYAEAABgISABAABYCEgAAACWbPYNCF7lBi5LUfuIMEdi6opUi14pcTdDJCP8PLp1hvxcAEDWRg8SAACAhYAEAABgISABAABYCEgAAAAWAhIAAICFgAQAAGAhIAEAAFgISAAAABYCEgAAgIWABAAAYCEgAQAAWAhIAAAAFgISAACAhYAEAABgISABAABYCEgAAAAWAhIAAICFgAQAAPBHA9KGDRvksccek5IlS0pISIgsXrzYs95xHBk6dKiUKFFCcubMKU2bNpWjR4962pw9e1Y6dOgg+fLlkwIFCkiXLl3k8uXLnjb79u2TRo0aSY4cOaR06dISExOT6LksXLhQKlWqZNpUr15dli9fntLNAQAA+OMB6cqVK1KzZk2ZNm3aLddrkJk8ebLMmDFDtm/fLrlz55aoqCi5du2ar42Go4MHD8rq1atl6dKlJnR169bNt/7ixYvSvHlzKVu2rOzevVvGjh0r0dHRMnPmTF+bLVu2yDPPPGPC1Z49e6Rt27bmcuDAgZRuEgAAgEc2SaGWLVuay61o79HEiRNl8ODB0qZNG3Pbxx9/LMWKFTM9Te3bt5fDhw/LihUrZOfOnVKnTh3TZsqUKdKqVSsZN26c6ZmaO3euXL9+XWbNmiXZs2eXqlWryt69e2XChAm+IDVp0iRp0aKF9O/f3yyPGDHCBK6pU6eacAYAAJBuAelOjh07JqdOnTKH1Vz58+eXevXqydatW01A0ms9rOaGI6XtQ0NDTY/T448/bto0btzYhCOX9kKNGTNGzp07JwULFjRt+vXr5/n52sY+5OcvLi7OXPx7qlR8fLy5JMVtk5y2mVFEmJOy9qGO5zojBNLvIqvvHylFPRKjJl7Uw4t6JL8e6VGjVA1IGo6U9hj502V3nV4XLVrU+ySyZZNChQp52pQvXz7RY7jrNCDp9Z1+zq2MGjVKhg0bluj2VatWSa5cuZK9ndpTlRXF1L27+42okyAZJRDHnWXV/eNuUY/EqIkX9fCiHknXIzY2VjJVQAp0gwYN8vQ6aQ+SDgDX8U46YDwpmlj1F9WsWTMJDw+XrKZa9MoUtdeeIw1HQ3aFSlxCiGSEA9FREiiy+v6RUtQjMWriRT28qEfy6+EeAco0Aal48eLm+vTp02YWm0uXa9Wq5Wtz5swZz/1u3LhhZra599drvY8/dzmpNu76W4mIiDAXmxY+JTtjSttnFnE37y7kaDi62/v+UYH4e8iq+8fdoh6JURMv6uFFPZKuR3rUJ1XPg6SHxTSgrFmzxpPydGxR/fr1zbJenz9/3sxOc61du1YSEhLMWCW3jc5s8z/GqCmyYsWK5vCa28b/57ht3J8DAACQbgFJz1ekM8r04g7M1v8fP37cnBepT58+MnLkSFmyZIns379fOnXqZGam6RR8VblyZTP7rGvXrrJjxw7ZvHmz9OrVywzg1nbq2WefNQO0dQq/ng5g/vz5Ztaa/+Gx3r17m9lw48ePl++//96cBmDXrl3msQAAANL1EJuGkIcffti37IaWzp07y5w5c2TAgAHmXEk6HV97iho2bGiCjJ7M0aXT+DXINGnSxMxea9eunTl3kv/MNx043bNnT4mMjJQiRYqYk0/6nyupQYMGMm/ePHNKgX/9619y7733mhls1apV+yP1AAAASHlAeuihh8z5jm5He5GGDx9uLrejM9Y03NxJjRo1ZOPGjXds8+STT5oLAABAauK72AAAACwEJAAAAAsBCQAAwEJAAgAAsBCQAAAALAQkAAAACwEJAADAQkACAACwEJAAAAAsBCQAAAALAQkAAMBCQAIAALAQkAAAACwEJAAAAAsBCQAAwEJAAgAAsBCQAAAALAQkAAAACwEJAADAQkACAACwEJAAAAAsBCQAAAALAQkAAMBCQAIAALAQkAAAACwEJAAAAAsBCQAAwEJAAgAAsBCQAAAALAQkAAAACwEJAADAQkACAACwEJAAAAAsBCQAAAALAQkAAMBCQAIAALAQkAAAACwEJAAAAAsBCQAAwEJAAgAAsBCQAAAALAQkAAAACwEJAADAQkACAACwEJAAAAAsBCQAAAALAQkAAMBCQAIAALAQkAAAACwEJAAAAAsBCQAAwEJAAgAAsBCQAAAALAQkAACAtA5IN2/elCFDhkj58uUlZ86ccs8998iIESPEcRxfG/3/0KFDpUSJEqZN06ZN5ejRo57HOXv2rHTo0EHy5csnBQoUkC5dusjly5c9bfbt2yeNGjWSHDlySOnSpSUmJia1NwcAAAShVA9IY8aMkenTp8vUqVPl8OHDZlmDy5QpU3xtdHny5MkyY8YM2b59u+TOnVuioqLk2rVrvjYajg4ePCirV6+WpUuXyoYNG6Rbt26+9RcvXpTmzZtL2bJlZffu3TJ27FiJjo6WmTNnpvYmAQCAIJMttR9wy5Yt0qZNG2ndurVZLleunHz66aeyY8cOX+/RxIkTZfDgwaad+vjjj6VYsWKyePFiad++vQlWK1askJ07d0qdOnVMGw1YrVq1knHjxknJkiVl7ty5cv36dZk1a5Zkz55dqlatKnv37pUJEyZ4ghQAAECG9yA1aNBA1qxZIz/88INZ/u6772TTpk3SsmVLs3zs2DE5deqUOazmyp8/v9SrV0+2bt1qlvVaD6u54Uhp+9DQUNPj5LZp3LixCUcu7YU6cuSInDt3LrU3CwAABJFU70EaOHCgOfxVqVIlCQsLM2OS3nrrLXPITGk4Utpj5E+X3XV6XbRoUe8TzZZNChUq5Gmj45zsx3DXFSxYMNFzi4uLMxeXPk8VHx9vLklx2ySnbWYUEeakrH2o47nOCIH0u8jq+0dKUY/EqIkX9fCiHsmvR3rUKNUD0oIFC8zhr3nz5vkOe/Xp08ccFuvcubNkpFGjRsmwYcMS3b5q1SrJlStXsh9Hx0VlRTF17+5+I+okSEZZvny5BJqsun/cLeqRGDXxoh5e1CPpesTGxkqmC0j9+/c3vUg6lkhVr15dfvnlFxNONCAVL17c3H769Gkzi82ly7Vq1TL/1zZnzpzxPO6NGzfMzDb3/nqt9/HnLrttbIMGDZJ+/fp5epB09psO9tbZcknRxKq/qGbNmkl4eLhkNdWiV6aovfYcaTgasitU4hJCJCMciI6SQJHV94+Uoh6JURMv6uFFPZJfD/cIUKYKSJrqdKyQPz3UlpDwv70MelhMA4yOU3IDkW6oji3q0aOHWa5fv76cP3/ezE6LjIw0t61du9Y8ho5Vctu88cYbpoBu4bSQFStWvOXhNRUREWEuNr1/SnbGlLbPLOJu3l3I0XB0t/f9owLx95BV94+7RT0SoyZe1MOLeiRdj/SoT6oP0n7sscfMmKNly5bJzz//LIsWLTIzyx5//HGzPiQkxBxyGzlypCxZskT2798vnTp1Mofg2rZta9pUrlxZWrRoIV27djWz3zZv3iy9evUyvVLaTj377LNmgLaeH0lPBzB//nyZNGmSp4cIAAAgIHqQdDq+nijy5ZdfNofJNNC89NJL5sSQrgEDBsiVK1fMdHztKWrYsKGZ1q8nfHTpOCYNRU2aNDE9Uu3atTPnTvKf+aZjh3r27Gl6mYoUKWJ+BlP8AQBAwAWkvHnzmvMc6eV2tBdp+PDh5nI7OmNNB3rfSY0aNWTjxo1/6PkCAADY+C42AAAACwEJAADAQkACAACwEJAAAAAsBCQAAAALAQkAAMBCQAIAALAQkAAAANL6RJH4X+UGLsvopwAAAO4SPUgAAAAWAhIAAICFgAQAAGAhIAEAAFgISAAAABYCEgAAgIWABAAAYCEgAQAAWAhIAAAAFgISAACAhYAEAABgISABAABYCEgAAAAWAhIAAICFgAQAAGAhIAEAAFgISAAAABYCEgAAgIWABAAAYCEgAQAAWAhIAAAAFgISAACAhYAEAABgISABAABYCEgAAAAWAhIAAICFgAQAAGAhIAEAAFgISAAAABYCEgAAgIWABAAAYCEgAQAAWAhIAAAAFgISAACAhYAEAABgISABAABYCEgAAAAWAhIAAICFgAQAAGAhIAEAAFgISAAAABYCEgAAgIWABAAAYCEgAQAAWAhIAAAAFgISAABAegSkX3/9VZ577jkpXLiw5MyZU6pXry67du3yrXccR4YOHSolSpQw65s2bSpHjx71PMbZs2elQ4cOki9fPilQoIB06dJFLl++7Gmzb98+adSokeTIkUNKly4tMTExabE5AAAgyKR6QDp37pw8+OCDEh4eLl999ZUcOnRIxo8fLwULFvS10SAzefJkmTFjhmzfvl1y584tUVFRcu3aNV8bDUcHDx6U1atXy9KlS2XDhg3SrVs33/qLFy9K8+bNpWzZsrJ7924ZO3asREdHy8yZM1N7kwAAQJDJltoPOGbMGNObM3v2bN9t5cuX9/QeTZw4UQYPHixt2rQxt3388cdSrFgxWbx4sbRv314OHz4sK1askJ07d0qdOnVMmylTpkirVq1k3LhxUrJkSZk7d65cv35dZs2aJdmzZ5eqVavK3r17ZcKECZ4gBQAAkOE9SEuWLDGh5sknn5SiRYtK7dq15f333/etP3bsmJw6dcocVnPlz59f6tWrJ1u3bjXLeq2H1dxwpLR9aGio6XFy2zRu3NiEI5f2Qh05csT0YgEAAARMD9JPP/0k06dPl379+sm//vUv0wv06quvmiDTuXNnE46U9hj502V3nV5ruPI80WzZpFChQp42/j1T/o+p6/wP6bni4uLMxf8wnYqPjzeXpLhtktM2IsyRrC4i1PFcZ4Tk/C7SS0r2j2BAPRKjJl7Uw4t6JL8e6VGjVA9ICQkJpufn7bffNsvag3TgwAEz3kgDUkYaNWqUDBs2LNHtq1atkly5ciX7cXRcVFJi6qb46WVaI+okZNjPXr58uQSa5OwfwYR6JEZNvKiHF/VIuh6xsbGS6QKSzkyrUqWK57bKlSvLv//9b/P/4sWLm+vTp0+bti5drlWrlq/NmTNnPI9x48YNM7PNvb9e6338uctuG9ugQYNMz5Z/D5KOl9LB3jpbLimaWPUX1axZMzMI/U6qRa+UrE57jjQcDdkVKnEJIRnyHA5ER0mgSMn+EQyoR2LUxIt6eFGP5NfDPQKUqQKSzmDTcUD+fvjhBzPbTOlhMQ0wa9as8QUi3VAdW9SjRw+zXL9+fTl//ryZnRYZGWluW7t2remd0rFKbps33njDFNAtnBayYsWKtzy8piIiIszFpvdPyc6YnPZxNzMmMGQEDUcZtb2B+CaS0v0pq6MeiVETL+rhRT2Srkd61CfVB2n37dtXtm3bZg6x/fjjjzJv3jwz9b5nz55mfUhIiPTp00dGjhxpBnTv379fOnXqZGamtW3b1tfj1KJFC+natavs2LFDNm/eLL169TIz3LSdevbZZ824Jj0/kp4OYP78+TJp0iRPDxEAAEBA9CDdf//9smjRInM4a/jw4abHSKf163mNXAMGDJArV66Y6fjaU9SwYUMzrV9P+OjSafwaipo0aWJmr7Vr186cO8l/5puOHdLgpb1MRYoUMSefZIo/AAAIuICkHn30UXO5He1F0vCkl9vRGWva+3QnNWrUkI0bN/6h5woAAGDju9gAAAAsBCQAAAALAQkAAMBCQAIAALAQkAAAACwEJAAAAAsBCQAAwEJAAgAAsBCQAAAALAQkAAAACwEJAADAQkACAACwEJAAAAAsBCQAAAALAQkAAMBCQAIAALAQkAAAACwEJAAAAAsBCQAAwEJAAgAAsBCQAAAALAQkAAAACwEJAADAQkACAACwEJAAAAAsBCQAAAALAQkAAMBCQAIAALAQkAAAACwEJAAAAAsBCQAAwEJAAgAAsBCQAAAALAQkAAAACwEJAADAQkACAACwEJAAAAAsBCQAAAALAQkAAMBCQAIAALAQkAAAACwEJAAAAAsBCQAAwEJAAgAAsBCQAAAALAQkAAAACwEJAADAQkACAACwEJAAAAAsBCQAAAALAQkAAMBCQAIAALAQkAAAACwEJAAAAAsBCQAAIL0D0ujRoyUkJET69Onju+3atWvSs2dPKVy4sOTJk0fatWsnp0+f9tzv+PHj0rp1a8mVK5cULVpU+vfvLzdu3PC0Wbdundx3330SEREhFSpUkDlz5qT15gAAgCCQpgFp586d8t5770mNGjU8t/ft21e+/PJLWbhwoaxfv15OnjwpTzzxhG/9zZs3TTi6fv26bNmyRT766CMTfoYOHeprc+zYMdPm4Ycflr1795oA9uKLL8rKlSvTcpMAAEAQSLOAdPnyZenQoYO8//77UrBgQd/tFy5ckA8//FAmTJggjzzyiERGRsrs2bNNENq2bZtps2rVKjl06JB88sknUqtWLWnZsqWMGDFCpk2bZkKTmjFjhpQvX17Gjx8vlStXll69esk//vEPeeedd9JqkwAAQJDIllYPrIfQtIenadOmMnLkSN/tu3fvlvj4eHO7q1KlSlKmTBnZunWrPPDAA+a6evXqUqxYMV+bqKgo6dGjhxw8eFBq165t2vg/htvG/1CeLS4uzlxcFy9eNNf6fPSSFLdNctpGhDmS1UWEOp7rjJCc30V6Scn+EQyoR2LUxIt6eFGP5NcjPWqUJgHps88+k2+//dYcYrOdOnVKsmfPLgUKFPDcrmFI17lt/MORu95dd6c2GnquXr0qOXPmTPSzR40aJcOGDUt0u/ZY6Vin5Fq9enWSbWLqStAYUSchw3728uXLJdAkZ/8IJtQjMWriRT28qEfS9YiNjZVMF5BOnDghvXv3NhuUI0cOCSSDBg2Sfv36+ZY1TJUuXVqaN28u+fLlS/L+mlh1u5o1aybh4eF3bFstOuuPhdKeIw1HQ3aFSlxCSIY8hwPRURIoUrJ/BAPqkRg18aIeXtQj+fVwjwBlqoCkh9DOnDljZpf5D7resGGDTJ061Qyi1nFE58+f9/Qi6Sy24sWLm//r9Y4dOzyP685y829jz3zTZQ06t+o9UjrbTS82LXxKdsbktI+7mTGBISNoOMqo7Q3EN5GU7k9ZHfVIjJp4UQ8v6pF0PdKjPqk+SLtJkyayf/9+M7PMvdSpU8cM2Hb/rxu2Zs0a332OHDlipvXXr1/fLOu1PoYGLZemSA0/VapU8bXxfwy3jfsYAAAAAdODlDdvXqlWrZrntty5c5tzHrm3d+nSxRzqKlSokAk9r7zyigk2OkBb6SEvDUIdO3aUmJgYM95o8ODBZuC32wPUvXt30yM1YMAAeeGFF2Tt2rWyYMECWbZsWWpvEgAACDJpNovtTnQqfmhoqDlBpM4q09ln7777rm99WFiYLF261Mxa0+CkAatz584yfPhwXxud4q9hSM+pNGnSJClVqpR88MEH5rEAAAACPiDpGa/96eBtPaeRXm6nbNmySc5Qeuihh2TPnj2p9jwBAAAU38UGAABgISABAABYCEgAAAAWAhIAAICFgAQAAGAhIAEAAFgISAAAABYCEgAAgIWABAAAYCEgAQAAWAhIAAAAFgISAACAhYAEAABgISABAABYCEgAAAAWAhIAAICFgAQAAGAhIAEAAFgISAAAABYCEgAAgIWABAAAYCEgAQAAWAhIAAAAFgISAACAhYAEAABgISABAABYCEgAAAAWAhIAAICFgAQAAGAhIAEAAFgISAAAABYCEgAAgIWABAAAYMlm3wBkJuUGLpNAERHmSExdkWrRKyXuZsht2/08unW6Pi8AQMrRgwQAAGAhIAEAAFgISAAAABYCEgAAgIWABAAAYCEgAQAAWAhIAAAAFgISAACAhYAEAABgISABAABYCEgAAAAWAhIAAICFgAQAAGAhIAEAAFgISAAAABYCEgAAgIWABAAAYCEgAQAAWAhIAAAAFgISAACAhYAEAACQ1gFp1KhRcv/990vevHmlaNGi0rZtWzly5IinzbVr16Rnz55SuHBhyZMnj7Rr105Onz7taXP8+HFp3bq15MqVyzxO//795caNG54269atk/vuu08iIiKkQoUKMmfOnNTeHAAAEIRSPSCtX7/ehJ9t27bJ6tWrJT4+Xpo3by5Xrlzxtenbt698+eWXsnDhQtP+5MmT8sQTT/jW37x504Sj69evy5YtW+Sjjz4y4Wfo0KG+NseOHTNtHn74Ydm7d6/06dNHXnzxRVm5cmVqbxIAAAgy2VL7AVesWOFZ1mCjPUC7d++Wxo0by4ULF+TDDz+UefPmySOPPGLazJ49WypXrmxC1QMPPCCrVq2SQ4cOyddffy3FihWTWrVqyYgRI+T111+X6OhoyZ49u8yYMUPKly8v48ePN4+h99+0aZO88847EhUVldqbBQAAgkiqBySbBiJVqFAhc61BSXuVmjZt6mtTqVIlKVOmjGzdutUEJL2uXr26CUcuDT09evSQgwcPSu3atU0b/8dw22hP0u3ExcWZi+vixYvmWp+PXpLitklO24gwR7K6iFDHcx3skluP5Ow/WUFKXi/Bgpp4UQ8v6pH8eqRHjdI0ICUkJJjA8uCDD0q1atXMbadOnTI9QAUKFPC01TCk69w2/uHIXe+uu1MbDT1Xr16VnDlz3nJ81LBhwxLdrj1WOtYpufTQYVJi6krQGFEnIaOfQqaqx/LlyyWYJOf1EmyoiRf18KIeSdcjNjZWMnVA0rFIBw4cMIe+AsGgQYOkX79+vmUNU6VLlzZjpPLly5fk/TWx6i+qWbNmEh4efse21aKz/lgo7SnRMDBkV6jEJYRIsEtuPQ5EB8ch4JS8XoIFNfGiHl7UI/n1cI8AZcqA1KtXL1m6dKls2LBBSpUq5bu9ePHiZvD1+fPnPb1IOotN17ltduzY4Xk8d5abfxt75psua9C5Ve+R0tluerFp4VOyMyanfdzN4AkMGgaCaXv/aD2C7Y0vpa+vYEBNvKiHF/VIuh7pUZ9Un8XmOI4JR4sWLZK1a9eagdT+IiMjzYatWbPGd5ueBkCn9devX98s6/X+/fvlzJkzvjaaIjX8VKlSxdfG/zHcNu5jAAAABEwPkh5W0xlqX3zxhTkXkjtmKH/+/KZnR6+7dOliDnXpwG0NPa+88ooJNjpAW+khLw1CHTt2lJiYGPMYgwcPNo/t9gB1795dpk6dKgMGDJAXXnjBhLEFCxbIsmXLUnuTAABAkEn1HqTp06ebmWsPPfSQlChRwneZP3++r41OxX/00UfNCSJ16r8eLvv8889968PCwszhOb3W4PTcc89Jp06dZPjw4b422jOlYUh7jWrWrGmm+3/wwQdM8QcAAIHXg6SH2JKSI0cOmTZtmrncTtmyZZOc7aMhbM+ePXf1PAEAAG6H72IDAACwEJAAAAAsBCQAAAALAQkAAMBCQAIAALAQkAAAACwEJAAAAAsBCQAAwEJAAgAAsBCQAAAALAQkAAAACwEJAADAQkACAACwEJAAAAAsBCQAAAALAQkAAMBCQAIAALAQkAAAACwEJAAAAAsBCQAAwEJAAgAAsBCQAAAALAQkAAAACwEJAADAQkACAACwEJAAAAAsBCQAAAALAQkAAMBCQAIAALAQkAAAACwEJAAAAAsBCQAAwEJAAgAAsBCQAAAALAQkAAAACwEJAADAQkACAACwEJAAAAAsBCQAAAALAQkAAMBCQAIAALAQkAAAACwEJAAAAAsBCQAAwEJAAgAAsGSzbwCQtsoNXCaZ0c+jW2f0UwCAdEMPEgAAgIWABAAAYCEgAQAAWAhIAAAAFgISAACAhYAEAABgISABAABYCEgAAAAWAhIAAEBWC0jTpk2TcuXKSY4cOaRevXqyY8eOjH5KAAAgk8vUAWn+/PnSr18/efPNN+Xbb7+VmjVrSlRUlJw5cyajnxoAAMjEMvV3sU2YMEG6du0qzz//vFmeMWOGLFu2TGbNmiUDBw7M6KcHBPV3yEWEORJTV6Ra9EqJuxkiGYHvjwMQdAHp+vXrsnv3bhk0aJDvttDQUGnatKls3br1lveJi4szF9eFCxfM9dmzZyU+Pj7Jn6ltYmNj5ffff5fw8PA7ts1244pkddkSHImNTZBs8aFyMyFj/gAGEuoRePWo8P8WSCCJCHVkcO0EqfXG5xJ3m5psH9REgkVK3lODAfVIfj0uXbpkrh3HkbSSaQPSb7/9Jjdv3pRixYp5btfl77///pb3GTVqlAwbNizR7eXLl0+z55nVPZvRTyDAUA8v6pHymhQZn05PBMgCLl26JPnz50+Tx860AeluaG+TjllyJSQkmN6jwoULS0hI0p9wL168KKVLl5YTJ05Ivnz5JNhRDy/q4UU9EqMmXtTDi3okvx7ac6ThqGTJkpJWMm1AKlKkiISFhcnp06c9t+ty8eLFb3mfiIgIc/FXoECBFP9s/UWx8/4f6uFFPbyoR2LUxIt6eFGP5NUjrXqOMv0stuzZs0tkZKSsWbPG0yOky/Xr18/Q5wYAADK3TNuDpPRwWefOnaVOnTpSt25dmThxoly5csU3qw0AACDoAtLTTz8t//M//yNDhw6VU6dOSa1atWTFihWJBm6nFj08p+dcsg/TBSvq4UU9vKhHYtTEi3p4UY/AqkeIk5Zz5AAAADKhTDsGCQAAIK0QkAAAACwEJAAAAAsBCQAAwEJASoFp06ZJuXLlJEeOHFKvXj3ZsWOHZHb69Sv333+/5M2bV4oWLSpt27aVI0eOeNpcu3ZNevbsac44nidPHmnXrl2iE3QeP35cWrduLbly5TKP079/f7lx44anzbp16+S+++4zMxIqVKggc+bMkUA3evRoc5b1Pn36BG09fv31V3nuuefM9ubMmVOqV68uu3bt8q3XeR46k7REiRJmvX4f4tGjRz2PoWes79ChgznZm56ctUuXLnL58mVPm3379kmjRo3M60vPnhsTEyOBRr/eaMiQIebriXRb77nnHhkxYoTn+6Cycj02bNggjz32mDl7sb4uFi9e7Fmfntu+cOFCqVSpkmmj++Ty5cslkOqh3yP2+uuvm+eWO3du06ZTp05y8uTJoKyHrXv37qaNnp4nYOuhs9iQtM8++8zJnj27M2vWLOfgwYNO165dnQIFCjinT592MrOoqChn9uzZzoEDB5y9e/c6rVq1csqUKeNcvnzZ16Z79+5O6dKlnTVr1ji7du1yHnjgAadBgwa+9Tdu3HCqVavmNG3a1NmzZ4+zfPlyp0iRIs6gQYN8bX766ScnV65cTr9+/ZxDhw45U6ZMccLCwpwVK1Y4gWrHjh1OuXLlnBo1aji9e/cOynqcPXvWKVu2rPPPf/7T2b59u3neK1eudH788Udfm9GjRzv58+d3Fi9e7Hz33XfO3//+d6d8+fLO1atXfW1atGjh1KxZ09m2bZuzceNGp0KFCs4zzzzjW3/hwgWnWLFiTocOHcy++Omnnzo5c+Z03nvvPSeQvPXWW07hwoWdpUuXOseOHXMWLlzo5MmTx5k0aVJQ1EP35TfeeMP5/PPPNRE6ixYt8qxPr23fvHmzeb3ExMSY18/gwYOd8PBwZ//+/U6g1OP8+fPmPWD+/PnO999/72zdutWpW7euExkZ6XmMYKmHP12v21yyZEnnnXfeCdh6EJCSSXfsnj17+pZv3rxpfrmjRo1yspIzZ86YHXv9+vW+F7nuWPqHwHX48GHTRl/w7osiNDTUOXXqlK/N9OnTnXz58jlxcXFmecCAAU7VqlU9P+vpp582AS0QXbp0ybn33nud1atXO3/72998ASnY6vH66687DRs2vO36hIQEp3jx4s7YsWN9t2mNIiIizBuX0jcorc/OnTt9bb766isnJCTE+fXXX83yu+++6xQsWNBXH/dnV6xY0QkkrVu3dl544QXPbU888YR5sw62eth/ANNz25966inzu/BXr14956WXXnIyyp0Cgf+HLm33yy+/BG09/vOf/zh//vOfTbjRD1/+ASnQ6sEhtmS4fv267N6923QXu0JDQ83y1q1bJSu5cOGCuS5UqJC51u3WrmL/bdduyzJlyvi2Xa+1C9P/BJ1RUVHmiwYPHjzoa+P/GG6bQK2fHkLTQ2T2cw62eixZssScqf7JJ580hwpr164t77//vm/9sWPHzEla/bdFvx9JD0H710O7yvVxXNpeX0Pbt2/3tWncuLH5CiH/eujh3nPnzkmgaNCggfk6ox9++MEsf/fdd7Jp0yZp2bJlUNbDX3pue2Z5/dzq/VUPK7nfARps9UhISJCOHTuaIQdVq1ZNtD7Q6kFASobffvvNjD2wz9Cty/qGkFXozqtjbR588EGpVq2auU23T3dE+0t9/bddr29VG3fdndpoaLh69aoEks8++0y+/fZbMz7LFmz1+Omnn2T69Oly7733ysqVK6VHjx7y6quvykcffeTZnju9NvRaw5W/bNmymRCekpoFgoEDB0r79u1NKA4PDzeBUV8zOmYiGOvhLz23/XZtArU27thFHZP0zDPP+L54NdjqMWbMGLN9+h5yK4FWj0z9VSNI/V6TAwcOmE/EwerEiRPSu3dvWb16tRncF+w0NOunubffftssayDQfWTGjBnmexCDzYIFC2Tu3Lkyb9488wl47969JiDpoNRgrAeSR3udn3rqKTOIXT9wBKPdu3fLpEmTzIdP7UXLDOhBSoYiRYpIWFhYoplKuly8eHHJCnr16iVLly6Vb775RkqVKuW7XbdPDzGeP3/+ttuu17eqjbvuTm30k5TOdgmkF/GZM2fM7DL95KKX9evXy+TJk83/9VNIMNVDZyNVqVLFc1vlypXNLD3/7bnTa0Ovtab+dEafzlZJSc0CgR4acHuR9DCqHi7o27evr7cx2OrhLz23/XZtArE2bjj65ZdfzAcvt/co2OqxceNGs606HMF9b9WavPbaa2Z2eCDWg4CUDHpIJTIy0ow98P9krcv169eXzEw/0Wg4WrRokaxdu9ZMX/an262HEvy3XY/16h9Id9v1ev/+/Z4d230jcP+4ahv/x3DbBFr9mjRpYrZFewbci/ag6CEU9//BVA893Gqf9kHH35QtW9b8X/cXfdPx3xY9TKjjBfzroYFSw6dL9zV9Den4FLeNThHWPyb+9ahYsaIULFhQAkVsbKwZD+FPPzzptgRjPfyl57ZnltePG470VAdff/21OVWGv2CqR8eOHc30fP/3Vu151Q8devg+IOuRoiHdQT7NX2djzJkzx4y079atm5nm7z9TKTPq0aOHmZa7bt0657///a/vEhsb65nWrlP/165da6a1169f31zsae3Nmzc3pwrQqep/+tOfbjmtvX///mbW17Rp0wJyWvut+M9iC7Z66KybbNmymentR48edebOnWue9yeffOKZ2q2vhS+++MLZt2+f06ZNm1tO7a5du7Y5VcCmTZvMDEH/qbs620mn7nbs2NHMbtHXm/6cjJ7WbuvcubOZgeNO89fpynoKB52VGAz10NmdeuoKveifjwkTJpj/u7Oy0mvbdRq37pfjxo0zr58333wzQ6a136ke169fN6c5KFWqlHkf8H9/9Z+BFSz1uBV7Flug1YOAlAJ6rhr9w6jnQ9Jp/3qehsxOd+JbXfTcSC59c3v55ZfN1ErdER9//HHzIvf3888/Oy1btjTno9A/GK+99poTHx/vafPNN984tWrVMvX7y1/+4vkZmSkgBVs9vvzySxP49ANCpUqVnJkzZ3rW6/TuIUOGmDctbdOkSRPnyJEjnja///67eZPTcwbp6Q6ef/5582bqT8+bo6cU0MfQEKJ/bAPNxYsXzb6g7wM5cuQwvzc974v/H7ysXA/dZ2/1fqHBMb23fcGCBc5f//pX8/rRU2YsW7bMCaR6aIC+3fur3i/Y6pHcgBRI9QjRf/5ItxkAAEBWwxgkAAAACwEJAADAQkACAACwEJAAAAAsBCQAAAALAQkAAMBCQAIAALAQkAAAACwEJAAAAAsBCQAAwEJAAgAAsBCQAAAAxOv/A9W/YED8sh8+AAAAAElFTkSuQmCC", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["Xs_df[\"sqft_living\"].hist()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 33, "id": "59942252", "metadata": {}, "outputs": [{"data": {"text/plain": ["((14408, 4), (7205, 4), (14408,), (7205,))"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["X_train, X_test, y_train, y_test = train_test_split(Xs, ys, test_size=1/3, random_state=0)\n", "X_train.shape, X_test.shape, y_train.shape, y_test.shape"]}, {"cell_type": "code", "execution_count": 24, "id": "03a10e05", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>bedrooms</td>\n", "      <td>-51118.881973</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>bathrooms</td>\n", "      <td>4146.343826</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>sqft_living</td>\n", "      <td>300.225555</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>floors</td>\n", "      <td>5226.700983</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             0             1\n", "0     bedrooms -51118.881973\n", "1    bathrooms   4146.343826\n", "2  sqft_living    300.225555\n", "3       floors   5226.700983"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["from sklearn.linear_model import LinearRegression\n", "\n", "model = LinearRegression()\n", "model.fit(X_train, y_train)\n", "\n", "pd.DataFrame(list(zip(Xs_df.columns, model.coef_)))"]}, {"cell_type": "code", "execution_count": 25, "id": "a352c45c", "metadata": {}, "outputs": [{"data": {"text/plain": ["(np.float64(71317.92892233969),\n", " array([-51118.88197258,   4146.34382619,    300.22555525,   5226.70098316]))"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["model.intercept_, model.coef_"]}, {"cell_type": "code", "execution_count": 34, "id": "85bd78d7", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[3.00e+00, 1.50e+00, 1.26e+03, 1.00e+00],\n", "       [2.00e+00, 1.00e+00, 1.32e+03, 1.50e+00],\n", "       [3.00e+00, 1.00e+00, 9.20e+02, 1.00e+00],\n", "       ...,\n", "       [3.00e+00, 2.25e+00, 2.36e+03, 1.00e+00],\n", "       [4.00e+00, 2.00e+00, 2.37e+03, 2.00e+00],\n", "       [4.00e+00, 2.25e+00, 2.38e+03, 1.00e+00]])"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["X_train"]}, {"cell_type": "code", "execution_count": 35, "id": "6b7f167f", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[1.00e+00, 3.00e+00, 1.50e+00, 1.26e+03, 1.00e+00],\n", "       [1.00e+00, 2.00e+00, 1.00e+00, 1.32e+03, 1.50e+00],\n", "       [1.00e+00, 3.00e+00, 1.00e+00, 9.20e+02, 1.00e+00],\n", "       ...,\n", "       [1.00e+00, 3.00e+00, 2.25e+00, 2.36e+03, 1.00e+00],\n", "       [1.00e+00, 4.00e+00, 2.00e+00, 2.37e+03, 2.00e+00],\n", "       [1.00e+00, 4.00e+00, 2.25e+00, 2.38e+03, 1.00e+00]])"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["X_train = np.c_[np.ones((X_train.shape[0], 1)), X_train]\n", "X_train"]}, {"cell_type": "code", "execution_count": 36, "id": "bb39d5a8", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 71317.9289224 , -51118.88197258,   4146.34382619,    300.22555525,\n", "         5226.70098316])"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["betas = np.linalg.inv(X_train.T @ X_train) @ X_train.T @ y_train\n", "betas"]}, {"cell_type": "code", "execution_count": 37, "id": "3b63acb8", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[1.00e+00, 2.00e+00, 1.50e+00, 1.43e+03, 3.00e+00],\n", "       [1.00e+00, 4.00e+00, 3.25e+00, 4.67e+03, 2.00e+00],\n", "       [1.00e+00, 2.00e+00, 7.50e-01, 1.44e+03, 1.00e+00],\n", "       ...,\n", "       [1.00e+00, 2.00e+00, 2.00e+00, 1.87e+03, 1.00e+00],\n", "       [1.00e+00, 2.00e+00, 1.50e+00, 1.16e+03, 2.00e+00],\n", "       [1.00e+00, 2.00e+00, 1.00e+00, 1.04e+03, 1.00e+00]])"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["X_test = np.c_[np.ones((X_test.shape[0], 1)), X_test]\n", "X_test"]}, {"cell_type": "code", "execution_count": 38, "id": "46e0a53f", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 420302.32767254, 1292824.76344785,  409741.42338907, ...,\n", "        544021.34192901,  334014.72677206,  290687.78724589])"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["y_pred = X_test @ betas\n", "y_pred"]}, {"cell_type": "code", "execution_count": 39, "id": "72b58ac0", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(0.346243690152302)"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["(abs(y_pred - y_test) / y_test).sum() / len(y_test)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}