import numpy as np
import pandas as pd
from matplotlib import pyplot as plt
from sklearn.neural_network import MLPClassifier
from sklearn.model_selection import train_test_split
from sklearn import metrics

data = pd.read_csv("../.inputs/otto/train.csv")
data.head()

data.info()

Xs_columns = data.columns[1: -1]
Xs = data[Xs_columns].values
ys = data["target"].values
Xs.shape, ys.shape

data["target"].unique()

dist = data.groupby("target").size() / data.shape[0] * 100
dist.plot(kind="pie", autopct='%1.1f%%')

for idx in range(1, 10):
    ##
    plt.subplot(3, 3, idx)
    data[data["target"] == f"Class_{idx}"]["feat_90"].hist()

plt.show()

plt.scatter(data.feat_1, data.feat_2)
plt.show()

## Correlation matrix
correlation_matrix = data[Xs_columns].corr()
correlation_matrix

## Plot correlation matrix
plt.figure(figsize=(10, 10))
plt.imshow(correlation_matrix, cmap="coolwarm", interpolation="nearest")
plt.colorbar()
plt.show()

## MLP

model = MLPClassifier(alpha=1e-5, hidden_layer_sizes=(30, 10), random_state=1, verbose=True)
model.fit(Xs, ys)

model.intercepts_

model.coefs_

model.coefs_[0].shape, model.coefs_[1].shape, model.coefs_[2].shape

test = pd.read_csv("../.inputs/otto/test.csv")
X_test = test[Xs_columns].values
X_test.shape

proba = model.predict_proba(X_test)
proba[0]