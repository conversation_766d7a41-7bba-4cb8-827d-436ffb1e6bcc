{"cells": [{"cell_type": "code", "execution_count": 163, "id": "88ce739a", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import tqdm\n", "from patsy import dmatrices\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.model_selection import cross_val_score,train_test_split"]}, {"cell_type": "code", "execution_count": 136, "id": "8ff56d90", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>satisfaction_level</th>\n", "      <th>last_evaluation</th>\n", "      <th>number_project</th>\n", "      <th>average_montly_hours</th>\n", "      <th>time_spend_company</th>\n", "      <th>Work_accident</th>\n", "      <th>left</th>\n", "      <th>promotion_last_5years</th>\n", "      <th>sales</th>\n", "      <th>salary</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0.38</td>\n", "      <td>0.53</td>\n", "      <td>2</td>\n", "      <td>157</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>sales</td>\n", "      <td>low</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0.80</td>\n", "      <td>0.86</td>\n", "      <td>5</td>\n", "      <td>262</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>sales</td>\n", "      <td>medium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0.11</td>\n", "      <td>0.88</td>\n", "      <td>7</td>\n", "      <td>272</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>sales</td>\n", "      <td>medium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0.72</td>\n", "      <td>0.87</td>\n", "      <td>5</td>\n", "      <td>223</td>\n", "      <td>5</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>sales</td>\n", "      <td>low</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0.37</td>\n", "      <td>0.52</td>\n", "      <td>2</td>\n", "      <td>159</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>sales</td>\n", "      <td>low</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   satisfaction_level  last_evaluation  number_project  average_montly_hours  \\\n", "0                0.38             0.53               2                   157   \n", "1                0.80             0.86               5                   262   \n", "2                0.11             0.88               7                   272   \n", "3                0.72             0.87               5                   223   \n", "4                0.37             0.52               2                   159   \n", "\n", "   time_spend_company  Work_accident  left  promotion_last_5years  sales  \\\n", "0                   3              0     1                      0  sales   \n", "1                   6              0     1                      0  sales   \n", "2                   4              0     1                      0  sales   \n", "3                   5              0     1                      0  sales   \n", "4                   3              0     1                      0  sales   \n", "\n", "   salary  \n", "0     low  \n", "1  medium  \n", "2  medium  \n", "3     low  \n", "4     low  "]}, "execution_count": 136, "metadata": {}, "output_type": "execute_result"}], "source": ["inputs_df = pd.read_csv(\"../.inputs/hr/HR_comma_sep.csv\")\n", "inputs_df.head()"]}, {"cell_type": "code", "execution_count": 137, "id": "f8d84ce8", "metadata": {}, "outputs": [{"data": {"text/plain": ["satisfaction_level       float64\n", "last_evaluation          float64\n", "number_project             int64\n", "average_montly_hours       int64\n", "time_spend_company         int64\n", "Work_accident              int64\n", "left                       int64\n", "promotion_last_5years      int64\n", "sales                     object\n", "salary                    object\n", "dtype: object"]}, "execution_count": 137, "metadata": {}, "output_type": "execute_result"}], "source": ["inputs_df.dtypes"]}, {"cell_type": "code", "execution_count": 138, "id": "0f93b3a5", "metadata": {}, "outputs": [{"data": {"text/plain": ["salary\n", "high      0.066289\n", "low       0.296884\n", "medium    0.204313\n", "Name: left, dtype: float64"]}, "execution_count": 138, "metadata": {}, "output_type": "execute_result"}], "source": ["inputs_df.groupby(\"salary\")[\"left\"].mean()"]}, {"cell_type": "code", "execution_count": 139, "id": "7bd0a916", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='salary'>"]}, "execution_count": 139, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["pd.crosstab(inputs_df.salary, inputs_df.left).plot(kind=\"bar\", stacked=True)"]}, {"cell_type": "code", "execution_count": 140, "id": "4d21219a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["left       0     1\n", "salary            \n", "high    1155    82\n", "low     5144  2172\n", "medium  5129  1317\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["q = pd.crosstab(inputs_df.salary, inputs_df.left)\n", "print(q)\n", "q.div(q.sum(1), axis=0).plot(kind=\"bar\", stacked=True)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 141, "id": "423246d7", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAjAAAAGdCAYAAAAMm0nCAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjEsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvc2/+5QAAAAlwSFlzAAAPYQAAD2EBqD+naQAAKhRJREFUeJzt3Q1UVHX+x/EvIAziCj4FyEZodjI1UdOVpdI0RVIPPbnbA6a0a9ID1lnZNaPUUFox7Jjmmh13NduTprXHrFWPglppiZm0rE/lpqnUSXArFZW/PN7/+f3WmRhEBZph5se8X+dch5l7586P+TFzP/4e7vWzLMsSAAAAg/h7ugAAAACNRYABAADGIcAAAADjEGAAAIBxCDAAAMA4BBgAAGAcAgwAADAOAQYAABinlbRQNTU18t1330nbtm3Fz8/P08UBAAANoM6ve+bMGYmKihJ/f3/fCzAqvERHR3u6GAAAoAm++eYbufrqq30vwKiWF/sbEBoa6uniGKGyslJyc3NlxIgREhgY6Oni+DTqwntQF96DuvCNuigtLdUNEPbjuM8FGHu3kQovBJiG/0GGhITo94svB8+iLrwHdeE9qAvfqgu/Kwz/YBAvAAAwDgEGAAAYhwADAACMQ4ABAADGIcAAAADjEGAAAIBxCDAAAMA4BBgAANDyA8y2bdskKSlJX6NAnWRm7dq1TuvVY/Utc+fOdWzTpUuXi9bPmTPHaT979uyRQYMGSXBwsD4jX05Ozs/5PQEAgC8HmHPnzkmfPn1k0aJF9a4/fvy407Js2TIdUMaMGeO03axZs5y2e/LJJ51OI6xOTxwTEyMFBQU6/GRmZsqSJUua8jsCAIAWptGXEhg5cqReLiUyMtLp/nvvvSdDhw6Va6+91ulxdY2DutvarVixQioqKnT4CQoKkl69eklhYaHMmzdPUlNTG1tkAADQwrh1DExJSYmsX79eJkyYcNE61WXUsWNH6devn25hqaqqcqzLz8+XwYMH6/Bil5iYKAcPHpSTJ0+6s8gAAMAAbr2Y4xtvvKFbWu69916nx5966im56aabpEOHDrJjxw7JyMjQ3UiqhUUpLi6Wrl27Oj0nIiLCsa59+/YXvVZ5ebleandD2S84pRZcmf194v3yPOrCe1AX3oO68I26qGzgPt0aYFQX0NixY/VA3NrS09MdP8fGxuqWlkcffVSys7PFZrM16bXUc2fOnHnR4+py3+qKmWi4vLw8TxcBF1AX3oO68B7UhfdwR12UlZV5NsBs375dd/msXr36itvGxcXpLqSjR49K9+7d9dgY1f1Um/3+pcbNqFac2sFItcCo2UtqMLC63DcalnrVH2NCQgKXqvcw6uLnuTFzk8v2ZfO3JGtAjUzf7S/lNX7iDvsyE92y35aGz4Vv1EXphR4UjwWYpUuXSv/+/fWMpStRA3T9/f0lPDxc34+Pj5fnnntOv0H2N0a9USrc1Nd9pKiWm/pab9Tz+UNvHN4z70FdNE15teuDhgov7tivQh03Dp8L7+GOumjo/ho9iPfs2bM6cKhFOXLkiP65qKjIKT2988478sgjj1z0fDVAd/78+fLvf/9bvv76az3jaPLkyfLQQw85wklycrLuVlKDf/fv369bcRYsWODUwgIAAHxXo1tgdu/eradF29lDRUpKiixfvlz/vGrVKrEsSx588MGLnq9aSdR6dV4XNehWDdZVAaZ2OAkLC9NjV9LS0nQrTqdOnWTGjBlMoQbQInV5Zr2Y4uic0Z4uAtC0ADNkyBAdTi5HBY1LhQ01+2jnzp1XfB01uFeNowEAAKiLayEBAADjEGAAAIBxCDAAAMA4BBgAAGAcAgwAADCOWy8lAABoWTw55dsWYEnOwP+dabmhJxVk2nfLRQsMAAAwDgEGAAAYhwADAACMQ4ABAADGIcAAAADjEGAAAIBxCDAAAMA4BBgAAGAcAgwAADAOAQYAABiHAAMAAIxDgAEAAMYhwAAAAOMQYAAAgHEIMAAAwDgEGAAAYBwCDAAAMA4BBgAAGIcAAwAAjEOAAQAAxiHAAAAA4xBgAACAcQgwAADAOAQYAABgHAIMAAAwDgEGAAAYp5WnCwAAgLt0eWa9mOTonNGeLoIxaIEBAADGIcAAAADjEGAAAIBxGAMDAICXMGXMji3AkpyBni0DLTAAAMA4BBgAAGAcAgwAAGj5AWbbtm2SlJQkUVFR4ufnJ2vXrnVa//DDD+vHay933HGH0zY//vijjB07VkJDQ6Vdu3YyYcIEOXv2rNM2e/bskUGDBklwcLBER0dLTk5OU39HAADQwjQ6wJw7d0769OkjixYtuuQ2KrAcP37csbz11ltO61V42b9/v+Tl5cm6det0KEpNTXWsLy0tlREjRkhMTIwUFBTI3LlzJTMzU5YsWdLY4gIAgBao0bOQRo4cqZfLsdlsEhkZWe+6L774QjZu3CifffaZDBgwQD+2cOFCGTVqlLz00ku6ZWfFihVSUVEhy5Ytk6CgIOnVq5cUFhbKvHnznIIOAADwTW6ZRv3hhx9KeHi4tG/fXm6//XZ54YUXpGPHjnpdfn6+7jayhxdl+PDh4u/vL59++qncc889epvBgwfr8GKXmJgoL774opw8eVLvt67y8nK91G7FUSorK/WCK7O/T7xfnkdd/Pwpni7bl7/ldAvPoS68h+1CHbjjO6qh+3R5gFHdR/fee6907dpVDh8+LM8++6xusVGhJCAgQIqLi3W4cSpEq1bSoUMHvU5Rt+r5tUVERDjW1RdgsrOzZebMmRc9npubKyEhIS7+LVs21bUH70BdNI07zk+RNaDG9TtFk1AX3sMd31FlZWWeCTAPPPCA4+fevXtLbGysdOvWTbfKDBs2TNwlIyND0tPTnVpg1OBfNZZGDRZGw1Kv+mNMSEiQwMBATxfHp1EXP8+NmZtc+j9NdcCcvttfymv8XLZfNB514X11keCG7yh7D4rHz8R77bXXSqdOneTQoUM6wKixMSdOnHDapqqqSs9Mso+bUbclJSVO29jvX2psjRp3o5a61BvLAaBxeM+8B3XRNOXVrj+4qQOmO/aLxqMuvIc7vqMauj+3nwfm22+/lR9++EE6d+6s78fHx8upU6f07CK7rVu3Sk1NjcTFxTm2UTOTaveDqf+Ndu/evd7uIwAA4FsaHWDU+VrUjCC1KEeOHNE/FxUV6XVTpkyRnTt3ytGjR2XLli1y1113yXXXXacH4So9evTQ42QmTpwou3btkk8++UQmTZqku57UDCQlOTlZD+BV54dR061Xr14tCxYscOoiAgAAvqvRXUi7d++WoUOHOu7bQ0VKSoosXrxYn4DujTfe0K0sKpCoMShZWVlO3TtqmrQKLapLSc0+GjNmjLzyyiuO9WFhYXrwbVpamvTv3193Qc2YMYMp1IAHmXKROQC+odEBZsiQIWJZl57CtmnTlQfPqRlHK1euvOw2avDv9u3bG1s8AADgA7gWEgAAMA4BBgAAGIcAAwAAjEOAAQAAxiHAAAAA4xBgAACAcQgwAADAOAQYAABgHAIMAAAwDgEGAAAYhwADAACMQ4ABAADGIcAAAADjEGAAAIBxCDAAAMA4BBgAAGAcAgwAADAOAQYAABiHAAMAAIxDgAEAAMYhwAAAAOMQYAAAgHEIMAAAwDgEGAAAYBwCDAAAMA4BBgAAGIcAAwAAjEOAAQAAxiHAAAAA4xBgAACAcQgwAADAOAQYAABgHAIMAAAwDgEGAAAYhwADAACMQ4ABAADGIcAAAADjEGAAAIBxCDAAAMA4BBgAANDyA8y2bdskKSlJoqKixM/PT9auXetYV1lZKVOnTpXevXtLmzZt9Dbjx4+X7777zmkfXbp00c+tvcyZM8dpmz179sigQYMkODhYoqOjJScn5+f8ngAAwJcDzLlz56RPnz6yaNGii9aVlZXJ559/LtOnT9e3a9askYMHD8qdd9550bazZs2S48ePO5Ynn3zSsa60tFRGjBghMTExUlBQIHPnzpXMzExZsmRJU35HAADQwrRq7BNGjhypl/qEhYVJXl6e02N/+ctfZODAgVJUVCTXXHON4/G2bdtKZGRkvftZsWKFVFRUyLJlyyQoKEh69eolhYWFMm/ePElNTW1skQEAgK8HmMY6ffq07iJq166d0+OqyygrK0uHmuTkZJk8ebK0avW/4uTn58vgwYN1eLFLTEyUF198UU6ePCnt27e/6HXKy8v1UrsVx96tpRZcmf194v3yPG+sC1uAJb7I5m853cJzqAvvYbtQB+74jmroPt0aYM6fP6/HxDz44IMSGhrqePypp56Sm266STp06CA7duyQjIwM3Y2kWliU4uJi6dq1q9O+IiIiHOvqCzDZ2dkyc+bMix7Pzc2VkJAQN/x2LVfdVjR4jjfVRc5A8WlZA2o8XQRcQF14D3d8R6nhKB4NMCpB3XfffWJZlixevNhpXXp6uuPn2NhY3dLy6KOP6hBis9ma9HoqBNXer2qBUYN/1Via2uEJl68z9ceYkJAggYGBni6OT/PGurgxc5P46v801QFz+m5/Ka/x83RxfBp14X11keCG7yh7D4pHAow9vBw7dky2bt16xQARFxcnVVVVcvToUenevbseG1NSUuK0jf3+pcbNqOBTX/hRb6y3HABMwXvmPbypLsqrffuAoQ6Yvv4eeAvqwnu44zuqofvzd1d4+eqrr2Tz5s3SsWPHKz5HDdD19/eX8PBwfT8+Pl5P167dD6b+N6rCTX3dRwAAwLc0ugXm7NmzcujQIcf9I0eO6ACixrN07txZfvOb3+gp1OvWrZPq6mo9ZkVR61VXkRqg++mnn8rQoUP1TCR1Xw3gfeihhxzhRA3qVeNZJkyYoMfQ7Nu3TxYsWCAvv/yyK393AADgKwFm9+7dOnzY2cedpKSk6HO1vP/++/p+3759nZ73wQcfyJAhQ3Q3z6pVq/S2ataQGqyrAkzt8StqOrYafJuWlib9+/eXTp06yYwZM5hCDQAAmhZgVAhRA3Mv5XLrFDX7aOfOnVd8HTW4d/v27Y0tHgAA8AFcCwkAABiHAAMAAIxDgAEAAMYhwAAAAOMQYAAAgHEIMAAAwDgEGAAAYBwCDAAAMA4BBgAAGIcAAwAAjEOAAQAAxiHAAAAA4xBgAACAcQgwAADAOAQYAABgHAIMAAAwDgEGAAAYhwADAACMQ4ABAADGIcAAAADjEGAAAIBxCDAAAMA4BBgAAGAcAgwAADAOAQYAABiHAAMAAIxDgAEAAMYhwAAAAOMQYAAAgHEIMAAAwDgEGAAAYBwCDAAAMA4BBgAAGIcAAwAAjEOAAQAAxiHAAAAA4xBgAACAcQgwAADAOAQYAADQ8gPMtm3bJCkpSaKiosTPz0/Wrl3rtN6yLJkxY4Z07txZWrduLcOHD5evvvrKaZsff/xRxo4dK6GhodKuXTuZMGGCnD171mmbPXv2yKBBgyQ4OFiio6MlJyenqb8jAADw9QBz7tw56dOnjyxatKje9SpovPLKK/Laa6/Jp59+Km3atJHExEQ5f/68YxsVXvbv3y95eXmybt06HYpSU1Md60tLS2XEiBESExMjBQUFMnfuXMnMzJQlS5Y09fcEAAAtSKvGPmHkyJF6qY9qfZk/f75MmzZN7rrrLv3Y3//+d4mIiNAtNQ888IB88cUXsnHjRvnss89kwIABepuFCxfKqFGj5KWXXtItOytWrJCKigpZtmyZBAUFSa9evaSwsFDmzZvnFHQAAIBvanSAuZwjR45IcXGx7jayCwsLk7i4OMnPz9cBRt2qbiN7eFHU9v7+/rrF5p577tHbDB48WIcXO9WK8+KLL8rJkyelffv2F712eXm5Xmq34iiVlZV6wZXZ3yfeL8/zxrqwBVjii2z+ltMtPIe68B62C3Xgju+ohu7TpQFGhRdFtbjUpu7b16nb8PBw50K0aiUdOnRw2qZr164X7cO+rr4Ak52dLTNnzrzo8dzcXAkJCfnZv5svUV178A7eVBc5A8WnZQ2o8XQRcAF14T3c8R1VVlbW/AHGkzIyMiQ9Pd2pBUYN/lVjadRgYTQs9ao/xoSEBAkMDPR0cbxH9tXN/pKV/sGS1/sVSdj7lATW/DR+7JIyvnV7mW7M3CS++j9NdcCcvttfymv8PF0cn0ZdeF9dJLjheGHvQWnWABMZGalvS0pK9CwkO3W/b9++jm1OnDjh9Lyqqio9M8n+fHWrnlOb/b59m7psNpte6lJvLAfjxuE9q6MhAcJNVHhpUIBphvoqr/btA4Y6YPr6e+AtqAvv4Y7jRUP359LzwKhuHxUwtmzZ4pSk1NiW+Ph4fV/dnjp1Ss8ustu6davU1NTosTL2bdTMpNr9YKploHv37vV2HwEAAN/S6ACjzteiZgSpxT5wV/1cVFSkzwvzhz/8QV544QV5//33Ze/evTJ+/Hg9s+juu+/W2/fo0UPuuOMOmThxouzatUs++eQTmTRpkh7gq7ZTkpOT9QBedX4YNd169erVsmDBAqcuIgAA4Lsa3YW0e/duGTp0qOO+PVSkpKTI8uXL5emnn9bnilHTnVVLy6233qqnTasT0tmpadIqtAwbNkzPPhozZow+d0ztmUtq8G1aWpr0799fOnXqpE+OxxRqAADQpAAzZMgQfb6XS1GtMLNmzdLLpagZRytXrrzs68TGxsr27dupJQAAcBGuhQQAAIxDgAEAAMYhwAAAAOMQYAAAgHEIMAAAwDgEGAAAYBwCDAAAMA4BBgAAGIcAAwAAjEOAAQAAxiHAAACAln8tJIhIZpgYIfO0p0sAAIBb0AIDAACMQ4ABAADGIcAAAADjEGAAAIBxCDAAAMA4BBgAAGAcAgwAADAOAQYAABiHAAMAAIxDgAEAAMYhwAAAAOMQYAAAgHEIMAAAwDgEGAAAYBwCDAAAMA4BBgAAGIcAAwAAjEOAAQAAxiHAAAAA4xBgAACAcQgwAADAOAQYAABgHAIMAAAwDgEGAAAYhwADAACMQ4ABAADGIcAAAADjEGAAAIBxXB5gunTpIn5+fhctaWlpev2QIUMuWvfYY4857aOoqEhGjx4tISEhEh4eLlOmTJGqqipXFxUAABiqlat3+Nlnn0l1dbXj/r59+yQhIUF++9vfOh6bOHGizJo1y3FfBRU79VwVXiIjI2XHjh1y/PhxGT9+vAQGBsrs2bNdXVwAAGAglweYq666yun+nDlzpFu3bnLbbbc5BRYVUOqTm5srBw4ckM2bN0tERIT07dtXsrKyZOrUqZKZmSlBQUGuLjIAAPD1AFNbRUWFvPnmm5Kenq67iuxWrFihH1chJikpSaZPn+5ohcnPz5fevXvr8GKXmJgojz/+uOzfv1/69etX72uVl5frxa60tFTfVlZW6sWl/IPFCI38ve3vk8vfL9N5oL4rL7ym/fbKT3B/ndkCLPFFNn/L6RaeQ114D9uFOnDH8aKh+/SzLMttfwlvv/22JCcn6zEtUVFR+rElS5ZITEyMvr9nzx7dsjJw4EBZs2aNXp+amirHjh2TTZs2OfZTVlYmbdq0kQ0bNsjIkSPrfS3VOjNz5syLHl+5cqVTFxUAAPBe6pivssPp06clNDTUMy0wS5cu1YHDHl7sAcVOtbR07txZhg0bJocPH9ZdTU2VkZGhW3pqt8BER0fLiBEjLvsGNEn21WKEjG8bnXrz8vL0mCU15gieq2/V8pLX+xVJ2PuUBNacd3ldN8WNmT/9p8LX/qeZNaBGpu/2l/Kan1qS0fyoC++riwQ3HC/sPShX4rYAo1pR1DgWe8vKpcTFxenbQ4cO6QCjupV27drltE1JSYm+vdS4GcVms+mlLvXGuvxg3JADijdo4u/tlvfMZB6sbxVeGhRgmqG+yqt9+4ChDpi+/h54C+rCe7jjeNHQ/bntPDCvv/66ngKtZhRdTmFhob5VLTFKfHy87N27V06cOOHYRrUKqFaUnj17uqu4AADAIG5pgampqdEBJiUlRVq1+uklVDeRGpMyatQo6dixox4DM3nyZBk8eLDExsbqbVSXjwoq48aNk5ycHCkuLpZp06bp88jU18ICAAB8j1sCjOo6UgN3f//73zs9rqZAq3Xz58+Xc+fO6TEqY8aM0QHFLiAgQNatW6dnHanWGDV4VwWh2ueNAQAAvs0tAUa1otQ3uUkFlo8++uiKz1ezlNSMIwAAgPpwLSQAAGAcAgwAADAOAQYAABiHAAMAAIxDgAEAAMYhwAAAAOMQYAAAgHEIMAAAwDgEGAAAYBwCDAAAMA4BBgAAGIcAAwAAjEOAAQAAxiHAAAAA4xBgAACAcQgwAADAOAQYAABgHAIMAAAwDgEGAAAYhwADAACMQ4ABAADGIcAAAADjEGAAAIBxCDAAAMA4BBgAAGAcAgwAADAOAQYAABiHAAMAAIzTytMFAOACmWFuf4mjwT/v+V3Or3RVUQCAFhgAAGAeAgwAADAOAQYAABiHMTAtWJdn1jdqe1uAJTkDRW7M3CTl1X76saNzRrupdAAANB0tMAAAwDgEGAAAYBwCDAAAMA4BBgAAGIcAAwAAjEOAAQAAxnF5gMnMzBQ/Pz+n5YYbbnCsP3/+vKSlpUnHjh3lF7/4hYwZM0ZKSkqc9lFUVCSjR4+WkJAQCQ8PlylTpkhVVZWriwoAAAzllvPA9OrVSzZv3vzTi7T66WUmT54s69evl3feeUfCwsJk0qRJcu+998onn3yi11dXV+vwEhkZKTt27JDjx4/L+PHjJTAwUGbPnu2O4gIAAMO4JcCowKICSF2nT5+WpUuXysqVK+X222/Xj73++uvSo0cP2blzp/z617+W3NxcOXDggA5AERER0rdvX8nKypKpU6fq1p2goCB3FBkAAPh6gPnqq68kKipKgoODJT4+XrKzs+Waa66RgoICqayslOHDhzu2Vd1Lal1+fr4OMOq2d+/eOrzYJSYmyuOPPy779++Xfv361fua5eXlerErLS3Vt+r11OJS/j/zsrzNRJ1Zt1Hb+1tOt4rL3zsTeaC+Ky+8pv22JWjs36O3qO9zAc+gLryH7UIduOMY0dB9ujzAxMXFyfLly6V79+66+2fmzJkyaNAg2bdvnxQXF+sWlHbt2jk9R4UVtU5Rt7XDi329fd2lqJCkXqsu1aKjxtK4VJ8lYoIcqW7S87IG1Dh+3rBhgwtLZCgP1nde71ekpWjq36O3qP25gGdRF94jLy/P5fssKyvzTIAZOXKk4+fY2FgdaGJiYuTtt9+W1q1bi7tkZGRIenq6UwtMdHS0jBgxQkJDQ137YtlXiwluLF/a6EStvhim7/aX8pr/XQtpX2aim0pnEA/Ut2p5UeElYe9TElhzXlqCxv49eov6PhfwDOrC++oiISFBj1F1JXsPiscv5qhaW66//no5dOiQ/kUrKirk1KlTTq0wahaSfcyMut21a5fTPuyzlOobV2Nns9n0Upd6Y1395oohBxT7BRkb/bwaP8dzXf7emciD9a3CS0sJME39e/QWtT8X8Czqwnu44xjb0P25/TwwZ8+elcOHD0vnzp2lf//+umBbtmxxrD948KCeNq3Gyijqdu/evXLixAmnJirVitKzZ093FxcAABjA5S0wf/rTnyQpKUl3G3333Xfy/PPPS0BAgDz44IN62vSECRN0V0+HDh10KHnyySd1aFEDeBXV5aOCyrhx4yQnJ0ePe5k2bZo+d0x9LSwAAMD3uDzAfPvttzqs/PDDD3LVVVfJrbfeqqdIq5+Vl19+Wfz9/fUJ7NSsITXD6NVXX3U8X4WddevW6VlHKti0adNGUlJSZNasWa4uKgAAMJTLA8yqVasuu15NrV60aJFeLkW13jD7BQAAXArXQgIAAMYhwAAAAOMQYAAAgHEIMAAAwDgEGAAAYBwCDAAAMA4BBgAAGIcAAwAAjEOAAQAAxiHAAAAA4xBgAACAcQgwAADAOAQYAABgHJdfjRotS5dn1ospjs4Z7ekiAACaCS0wAADAOLTAAAB80tHgZPF2Xc6v9HQRvBYtMAAAwDgEGAAAYBwCDAAAMA5jYAAARowtqfQPlg2yRPbZJkhgzXm3vAbMQQsMAAAwDgEGAAAYhy4ktBjuOune0WC37BYA8DPQAgMAAIxDgAEAAMYhwAAAAOMQYAAAgHEIMAAAwDgEGAAAYBwCDAAAMA4BBgAAGIcAAwAAjEOAAQAAxiHAAAAA4xBgAACAcQgwAADAOAQYAABgHAIMAAAwDgEGAAAYhwADAACM4/IAk52dLb/61a+kbdu2Eh4eLnfffbccPHjQaZshQ4aIn5+f0/LYY485bVNUVCSjR4+WkJAQvZ8pU6ZIVVWVq4sLAAAM1MrVO/zoo48kLS1NhxgVOJ599lkZMWKEHDhwQNq0aePYbuLEiTJr1izHfRVU7Kqrq3V4iYyMlB07dsjx48dl/PjxEhgYKLNnz3Z1kQEAgK8HmI0bNzrdX758uW5BKSgokMGDBzsFFhVQ6pObm6sDz+bNmyUiIkL69u0rWVlZMnXqVMnMzJSgoCBXFxsAAPhygKnr9OnT+rZDhw5Oj69YsULefPNNHWKSkpJk+vTpjlaY/Px86d27tw4vdomJifL444/L/v37pV+/fhe9Tnl5uV7sSktL9W1lZaVeXMo/WExgC7Aat72/5XSL/6n0QH3bX9MTr+0tf4/egs9F47nr77Ylfi5M/dzYLnweXH58bcQ+/SzLctu7U1NTI3feeaecOnVKPv74Y8fjS5YskZiYGImKipI9e/bolpWBAwfKmjVr9PrU1FQ5duyYbNq0yfGcsrIy3QW1YcMGGTly5EWvpVpmZs6cedHjK1eudOqeAgAA3ksd75OTk3UDSGhoqGdaYNRYmH379jmFF3tAsVMtLZ07d5Zhw4bJ4cOHpVu3bk16rYyMDElPT3dqgYmOjtbjby73BjRJ9tVighvLlzY6UWcNqJHpu/2lvMbPbeUyzT7bhGZ/TfU/zLzer0jC3qcksOa8tASN/Xv0FnwuvOcz0xI/F6Z+bmwXPhcJCQl6fKor2XtQrsRtAWbSpEmybt062bZtm1x99eUP+HFxcfr20KFDOsCobqVdu3Y5bVNSUqJvLzVuxmaz6aUu9ca6+s0VQz445dVN+7JVX9JNfW5L5MkvSvXaLeWL2vS/KT4XDefuv9mW9Lm4Em//mwt0wzG2oftz+TRq1SOlwsu7774rW7dula5du17xOYWFhfpWtcQo8fHxsnfvXjlx4oRjm7y8PN2S0rNnT1cXGQAAGKaVO7qN1LiT9957T58Lpri4WD8eFhYmrVu31t1Eav2oUaOkY8eOegzM5MmT9Qyl2NhYva3q9lFBZdy4cZKTk6P3MW3aNL3v+lpZAACAb3F5C8zixYv1wBt1sjrVomJfVq9erderKdBqerQKKTfccIP88Y9/lDFjxsg///lPxz4CAgJ095O6Va0xDz30kD4PTO3zxgAAAN/l8haYK01qUgNr1cnurkTNUlIzjgAAAOriWkgAAMA4BBgAAGAcAgwAADAOAQYAABiHAAMAAIxDgAEAAMYhwAAAAOMQYAAAgHHcejVqAIBrHQ1O9nQRAK9ACwwAADAOAQYAABiHAAMAAIxDgAEAAMYhwAAAAOMQYAAAgHEIMAAAwDgEGAAAYBwCDAAAMA4BBgAAGIcAAwAAjEOAAQAAxiHAAAAA4xBgAACAcQgwAADAOAQYAABgHAIMAAAwDgEGAAAYhwADAACMQ4ABAADGIcAAAADjEGAAAIBxCDAAAMA4BBgAAGAcAgwAADAOAQYAABinlacLAN92NDjZ00VAMzGhrrucX+npIgBoIAIMAFwmZFX6B8sGWSL7bBMksOa8R8oF4GJ0IQEAAOMQYAAAgHG8ugtp0aJFMnfuXCkuLpY+ffrIwoULZeDAgZ4uVosdc0BTOQDAFF7bArN69WpJT0+X559/Xj7//HMdYBITE+XEiROeLhoAAPAwrw0w8+bNk4kTJ8rvfvc76dmzp7z22msSEhIiy5Yt83TRAACAh3llF1JFRYUUFBRIRkaG4zF/f38ZPny45Ofn1/uc8vJyvdidPn1a3/74449SWVnp4gIGSUtU6R8kZWVl8kNFkATW1Hi6OD6NuvAe1IX38MW6aFV1TrxRqxpLyspq5IcffpDAwECX7vvMmTP61rKsy5dBvND3338v1dXVEhER4fS4uv/ll1/W+5zs7GyZOXPmRY937drVbeVsmbz/XB2+g7rwHtSF9/C1urhffLUmzpw5I2FhYWYFmKZQrTVqzIxdTU2Nbn3p2LGj+Pn5ebRspigtLZXo6Gj55ptvJDQ01NPF8WnUhfegLrwHdeEbdWFZlg4vUVFRl93OKwNMp06dJCAgQEpKSpweV/cjIyPrfY7NZtNLbe3atXNrOVsq9cfIl4N3oC68B3XhPagL7+Guurhcy4tXD+INCgqS/v37y5YtW5xaVNT9+Ph4j5YNAAB4nle2wCiqOyglJUUGDBigz/0yf/58OXfunJ6VBAAAfJvXBpj7779f/vvf/8qMGTP0iez69u0rGzduvGhgL1xHdcGp8+7U7YpD86MuvAd14T2oC+9h84K68LOuNE8JAADAy3jlGBgAAIDLIcAAAADjEGAAAIBxCDAAAMA4BBgfs2jRIunSpYsEBwdLXFyc7Nq165Lb/vWvf5VBgwZJ+/bt9aKuRXW57eG+uqht1apV+uzSd999t9vL6CsaWxenTp2StLQ06dy5s56Fcf3118uGDRuarbwtWWPrQp1io3v37tK6dWt9ZtjJkyfL+fPnm628LdW2bdskKSlJnw1Xfd+sXbv2is/58MMP5aabbtKfieuuu06WL1/u3kKqWUjwDatWrbKCgoKsZcuWWfv377cmTpxotWvXziopKal3++TkZGvRokXWv/71L+uLL76wHn74YSssLMz69ttvm73svl4XdkeOHLF++ctfWoMGDbLuuuuuZitvS9bYuigvL7cGDBhgjRo1yvr44491nXz44YdWYWFhs5fd1+tixYoVls1m07eqHjZt2mR17tzZmjx5crOXvaXZsGGD9dxzz1lr1qxRM5Wtd99997Lbf/3111ZISIiVnp5uHThwwFq4cKEVEBBgbdy40W1lJMD4kIEDB1ppaWmO+9XV1VZUVJSVnZ3doOdXVVVZbdu2td544w03ltI3NKUu1Pt/8803W3/729+slJQUAoyH6mLx4sXWtddea1VUVDRjKX1DY+tCbXv77bc7PaYOoLfccovby+pLpAEB5umnn7Z69erl9Nj9999vJSYmuq1cdCH5iIqKCikoKNDdQHb+/v76fn5+foP2oS5jX1lZKR06dHBjSVu+ptbFrFmzJDw8XCZMmNBMJW35mlIX77//vr6kiepCUifWvPHGG2X27NlSXV3djCVveZpSFzfffLN+jr2b6euvv9ZdeaNGjWq2cuN/VB3VrjslMTGxwceXFnUmXrjW999/r79g657JWN3/8ssvG7SPqVOn6v7Qun+kcH9dfPzxx7J06VIpLCxsplL6hqbUhTpIbt26VcaOHasPlocOHZInnnhCh3t1ZlI0X10kJyfr59166636CsZVVVXy2GOPybPPPttMpYadOmN+fXWnrlr9f//3f3qMkqvRAoMGmTNnjh48+u677+rBdWg+6rLy48aN04Oq1ZXa4VnqwrKqJWzJkiX6orPqsifPPfecvPbaa54ums9Rg0ZV69err74qn3/+uaxZs0bWr18vWVlZni4amgEtMD5CHfgCAgKkpKTE6XF1PzIy8rLPfemll3SA2bx5s8TGxrq5pC1fY+vi8OHDcvToUT0joPZBVGnVqpUcPHhQunXr1gwlb3ma8rlQM48CAwP18+x69Oih/wequkGCgoLcXu6WqCl1MX36dB3uH3nkEX2/d+/e+qK/qampOlSqLig0D1VH9dVdaGioW1pfFGrXR6gvVfW/xS1btjgdBNV91Z9/KTk5Ofp/M+pCmurK4Gj+urjhhhtk7969uvvIvtx5550ydOhQ/bOaOorm+1zccsstutvIHiKV//znPzrYEF6aty7UuLy6IcUeLLnMX/NSdVS77pS8vLzLHl9+NrcND4ZXTlFUUw6XL1+up7mlpqbqKYrFxcV6/bhx46xnnnnGsf2cOXP0lMZ//OMf1vHjxx3LmTNnPPhb+GZd1MUsJM/VRVFRkZ6NN2nSJOvgwYPWunXrrPDwcOuFF17w4G/hm3Xx/PPP67p466239DTe3Nxcq1u3btZ9993nwd+iZThz5ow+hYZaVFSYN2+e/vnYsWN6vaoHVR91p1FPmTJFn3ZDnYKDadRwKTU3/5prrtHBRE1Z3Llzp2Pdbbfdpg+MdjExMfoPt+6ivjTQvHVRFwHGs3WxY8cOKy4uTh9s1ZTqP//5z3qaO5q3LiorK63MzEwdWoKDg63o6GjriSeesE6ePOmh0rccH3zwQb3f//b3X92q+qj7nL59++q6U5+L119/3a1l9FP/uK99BwAAwPUYAwMAAIxDgAEAAMYhwAAAAOMQYAAAgHEIMAAAwDgEGAAAYBwCDAAAMA4BBgAAGIcAAwAAjEOAAQAAxiHAAAAA4xBgAACAmOb/AWgSC8faqioQAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["inputs_df.loc[inputs_df[\"left\"] == 0][\"satisfaction_level\"].hist()\n", "inputs_df.loc[inputs_df[\"left\"] == 1][\"satisfaction_level\"].hist()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 142, "id": "0ec0487d", "metadata": {}, "outputs": [{"data": {"text/plain": ["satisfaction_level       float64\n", "last_evaluation          float64\n", "number_project             int64\n", "average_montly_hours       int64\n", "time_spend_company         int64\n", "Work_accident              int64\n", "left                       int64\n", "promotion_last_5years      int64\n", "sales                     object\n", "salary                    object\n", "dtype: object"]}, "execution_count": 142, "metadata": {}, "output_type": "execute_result"}], "source": ["inputs_df.dtypes"]}, {"cell_type": "code", "execution_count": 143, "id": "a3344ec9", "metadata": {}, "outputs": [], "source": ["## Dummy variables\n", "ys, Xs = dmatrices(\"left~satisfaction_level+last_evaluation+number_project+average_montly_hours+time_spend_company+Work_accident+C(promotion_last_5years)+C(sales)+C(salary)\",\n", "                   inputs_df,\n", "                   return_type=\"dataframe\",)"]}, {"cell_type": "code", "execution_count": 144, "id": "e554653e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>left</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   left\n", "0   1.0\n", "1   1.0\n", "2   1.0\n", "3   1.0\n", "4   1.0"]}, "execution_count": 144, "metadata": {}, "output_type": "execute_result"}], "source": ["ys.head()"]}, {"cell_type": "code", "execution_count": 145, "id": "6490b849", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Intercept</th>\n", "      <th>C(promotion_last_5years)[T.1]</th>\n", "      <th>C(sales)[<PERSON>.<PERSON>]</th>\n", "      <th>C(sales)[T.accounting]</th>\n", "      <th>C(sales)[T.hr]</th>\n", "      <th>C(sales)[T.management]</th>\n", "      <th>C(sales)[T.marketing]</th>\n", "      <th>C(sales)[T.product_mng]</th>\n", "      <th>C(sales)[T.sales]</th>\n", "      <th>C(sales)[T.support]</th>\n", "      <th>C(sales)[T.technical]</th>\n", "      <th>C(salary)[T.low]</th>\n", "      <th>C(salary)[T.medium]</th>\n", "      <th>satisfaction_level</th>\n", "      <th>last_evaluation</th>\n", "      <th>number_project</th>\n", "      <th>average_montly_hours</th>\n", "      <th>time_spend_company</th>\n", "      <th>Work_accident</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.38</td>\n", "      <td>0.53</td>\n", "      <td>2.0</td>\n", "      <td>157.0</td>\n", "      <td>3.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.80</td>\n", "      <td>0.86</td>\n", "      <td>5.0</td>\n", "      <td>262.0</td>\n", "      <td>6.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.11</td>\n", "      <td>0.88</td>\n", "      <td>7.0</td>\n", "      <td>272.0</td>\n", "      <td>4.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.72</td>\n", "      <td>0.87</td>\n", "      <td>5.0</td>\n", "      <td>223.0</td>\n", "      <td>5.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.37</td>\n", "      <td>0.52</td>\n", "      <td>2.0</td>\n", "      <td>159.0</td>\n", "      <td>3.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Intercept  C(promotion_last_5years)[T.1]  C(sales)[T.Rand<PERSON>]  \\\n", "0        1.0                            0.0                0.0   \n", "1        1.0                            0.0                0.0   \n", "2        1.0                            0.0                0.0   \n", "3        1.0                            0.0                0.0   \n", "4        1.0                            0.0                0.0   \n", "\n", "   C(sales)[T.accounting]  C(sales)[T.hr]  C(sales)[T.management]  \\\n", "0                     0.0             0.0                     0.0   \n", "1                     0.0             0.0                     0.0   \n", "2                     0.0             0.0                     0.0   \n", "3                     0.0             0.0                     0.0   \n", "4                     0.0             0.0                     0.0   \n", "\n", "   C(sales)[T.marketing]  C(sales)[T.product_mng]  C(sales)[T.sales]  \\\n", "0                    0.0                      0.0                1.0   \n", "1                    0.0                      0.0                1.0   \n", "2                    0.0                      0.0                1.0   \n", "3                    0.0                      0.0                1.0   \n", "4                    0.0                      0.0                1.0   \n", "\n", "   C(sales)[T.support]  C(sales)[T.technical]  C(salary)[T.low]  \\\n", "0                  0.0                    0.0               1.0   \n", "1                  0.0                    0.0               0.0   \n", "2                  0.0                    0.0               0.0   \n", "3                  0.0                    0.0               1.0   \n", "4                  0.0                    0.0               1.0   \n", "\n", "   C(salary)[T.medium]  satisfaction_level  last_evaluation  number_project  \\\n", "0                  0.0                0.38             0.53             2.0   \n", "1                  1.0                0.80             0.86             5.0   \n", "2                  1.0                0.11             0.88             7.0   \n", "3                  0.0                0.72             0.87             5.0   \n", "4                  0.0                0.37             0.52             2.0   \n", "\n", "   average_montly_hours  time_spend_company  Work_accident  \n", "0                 157.0                 3.0            0.0  \n", "1                 262.0                 6.0            0.0  \n", "2                 272.0                 4.0            0.0  \n", "3                 223.0                 5.0            0.0  \n", "4                 159.0                 3.0            0.0  "]}, "execution_count": 145, "metadata": {}, "output_type": "execute_result"}], "source": ["Xs.head()"]}, {"cell_type": "code", "execution_count": 146, "id": "060628d3", "metadata": {}, "outputs": [{"data": {"text/plain": ["Intercept                        float64\n", "C(promotion_last_5years)[T.1]    float64\n", "C(sales)[T.<PERSON>]                float64\n", "C(sales)[T.accounting]           float64\n", "C(sales)[T.hr]                   float64\n", "C(sales)[T.management]           float64\n", "C(sales)[T.marketing]            float64\n", "C(sales)[T.product_mng]          float64\n", "C(sales)[T.sales]                float64\n", "C(sales)[T.support]              float64\n", "C(sales)[T.technical]            float64\n", "C(salary)[T.low]                 float64\n", "C(salary)[T.medium]              float64\n", "satisfaction_level               float64\n", "last_evaluation                  float64\n", "number_project                   float64\n", "average_montly_hours             float64\n", "time_spend_company               float64\n", "Work_accident                    float64\n", "dtype: object"]}, "execution_count": 146, "metadata": {}, "output_type": "execute_result"}], "source": ["Xs.dtypes"]}, {"cell_type": "code", "execution_count": 147, "id": "20e8478e", "metadata": {}, "outputs": [{"data": {"text/plain": ["((14999, 19), (14999, 1))"]}, "execution_count": 147, "metadata": {}, "output_type": "execute_result"}], "source": ["Xs, ys = Xs.values, ys.values\n", "Xs.shape, ys.shape"]}, {"cell_type": "code", "execution_count": 148, "id": "c139d283", "metadata": {}, "outputs": [{"data": {"text/plain": ["(14999,)"]}, "execution_count": 148, "metadata": {}, "output_type": "execute_result"}], "source": ["ys = ys.reshape(-1)\n", "ys.shape"]}, {"cell_type": "code", "execution_count": 149, "id": "cbc6c8b1", "metadata": {}, "outputs": [{"data": {"text/plain": ["((11999, 19), (3000, 19), (11999,), (3000,))"]}, "execution_count": 149, "metadata": {}, "output_type": "execute_result"}], "source": ["X_train, X_test, y_train, y_test = train_test_split(Xs, ys, test_size=0.2, random_state=42)\n", "X_train.shape, X_test.shape, y_train.shape, y_test.shape"]}, {"cell_type": "markdown", "id": "beaec364", "metadata": {}, "source": ["# Sklearn API"]}, {"cell_type": "code", "execution_count": 150, "id": "4a756fda", "metadata": {}, "outputs": [{"data": {"text/html": ["<style>#sk-container-id-6 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: #000;\n", "  --sklearn-color-text-muted: #666;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-6 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-6 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-6 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-6 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-6 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-6 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-6 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-6 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-6 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-6 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-6 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-6 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-6 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-6 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-6 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: flex;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "  align-items: start;\n", "  justify-content: space-between;\n", "  gap: 0.5em;\n", "}\n", "\n", "#sk-container-id-6 label.sk-toggleable__label .caption {\n", "  font-size: 0.6rem;\n", "  font-weight: lighter;\n", "  color: var(--sklearn-color-text-muted);\n", "}\n", "\n", "#sk-container-id-6 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-6 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-6 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-6 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-6 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-6 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-6 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-6 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-6 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-6 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-6 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-6 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-6 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-6 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-6 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-6 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-6 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-6 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-6 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-6 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-6 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-6 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 0.5em;\n", "  text-align: center;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-6 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-6 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-6 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-6 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-6\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>LogisticRegression(fit_intercept=False, max_iter=10000)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-6\" type=\"checkbox\" checked><label for=\"sk-estimator-id-6\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>LogisticRegression</div></div><div><a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.6/modules/generated/sklearn.linear_model.LogisticRegression.html\">?<span>Documentation for LogisticRegression</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></div></label><div class=\"sk-toggleable__content fitted\"><pre>LogisticRegression(fit_intercept=False, max_iter=10000)</pre></div> </div></div></div></div>"], "text/plain": ["LogisticRegression(fit_intercept=False, max_iter=10000)"]}, "execution_count": 150, "metadata": {}, "output_type": "execute_result"}], "source": ["model = LogisticRegression(max_iter=10000, fit_intercept=False)\n", "model.fit(X_train, y_train)"]}, {"cell_type": "code", "execution_count": 151, "id": "27f45bed", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[0.********, 0.********],\n", "       [0.********, 0.********]])"]}, "execution_count": 151, "metadata": {}, "output_type": "execute_result"}], "source": ["model.predict_proba(Xs[0:2])"]}, {"cell_type": "code", "execution_count": 152, "id": "d6889d66", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['Intercept', 'C(promotion_last_5years)[T.1]', 'C(sales)[T.RandD]',\n", "       'C(sales)[T.accounting]', 'C(sales)[T.hr]', 'C(sales)[T.management]',\n", "       'C(sales)[T.marketing]', 'C(sales)[T.product_mng]', 'C(sales)[T.sales]',\n", "       'C(sales)[T.support]', 'C(sales)[T.technical]', 'C(salary)[T.low]',\n", "       'C(salary)[T.medium]', 'satisfaction_level', 'last_evaluation',\n", "       'number_project', 'average_montly_hours', 'time_spend_company',\n", "       'Work_accident'],\n", "      dtype='object')"]}, "execution_count": 152, "metadata": {}, "output_type": "execute_result"}], "source": ["ys, Xs = dmatrices(\"left~satisfaction_level+last_evaluation+number_project+average_montly_hours+time_spend_company+Work_accident+C(promotion_last_5years)+C(sales)+C(salary)\",\n", "                   inputs_df,\n", "                   return_type=\"dataframe\",)\n", "Xs.columns"]}, {"cell_type": "code", "execution_count": 153, "id": "eb630915", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([-1.********, -1.********, -0.********,  0.********,  0.********,\n", "       -0.30940526,  0.17000023,  0.0619149 ,  0.10124522,  0.16034375,\n", "        0.2176953 ,  1.91458304,  1.40960183, -4.13894286,  0.60319967,\n", "       -0.30700624,  0.00495846,  0.26555406, -1.5082644 ])"]}, "execution_count": 153, "metadata": {}, "output_type": "execute_result"}], "source": ["coef = model.coef_[0]\n", "coef"]}, {"cell_type": "code", "execution_count": 154, "id": "bd03cf87", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>feature</th>\n", "      <th>coef</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>C(salary)[T.low]</td>\n", "      <td>1.914583</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>C(salary)[T.medium]</td>\n", "      <td>1.409602</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>last_evaluation</td>\n", "      <td>0.603200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>C(sales)[T.hr]</td>\n", "      <td>0.395640</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>time_spend_company</td>\n", "      <td>0.265554</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>C(sales)[T.technical]</td>\n", "      <td>0.217695</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>C(sales)[T.marketing]</td>\n", "      <td>0.170000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>C(sales)[T.support]</td>\n", "      <td>0.160344</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>C(sales)[T.accounting]</td>\n", "      <td>0.129325</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>C(sales)[T.sales]</td>\n", "      <td>0.101245</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>C(sales)[T.product_mng]</td>\n", "      <td>0.061915</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>average_montly_hours</td>\n", "      <td>0.004958</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>number_project</td>\n", "      <td>-0.307006</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>C(sales)[T.management]</td>\n", "      <td>-0.309405</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>C(sales)[T.<PERSON>]</td>\n", "      <td>-0.542008</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>C(promotion_last_5years)[T.1]</td>\n", "      <td>-1.327476</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>Work_accident</td>\n", "      <td>-1.508264</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Intercept</td>\n", "      <td>-1.628679</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>satisfaction_level</td>\n", "      <td>-4.138943</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                          feature      coef\n", "11               C(salary)[T.low]  1.914583\n", "12            C(salary)[T.medium]  1.409602\n", "14                last_evaluation  0.603200\n", "4                  C(sales)[T.hr]  0.395640\n", "17             time_spend_company  0.265554\n", "10          C(sales)[T.technical]  0.217695\n", "6           C(sales)[T.marketing]  0.170000\n", "9             C(sales)[T.support]  0.160344\n", "3          C(sales)[T.accounting]  0.129325\n", "8               C(sales)[T.sales]  0.101245\n", "7         C(sales)[T.product_mng]  0.061915\n", "16           average_montly_hours  0.004958\n", "15                 number_project -0.307006\n", "5          C(sales)[T.management] -0.309405\n", "2               C(sales)[<PERSON><PERSON>] -0.542008\n", "1   C(promotion_last_5years)[T.1] -1.327476\n", "18                  Work_accident -1.508264\n", "0                       Intercept -1.628679\n", "13             satisfaction_level -4.138943"]}, "execution_count": 154, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame(list(zip(Xs.columns, coef)), columns=[\"feature\", \"coef\"]).sort_values(by=\"coef\", ascending=False)"]}, {"cell_type": "code", "execution_count": 155, "id": "647a8a42", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.****************\n", "[[2117  177]\n", " [ 473  233]]\n", "              precision    recall  f1-score   support\n", "\n", "         0.0       0.82      0.92      0.87      2294\n", "         1.0       0.57      0.33      0.42       706\n", "\n", "    accuracy                           0.78      3000\n", "   macro avg       0.69      0.63      0.64      3000\n", "weighted avg       0.76      0.78      0.76      3000\n", "\n"]}], "source": ["y_pred = model.predict(X_test)\n", "\n", "from sklearn.metrics import accuracy_score, confusion_matrix, classification_report\n", "print(accuracy_score(y_test, y_pred))\n", "print(confusion_matrix(y_test, y_pred))\n", "print(classification_report(y_test, y_pred))"]}, {"cell_type": "markdown", "id": "73c4054e", "metadata": {}, "source": ["## Regularized Logistic Regression"]}, {"cell_type": "code", "execution_count": 162, "id": "084865c8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.7843333333333333\n", "[[2116  178]\n", " [ 469  237]]\n", "              precision    recall  f1-score   support\n", "\n", "         0.0       0.82      0.92      0.87      2294\n", "         1.0       0.57      0.34      0.42       706\n", "\n", "    accuracy                           0.78      3000\n", "   macro avg       0.69      0.63      0.65      3000\n", "weighted avg       0.76      0.78      0.76      3000\n", "\n"]}], "source": ["model = LogisticRegression(max_iter=10000, fit_intercept=False, C=1000)\n", "model.fit(X_train, y_train)\n", "y_pred = model.predict(X_test)\n", "print(accuracy_score(y_test, y_pred))\n", "print(confusion_matrix(y_test, y_pred))\n", "print(classification_report(y_test, y_pred))"]}, {"cell_type": "markdown", "id": "a80ba95e", "metadata": {}, "source": ["## Cross Validation"]}, {"cell_type": "code", "execution_count": 176, "id": "9333ee16", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0.80733333, 0.79133333, 0.796     , 0.788     , 0.80666667,\n", "       0.806     , 0.79333333, 0.78933333, 0.748     , 0.73715811])"]}, "execution_count": 176, "metadata": {}, "output_type": "execute_result"}], "source": ["cross_val_score(model, Xs.values, ys.values.ravel(), cv=10, scoring=\"accuracy\")"]}, {"cell_type": "markdown", "id": "6e85b189", "metadata": {}, "source": ["# Gradient Descent"]}, {"cell_type": "code", "execution_count": 157, "id": "5a4f58eb", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[  1.,   0.,   0., ..., 188.,   4.,   0.],\n", "       [  1.,   0.,   0., ..., 196.,   5.,   0.],\n", "       [  1.,   0.,   1., ..., 175.,   2.,   0.],\n", "       ...,\n", "       [  1.,   0.,   0., ..., 249.,   2.,   0.],\n", "       [  1.,   0.,   0., ..., 218.,   5.,   0.],\n", "       [  1.,   0.,   0., ..., 219.,   4.,   0.]])"]}, "execution_count": 157, "metadata": {}, "output_type": "execute_result"}], "source": ["X_train"]}, {"cell_type": "code", "execution_count": 158, "id": "becfb35c", "metadata": {}, "outputs": [], "source": ["## Feature normalization\n", "\n", "for i in range(1, X_train.shape[1]):\n", "    xmin = X_train[:, i].min()\n", "    xmax = X_train[:, i].max()\n", "    X_train[:, i] = (X_train[:, i] - xmin) / (xmax - xmin)\n", "\n", "for i in range(1, X_test.shape[1]):\n", "    xmin = X_test[:, i].min()\n", "    xmax = X_test[:, i].max()\n", "    X_test[:, i] = (X_test[:, i] - xmin) / (xmax - xmin)"]}, {"cell_type": "code", "execution_count": 159, "id": "b6737aca", "metadata": {}, "outputs": [], "source": ["def gradient_descent(\n", "        Xs: np.n<PERSON><PERSON>,\n", "        ys: np.n<PERSON><PERSON>,\n", "        alpha: float,\n", "        num_epochs: int,\n", ") -> tuple[np.n<PERSON><PERSON>, np.n<PERSON><PERSON>, np.ndarray]:\n", "    ##\n", "    np.random.seed(1)\n", "\n", "    ## Random initialization\n", "    betas = np.random.randn(Xs.shape[1])\n", "    losses = np.zeros(num_epochs)\n", "    errors = np.zeros(num_epochs)\n", "\n", "    ##\n", "    for epoch in tqdm.tqdm(range(num_epochs)):\n", "        ## Sigmoid P_i\n", "        proba = 1. / (1. + np.exp(-np.matmul(Xs, betas)))\n", "        proba_y = list(zip(proba, ys))\n", "\n", "        ## Cross entropy on P_i\n", "        loss = -np.sum([np.log(p) if y == 1 else np.log(1 - p) for p, y in proba_y]) / ys.shape[0]\n", "\n", "        ## Performance counting\n", "        error_rate = 0.\n", "        for i in range(ys.shape[0]): \n", "            if (proba[i] >= 0.5 and ys[i] == 0) or (proba[i] < 0.5 and ys[i] == 1):\n", "                error_rate += 1\n", "        error_rate /= ys.shape[0]\n", "\n", "        losses[epoch] = loss\n", "        errors[epoch] = error_rate\n", "\n", "        ## Gradient descent\n", "        grad = np.zeros(Xs.shape[1])\n", "        for i in range(ys.shape[0]):\n", "            grad += Xs[i] * (proba[i] - ys[i])\n", "        grad /= ys.shape[0]\n", "        betas -= alpha * grad\n", "\n", "    ##\n", "    return betas, losses, errors\n", "\n", "\n", "def trainning_plot(\n", "        losses: np.n<PERSON><PERSON>,\n", "        errors: np.<PERSON><PERSON><PERSON>,\n", ") -> None:\n", "    ## Plotting\n", "    plt.figure(figsize=(10, 5))\n", "    gca = plt.gca()\n", "    gca.set_ylabel(\"Error rate\")\n", "    plt.plot(errors, color=\"orange\", label=\"Error rate\")\n", "    plt.legend(loc=\"lower left\")\n", "    ax2 = gca.twinx()\n", "    plt.plot(losses, label=\"Loss\")\n", "    ax2.set_ylabel(\"Loss\")\n", "    gca.set_xlabel(\"Epoch\")\n", "    plt.title(\"Training loss and error rate\")\n", "    plt.legend(loc=\"upper right\")\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": 160, "id": "a45e0daf", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 200/200 [00:03<00:00, 62.98it/s]\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["betas, losses, errors = gradient_descent(X_train, y_train, alpha=1., num_epochs=200)\n", "trainning_plot(losses, errors)"]}, {"cell_type": "code", "execution_count": 161, "id": "c9042ded", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 500/500 [00:01<00:00, 253.95it/s]\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["betas, losses, errors = gradient_descent(X_test, y_test, alpha=1, num_epochs=500)\n", "trainning_plot(losses, errors)"]}, {"cell_type": "markdown", "id": "b51bc9d2", "metadata": {}, "source": ["# Handwritten recognition"]}, {"cell_type": "code", "execution_count": 182, "id": "45746522", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from sklearn import datasets, metrics\n", "\n", "digits = datasets.load_digits()\n", "samples = zip(digits.images, digits.target)\n", "\n", "for idx, (image, label) in enumerate(list(samples)[:4]):\n", "    plt.subplot(1, 4, idx + 1)\n", "    plt.axis(\"off\")\n", "    plt.imshow(image)\n", "    plt.title(f\"Label: {label}\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 186, "id": "b4e2f4fd", "metadata": {}, "outputs": [{"data": {"text/plain": ["((1437, 64), (360, 64), (1437,), (360,))"]}, "execution_count": 186, "metadata": {}, "output_type": "execute_result"}], "source": ["data, label = digits.data, digits.target\n", "X_train, X_test, y_train, y_test = train_test_split(data, label, test_size=0.2, random_state=42)\n", "X_train.shape, X_test.shape, y_train.shape, y_test.shape"]}, {"cell_type": "code", "execution_count": 187, "id": "b8afa2c5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.9638888888888889\n", "[[32  0  1  0  0  0  0  0  0  0]\n", " [ 0 28  0  0  0  0  0  0  0  0]\n", " [ 0  1 32  0  0  0  0  0  0  0]\n", " [ 0  0  0 33  0  1  0  0  0  0]\n", " [ 0  1  0  0 45  0  0  0  0  0]\n", " [ 0  0  1  0  0 45  1  0  0  0]\n", " [ 0  0  0  0  0  1 34  0  0  0]\n", " [ 0  0  0  0  0  1  0 33  0  0]\n", " [ 0  0  0  0  0  1  0  0 29  0]\n", " [ 0  1  0  1  0  0  0  0  2 36]]\n", "              precision    recall  f1-score   support\n", "\n", "           0       1.00      0.97      0.98        33\n", "           1       0.90      1.00      0.95        28\n", "           2       0.94      0.97      0.96        33\n", "           3       0.97      0.97      0.97        34\n", "           4       1.00      0.98      0.99        46\n", "           5       0.92      0.96      0.94        47\n", "           6       0.97      0.97      0.97        35\n", "           7       1.00      0.97      0.99        34\n", "           8       0.94      0.97      0.95        30\n", "           9       1.00      0.90      0.95        40\n", "\n", "    accuracy                           0.96       360\n", "   macro avg       0.96      0.97      0.96       360\n", "weighted avg       0.97      0.96      0.96       360\n", "\n"]}], "source": ["model = LogisticRegression(max_iter=10000, fit_intercept=False, C=1e5)\n", "model.fit(X_train, y_train)\n", "y_pred = model.predict(X_test)\n", "print(accuracy_score(y_test, y_pred))\n", "print(confusion_matrix(y_test, y_pred))\n", "print(classification_report(y_test, y_pred))"]}, {"cell_type": "code", "execution_count": 189, "id": "cb0f6182", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["samples = zip(X_test, y_pred)\n", "\n", "for idx, (image, label) in enumerate(list(samples)[:4]):\n", "    plt.subplot(1, 4, idx + 1)\n", "    plt.axis(\"off\")\n", "    plt.imshow(image.reshape((8, 8)))\n", "    plt.title(f\"Label: {label}\")\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}