import mlx.core as mlx

mlx.metal.is_available()

mlx.metal.device_info()

import mlx.core as mlx

num_features = 100
num_examples = 1_000
num_iters = 10_000
lr = 0.01

w_star = mlx.random.normal((num_features,))

Xs = mlx.random.normal((num_examples, num_features))

eps = 1e-2 * mlx.random.normal((num_examples,))

ys = Xs @ w_star + eps

def loss_fn(w):
    return 0.5 * mlx.mean((mlx.square(Xs @ w - ys)))

grad_fn = mlx.grad(loss_fn)

w = 1e-2 * mlx.random.normal((num_features,))

for _ in range(num_iters):
    ##
    grad = grad_fn(w)
    w -= lr * grad
    mlx.eval(w)

loss = loss_fn(w)
error_norm = mlx.sum(mlx.square(w - w_star)).item() ** 0.5

print(f"Loss: {loss.item():.5f}, |w - w*| = {error_norm:.5f}")