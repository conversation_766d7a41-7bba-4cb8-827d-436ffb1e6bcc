{"cells": [{"cell_type": "code", "execution_count": 1, "id": "40314f70", "metadata": {}, "outputs": [], "source": ["%matplotlib inline\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import os\n", "import pandas as pd\n", "import seaborn as sns\n", "\n", "from sklearn.model_selection import (\n", "    cross_val_score,\n", "    train_test_split,\n", ")\n", "from sklearn import tree\n", "from sklearn import metrics"]}, {"cell_type": "code", "execution_count": 5, "id": "7256414a", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/4m/5c4zjxt168z_1149t9g2vxkw0000gn/T/ipykernel_66182/1998762364.py:1: DtypeWarning: Columns (19,47) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  data = pd.read_csv(\"../.inputs/loan_sub/loan_sub.csv\")\n"]}, {"data": {"text/plain": ["Index(['id', 'member_id', 'loan_amnt', 'funded_amnt', 'funded_amnt_inv',\n", "       'term', 'int_rate', 'installment', 'grade', 'sub_grade', 'emp_title',\n", "       'emp_length', 'home_ownership', 'annual_inc', 'is_inc_v', 'issue_d',\n", "       'loan_status', 'pymnt_plan', 'url', 'desc', 'purpose', 'title',\n", "       'zip_code', 'addr_state', 'dti', 'delinq_2yrs', 'earliest_cr_line',\n", "       'inq_last_6mths', 'mths_since_last_delinq', 'mths_since_last_record',\n", "       'open_acc', 'pub_rec', 'revol_bal', 'revol_util', 'total_acc',\n", "       'initial_list_status', 'out_prncp', 'out_prncp_inv', 'total_pymnt',\n", "       'total_pymnt_inv', 'total_rec_prncp', 'total_rec_int',\n", "       'total_rec_late_fee', 'recoveries', 'collection_recovery_fee',\n", "       'last_pymnt_d', 'last_pymnt_amnt', 'next_pymnt_d', 'last_credit_pull_d',\n", "       'collections_12_mths_ex_med', 'mths_since_last_major_derog',\n", "       'policy_code', 'not_compliant', 'status', 'inactive_loans', 'bad_loans',\n", "       'emp_length_num', 'grade_num', 'sub_grade_num', 'delinq_2yrs_zero',\n", "       'pub_rec_zero', 'collections_12_mths_zero', 'short_emp',\n", "       'payment_inc_ratio', 'final_d', 'last_delinq_none', 'last_record_none',\n", "       'last_major_derog_none'],\n", "      dtype='object')"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["data = pd.read_csv(\"../.inputs/loan_sub/loan_sub.csv\")\n", "data.columns"]}, {"cell_type": "code", "execution_count": 4, "id": "327f214b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>member_id</th>\n", "      <th>loan_amnt</th>\n", "      <th>funded_amnt</th>\n", "      <th>funded_amnt_inv</th>\n", "      <th>term</th>\n", "      <th>int_rate</th>\n", "      <th>installment</th>\n", "      <th>grade</th>\n", "      <th>sub_grade</th>\n", "      <th>emp_title</th>\n", "      <th>emp_length</th>\n", "      <th>home_ownership</th>\n", "      <th>annual_inc</th>\n", "      <th>is_inc_v</th>\n", "      <th>issue_d</th>\n", "      <th>loan_status</th>\n", "      <th>pymnt_plan</th>\n", "      <th>url</th>\n", "      <th>desc</th>\n", "      <th>purpose</th>\n", "      <th>title</th>\n", "      <th>zip_code</th>\n", "      <th>addr_state</th>\n", "      <th>dti</th>\n", "      <th>delinq_2yrs</th>\n", "      <th>earliest_cr_line</th>\n", "      <th>inq_last_6mths</th>\n", "      <th>mths_since_last_delinq</th>\n", "      <th>mths_since_last_record</th>\n", "      <th>open_acc</th>\n", "      <th>pub_rec</th>\n", "      <th>revol_bal</th>\n", "      <th>revol_util</th>\n", "      <th>total_acc</th>\n", "      <th>initial_list_status</th>\n", "      <th>out_prncp</th>\n", "      <th>out_prncp_inv</th>\n", "      <th>total_pymnt</th>\n", "      <th>total_pymnt_inv</th>\n", "      <th>total_rec_prncp</th>\n", "      <th>total_rec_int</th>\n", "      <th>total_rec_late_fee</th>\n", "      <th>recoveries</th>\n", "      <th>collection_recovery_fee</th>\n", "      <th>last_pymnt_d</th>\n", "      <th>last_pymnt_amnt</th>\n", "      <th>next_pymnt_d</th>\n", "      <th>last_credit_pull_d</th>\n", "      <th>collections_12_mths_ex_med</th>\n", "      <th>mths_since_last_major_derog</th>\n", "      <th>policy_code</th>\n", "      <th>not_compliant</th>\n", "      <th>status</th>\n", "      <th>inactive_loans</th>\n", "      <th>bad_loans</th>\n", "      <th>emp_length_num</th>\n", "      <th>grade_num</th>\n", "      <th>sub_grade_num</th>\n", "      <th>delinq_2yrs_zero</th>\n", "      <th>pub_rec_zero</th>\n", "      <th>collections_12_mths_zero</th>\n", "      <th>short_emp</th>\n", "      <th>payment_inc_ratio</th>\n", "      <th>final_d</th>\n", "      <th>last_delinq_none</th>\n", "      <th>last_record_none</th>\n", "      <th>last_major_derog_none</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1077501</td>\n", "      <td>1296599</td>\n", "      <td>5000</td>\n", "      <td>5000</td>\n", "      <td>4975</td>\n", "      <td>36 months</td>\n", "      <td>10.65</td>\n", "      <td>162.87</td>\n", "      <td>B</td>\n", "      <td>B2</td>\n", "      <td>NaN</td>\n", "      <td>10+ years</td>\n", "      <td>RENT</td>\n", "      <td>24000.0</td>\n", "      <td>Verified</td>\n", "      <td>20111201T000000</td>\n", "      <td><PERSON><PERSON>id</td>\n", "      <td>n</td>\n", "      <td>https://www.lendingclub.com/browse/loanDetail....</td>\n", "      <td><PERSON><PERSON><PERSON> added on 12/22/11 &gt; I need to upgra...</td>\n", "      <td>credit_card</td>\n", "      <td>Computer</td>\n", "      <td>860xx</td>\n", "      <td>AZ</td>\n", "      <td>27.65</td>\n", "      <td>0.0</td>\n", "      <td>19850101T000000</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>3.0</td>\n", "      <td>0.0</td>\n", "      <td>13648</td>\n", "      <td>83.7</td>\n", "      <td>9.0</td>\n", "      <td>f</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>5861.07</td>\n", "      <td>5831.78</td>\n", "      <td>5000.00</td>\n", "      <td>861.07</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>20150101T000000</td>\n", "      <td>171.62</td>\n", "      <td>NaN</td>\n", "      <td>20150101T000000</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td><PERSON><PERSON>id</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>11</td>\n", "      <td>5</td>\n", "      <td>0.4</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>0</td>\n", "      <td>8.14350</td>\n", "      <td>20141201T000000</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1077430</td>\n", "      <td>1314167</td>\n", "      <td>2500</td>\n", "      <td>2500</td>\n", "      <td>2500</td>\n", "      <td>60 months</td>\n", "      <td>15.27</td>\n", "      <td>59.83</td>\n", "      <td>C</td>\n", "      <td>C4</td>\n", "      <td><PERSON></td>\n", "      <td>&lt; 1 year</td>\n", "      <td>RENT</td>\n", "      <td>30000.0</td>\n", "      <td>Source Verified</td>\n", "      <td>20111201T000000</td>\n", "      <td>Charged Off</td>\n", "      <td>n</td>\n", "      <td>https://www.lendingclub.com/browse/loanDetail....</td>\n", "      <td><PERSON><PERSON><PERSON> added on 12/22/11 &gt; I plan to use t...</td>\n", "      <td>car</td>\n", "      <td>bike</td>\n", "      <td>309xx</td>\n", "      <td>GA</td>\n", "      <td>1.00</td>\n", "      <td>0.0</td>\n", "      <td>19990401T000000</td>\n", "      <td>5.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>3.0</td>\n", "      <td>0.0</td>\n", "      <td>1687</td>\n", "      <td>9.4</td>\n", "      <td>4.0</td>\n", "      <td>f</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1008.71</td>\n", "      <td>1008.71</td>\n", "      <td>456.46</td>\n", "      <td>435.17</td>\n", "      <td>0.00</td>\n", "      <td>117.08</td>\n", "      <td>1.11</td>\n", "      <td>20130401T000000</td>\n", "      <td>119.66</td>\n", "      <td>NaN</td>\n", "      <td>20130901T000000</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Charged Off</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>4</td>\n", "      <td>0.8</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1</td>\n", "      <td>2.39320</td>\n", "      <td>20161201T000000</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1077175</td>\n", "      <td>1313524</td>\n", "      <td>2400</td>\n", "      <td>2400</td>\n", "      <td>2400</td>\n", "      <td>36 months</td>\n", "      <td>15.96</td>\n", "      <td>84.33</td>\n", "      <td>C</td>\n", "      <td>C5</td>\n", "      <td>NaN</td>\n", "      <td>10+ years</td>\n", "      <td>RENT</td>\n", "      <td>12252.0</td>\n", "      <td>Not Verified</td>\n", "      <td>20111201T000000</td>\n", "      <td><PERSON><PERSON>id</td>\n", "      <td>n</td>\n", "      <td>https://www.lendingclub.com/browse/loanDetail....</td>\n", "      <td>NaN</td>\n", "      <td>small_business</td>\n", "      <td>real estate business</td>\n", "      <td>606xx</td>\n", "      <td>IL</td>\n", "      <td>8.72</td>\n", "      <td>0.0</td>\n", "      <td>20011101T000000</td>\n", "      <td>2.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2.0</td>\n", "      <td>0.0</td>\n", "      <td>2956</td>\n", "      <td>98.5</td>\n", "      <td>10.0</td>\n", "      <td>f</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>3003.65</td>\n", "      <td>3003.65</td>\n", "      <td>2400.00</td>\n", "      <td>603.65</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>20140601T000000</td>\n", "      <td>649.91</td>\n", "      <td>NaN</td>\n", "      <td>20150201T000000</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td><PERSON><PERSON>id</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>11</td>\n", "      <td>4</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>0</td>\n", "      <td>8.25955</td>\n", "      <td>20141201T000000</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1076863</td>\n", "      <td>1277178</td>\n", "      <td>10000</td>\n", "      <td>10000</td>\n", "      <td>10000</td>\n", "      <td>36 months</td>\n", "      <td>13.49</td>\n", "      <td>339.31</td>\n", "      <td>C</td>\n", "      <td>C1</td>\n", "      <td>AIR RESOURCES BOARD</td>\n", "      <td>10+ years</td>\n", "      <td>RENT</td>\n", "      <td>49200.0</td>\n", "      <td>Source Verified</td>\n", "      <td>20111201T000000</td>\n", "      <td><PERSON><PERSON>id</td>\n", "      <td>n</td>\n", "      <td>https://www.lendingclub.com/browse/loanDetail....</td>\n", "      <td><PERSON><PERSON><PERSON> added on 12/21/11 &gt; to pay for prop...</td>\n", "      <td>other</td>\n", "      <td>personel</td>\n", "      <td>917xx</td>\n", "      <td>CA</td>\n", "      <td>20.00</td>\n", "      <td>0.0</td>\n", "      <td>19960201T000000</td>\n", "      <td>1.0</td>\n", "      <td>35.0</td>\n", "      <td>NaN</td>\n", "      <td>10.0</td>\n", "      <td>0.0</td>\n", "      <td>5598</td>\n", "      <td>21.0</td>\n", "      <td>37.0</td>\n", "      <td>f</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>12226.30</td>\n", "      <td>12226.30</td>\n", "      <td>10000.00</td>\n", "      <td>2209.33</td>\n", "      <td>16.97</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>20150101T000000</td>\n", "      <td>357.48</td>\n", "      <td>NaN</td>\n", "      <td>20150101T000000</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td><PERSON><PERSON>id</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>11</td>\n", "      <td>4</td>\n", "      <td>0.2</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>0</td>\n", "      <td>8.27585</td>\n", "      <td>20141201T000000</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1075269</td>\n", "      <td>1311441</td>\n", "      <td>5000</td>\n", "      <td>5000</td>\n", "      <td>5000</td>\n", "      <td>36 months</td>\n", "      <td>7.90</td>\n", "      <td>156.46</td>\n", "      <td>A</td>\n", "      <td>A4</td>\n", "      <td>Veolia Transportaton</td>\n", "      <td>3 years</td>\n", "      <td>RENT</td>\n", "      <td>36000.0</td>\n", "      <td>Source Verified</td>\n", "      <td>20111201T000000</td>\n", "      <td><PERSON><PERSON>id</td>\n", "      <td>n</td>\n", "      <td>https://www.lendingclub.com/browse/loanDetail....</td>\n", "      <td>NaN</td>\n", "      <td>wedding</td>\n", "      <td>My wedding loan I promise to pay back</td>\n", "      <td>852xx</td>\n", "      <td>AZ</td>\n", "      <td>11.20</td>\n", "      <td>0.0</td>\n", "      <td>20041101T000000</td>\n", "      <td>3.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>9.0</td>\n", "      <td>0.0</td>\n", "      <td>7963</td>\n", "      <td>28.3</td>\n", "      <td>12.0</td>\n", "      <td>f</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>5631.38</td>\n", "      <td>5631.38</td>\n", "      <td>5000.00</td>\n", "      <td>631.38</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>20150101T000000</td>\n", "      <td>161.03</td>\n", "      <td>NaN</td>\n", "      <td>20150201T000000</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td><PERSON><PERSON>id</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>6</td>\n", "      <td>0.8</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>0</td>\n", "      <td>5.21533</td>\n", "      <td>20141201T000000</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>1072053</td>\n", "      <td>1288686</td>\n", "      <td>3000</td>\n", "      <td>3000</td>\n", "      <td>3000</td>\n", "      <td>36 months</td>\n", "      <td>18.64</td>\n", "      <td>109.43</td>\n", "      <td>E</td>\n", "      <td>E1</td>\n", "      <td>MKC Accounting</td>\n", "      <td>9 years</td>\n", "      <td>RENT</td>\n", "      <td>48000.0</td>\n", "      <td>Source Verified</td>\n", "      <td>20111201T000000</td>\n", "      <td><PERSON><PERSON>id</td>\n", "      <td>n</td>\n", "      <td>https://www.lendingclub.com/browse/loanDetail....</td>\n", "      <td><PERSON><PERSON><PERSON> added on 12/16/11 &gt; Downpayment for...</td>\n", "      <td>car</td>\n", "      <td>Car Downpayment</td>\n", "      <td>900xx</td>\n", "      <td>CA</td>\n", "      <td>5.35</td>\n", "      <td>0.0</td>\n", "      <td>20070101T000000</td>\n", "      <td>2.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4.0</td>\n", "      <td>0.0</td>\n", "      <td>8221</td>\n", "      <td>87.5</td>\n", "      <td>4.0</td>\n", "      <td>f</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>3938.14</td>\n", "      <td>3938.14</td>\n", "      <td>3000.00</td>\n", "      <td>938.14</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>20150101T000000</td>\n", "      <td>111.34</td>\n", "      <td>NaN</td>\n", "      <td>20141201T000000</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td><PERSON><PERSON>id</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>10</td>\n", "      <td>2</td>\n", "      <td>0.2</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>0</td>\n", "      <td>2.73575</td>\n", "      <td>20141201T000000</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>1071795</td>\n", "      <td>1306957</td>\n", "      <td>5600</td>\n", "      <td>5600</td>\n", "      <td>5600</td>\n", "      <td>60 months</td>\n", "      <td>21.28</td>\n", "      <td>152.39</td>\n", "      <td>F</td>\n", "      <td>F2</td>\n", "      <td>NaN</td>\n", "      <td>4 years</td>\n", "      <td>OWN</td>\n", "      <td>40000.0</td>\n", "      <td>Source Verified</td>\n", "      <td>20111201T000000</td>\n", "      <td>Charged Off</td>\n", "      <td>n</td>\n", "      <td>https://www.lendingclub.com/browse/loanDetail....</td>\n", "      <td><PERSON><PERSON><PERSON> added on 12/21/11 &gt; I own a small h...</td>\n", "      <td>small_business</td>\n", "      <td>Expand Business &amp; Buy Debt Portfolio</td>\n", "      <td>958xx</td>\n", "      <td>CA</td>\n", "      <td>5.55</td>\n", "      <td>0.0</td>\n", "      <td>20040401T000000</td>\n", "      <td>2.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>11.0</td>\n", "      <td>0.0</td>\n", "      <td>5210</td>\n", "      <td>32.6</td>\n", "      <td>13.0</td>\n", "      <td>f</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>646.02</td>\n", "      <td>646.02</td>\n", "      <td>162.02</td>\n", "      <td>294.94</td>\n", "      <td>0.00</td>\n", "      <td>189.06</td>\n", "      <td>2.09</td>\n", "      <td>20120401T000000</td>\n", "      <td>152.39</td>\n", "      <td>NaN</td>\n", "      <td>20120801T000000</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Charged Off</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>5</td>\n", "      <td>1</td>\n", "      <td>0.4</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>0</td>\n", "      <td>4.57170</td>\n", "      <td>20161201T000000</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>1071570</td>\n", "      <td>1306721</td>\n", "      <td>5375</td>\n", "      <td>5375</td>\n", "      <td>5350</td>\n", "      <td>60 months</td>\n", "      <td>12.69</td>\n", "      <td>121.45</td>\n", "      <td>B</td>\n", "      <td>B5</td>\n", "      <td>Starbucks</td>\n", "      <td>&lt; 1 year</td>\n", "      <td>RENT</td>\n", "      <td>15000.0</td>\n", "      <td>Verified</td>\n", "      <td>20111201T000000</td>\n", "      <td>Charged Off</td>\n", "      <td>n</td>\n", "      <td>https://www.lendingclub.com/browse/loanDetail....</td>\n", "      <td><PERSON><PERSON><PERSON> added on 12/16/11 &gt; I'm trying to b...</td>\n", "      <td>other</td>\n", "      <td>Building my credit history.</td>\n", "      <td>774xx</td>\n", "      <td>TX</td>\n", "      <td>18.08</td>\n", "      <td>0.0</td>\n", "      <td>20040901T000000</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2.0</td>\n", "      <td>0.0</td>\n", "      <td>9279</td>\n", "      <td>36.5</td>\n", "      <td>3.0</td>\n", "      <td>f</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1476.19</td>\n", "      <td>1469.34</td>\n", "      <td>673.48</td>\n", "      <td>533.42</td>\n", "      <td>0.00</td>\n", "      <td>269.29</td>\n", "      <td>2.52</td>\n", "      <td>20121101T000000</td>\n", "      <td>121.45</td>\n", "      <td>NaN</td>\n", "      <td>20130301T000000</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Charged Off</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>5</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1</td>\n", "      <td>9.71600</td>\n", "      <td>20161201T000000</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>1070078</td>\n", "      <td>1305201</td>\n", "      <td>6500</td>\n", "      <td>6500</td>\n", "      <td>6500</td>\n", "      <td>60 months</td>\n", "      <td>14.65</td>\n", "      <td>153.45</td>\n", "      <td>C</td>\n", "      <td>C3</td>\n", "      <td>Southwest Rural metro</td>\n", "      <td>5 years</td>\n", "      <td>OWN</td>\n", "      <td>72000.0</td>\n", "      <td>Not Verified</td>\n", "      <td>20111201T000000</td>\n", "      <td><PERSON><PERSON>id</td>\n", "      <td>n</td>\n", "      <td>https://www.lendingclub.com/browse/loanDetail....</td>\n", "      <td><PERSON><PERSON><PERSON> added on 12/15/11 &gt; I had recived a...</td>\n", "      <td>debt_consolidation</td>\n", "      <td>High intrest Consolidation</td>\n", "      <td>853xx</td>\n", "      <td>AZ</td>\n", "      <td>16.12</td>\n", "      <td>0.0</td>\n", "      <td>19980101T000000</td>\n", "      <td>2.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>14.0</td>\n", "      <td>0.0</td>\n", "      <td>4032</td>\n", "      <td>20.6</td>\n", "      <td>23.0</td>\n", "      <td>f</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>7677.52</td>\n", "      <td>7677.52</td>\n", "      <td>6500.00</td>\n", "      <td>1177.52</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>20130601T000000</td>\n", "      <td>1655.54</td>\n", "      <td>NaN</td>\n", "      <td>20130701T000000</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td><PERSON><PERSON>id</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>6</td>\n", "      <td>4</td>\n", "      <td>0.6</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>0</td>\n", "      <td>2.55750</td>\n", "      <td>20161201T000000</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>1069908</td>\n", "      <td>1305008</td>\n", "      <td>12000</td>\n", "      <td>12000</td>\n", "      <td>12000</td>\n", "      <td>36 months</td>\n", "      <td>12.69</td>\n", "      <td>402.54</td>\n", "      <td>B</td>\n", "      <td>B5</td>\n", "      <td>UCLA</td>\n", "      <td>10+ years</td>\n", "      <td>OWN</td>\n", "      <td>75000.0</td>\n", "      <td>Source Verified</td>\n", "      <td>20111201T000000</td>\n", "      <td><PERSON><PERSON>id</td>\n", "      <td>n</td>\n", "      <td>https://www.lendingclub.com/browse/loanDetail....</td>\n", "      <td>NaN</td>\n", "      <td>debt_consolidation</td>\n", "      <td>Consolidation</td>\n", "      <td>913xx</td>\n", "      <td>CA</td>\n", "      <td>10.78</td>\n", "      <td>0.0</td>\n", "      <td>19891001T000000</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>12.0</td>\n", "      <td>0.0</td>\n", "      <td>23336</td>\n", "      <td>67.1</td>\n", "      <td>34.0</td>\n", "      <td>f</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>13943.10</td>\n", "      <td>13943.10</td>\n", "      <td>12000.00</td>\n", "      <td>1943.08</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>20130901T000000</td>\n", "      <td>6315.30</td>\n", "      <td>NaN</td>\n", "      <td>20130801T000000</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td><PERSON><PERSON>id</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>11</td>\n", "      <td>5</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>0</td>\n", "      <td>6.44064</td>\n", "      <td>20141201T000000</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        id  member_id  ...  last_record_none  last_major_derog_none\n", "0  1077501    1296599  ...                 1                      1\n", "1  1077430    1314167  ...                 1                      1\n", "2  1077175    1313524  ...                 1                      1\n", "3  1076863    1277178  ...                 1                      1\n", "4  1075269    1311441  ...                 1                      1\n", "5  1072053    1288686  ...                 1                      1\n", "6  1071795    1306957  ...                 1                      1\n", "7  1071570    1306721  ...                 1                      1\n", "8  1070078    1305201  ...                 1                      1\n", "9  1069908    1305008  ...                 1                      1\n", "\n", "[10 rows x 68 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["data.head(10)"]}, {"cell_type": "code", "execution_count": 6, "id": "a27fd9ac", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 122607 entries, 0 to 122606\n", "Data columns (total 68 columns):\n", " #   Column                       Non-Null Count   Dtype  \n", "---  ------                       --------------   -----  \n", " 0   id                           122607 non-null  int64  \n", " 1   member_id                    122607 non-null  int64  \n", " 2   loan_amnt                    122607 non-null  int64  \n", " 3   funded_amnt                  122607 non-null  int64  \n", " 4   funded_amnt_inv              122607 non-null  int64  \n", " 5   term                         122607 non-null  object \n", " 6   int_rate                     122607 non-null  float64\n", " 7   installment                  122607 non-null  float64\n", " 8   grade                        122607 non-null  object \n", " 9   sub_grade                    122607 non-null  object \n", " 10  emp_title                    115767 non-null  object \n", " 11  emp_length                   118516 non-null  object \n", " 12  home_ownership               122607 non-null  object \n", " 13  annual_inc                   122603 non-null  float64\n", " 14  is_inc_v                     122607 non-null  object \n", " 15  issue_d                      122607 non-null  object \n", " 16  loan_status                  122607 non-null  object \n", " 17  pymnt_plan                   122607 non-null  object \n", " 18  url                          122607 non-null  object \n", " 19  desc                         60703 non-null   object \n", " 20  purpose                      122607 non-null  object \n", " 21  title                        122596 non-null  object \n", " 22  zip_code                     122607 non-null  object \n", " 23  addr_state                   122607 non-null  object \n", " 24  dti                          122607 non-null  float64\n", " 25  delinq_2yrs                  122578 non-null  float64\n", " 26  earliest_cr_line             122578 non-null  object \n", " 27  inq_last_6mths               122578 non-null  float64\n", " 28  mths_since_last_delinq       50500 non-null   float64\n", " 29  mths_since_last_record       12531 non-null   float64\n", " 30  open_acc                     122578 non-null  float64\n", " 31  pub_rec                      122578 non-null  float64\n", " 32  revol_bal                    122607 non-null  int64  \n", " 33  revol_util                   122607 non-null  float64\n", " 34  total_acc                    122578 non-null  float64\n", " 35  initial_list_status          122607 non-null  object \n", " 36  out_prncp                    122607 non-null  float64\n", " 37  out_prncp_inv                122607 non-null  float64\n", " 38  total_pymnt                  122607 non-null  float64\n", " 39  total_pymnt_inv              122607 non-null  float64\n", " 40  total_rec_prncp              122607 non-null  float64\n", " 41  total_rec_int                122607 non-null  float64\n", " 42  total_rec_late_fee           122607 non-null  float64\n", " 43  recoveries                   122607 non-null  float64\n", " 44  collection_recovery_fee      122607 non-null  float64\n", " 45  last_pymnt_d                 122271 non-null  object \n", " 46  last_pymnt_amnt              122607 non-null  float64\n", " 47  next_pymnt_d                 2907 non-null    object \n", " 48  last_credit_pull_d           122601 non-null  object \n", " 49  collections_12_mths_ex_med   122462 non-null  float64\n", " 50  mths_since_last_major_derog  15460 non-null   float64\n", " 51  policy_code                  122607 non-null  int64  \n", " 52  not_compliant                122607 non-null  int64  \n", " 53  status                       122607 non-null  object \n", " 54  inactive_loans               122607 non-null  int64  \n", " 55  bad_loans                    122607 non-null  int64  \n", " 56  emp_length_num               122607 non-null  int64  \n", " 57  grade_num                    122607 non-null  int64  \n", " 58  sub_grade_num                122607 non-null  float64\n", " 59  delinq_2yrs_zero             122578 non-null  float64\n", " 60  pub_rec_zero                 122578 non-null  float64\n", " 61  collections_12_mths_zero     122462 non-null  float64\n", " 62  short_emp                    122607 non-null  int64  \n", " 63  payment_inc_ratio            122603 non-null  float64\n", " 64  final_d                      122607 non-null  object \n", " 65  last_delinq_none             122607 non-null  int64  \n", " 66  last_record_none             122607 non-null  int64  \n", " 67  last_major_derog_none        122607 non-null  int64  \n", "dtypes: float64(29), int64(16), object(23)\n", "memory usage: 63.6+ MB\n"]}], "source": ["data.info()"]}, {"cell_type": "code", "execution_count": 10, "id": "59b6352a", "metadata": {}, "outputs": [], "source": ["data[\"safe_loans\"] = data[\"bad_loans\"].apply(lambda x: int(not x))\n", "data.drop(columns=[\"bad_loans\"], inplace=True)"]}, {"cell_type": "code", "execution_count": 12, "id": "4d7695aa", "metadata": {}, "outputs": [{"data": {"text/plain": ["safe_loans\n", "1    0.811185\n", "0    0.188815\n", "Name: proportion, dtype: float64"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["data[\"safe_loans\"].value_counts(normalize=True)"]}, {"cell_type": "markdown", "id": "f17a5aa0", "metadata": {}, "source": ["# Random Features"]}, {"cell_type": "code", "execution_count": 13, "id": "2479c4eb", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>grade</th>\n", "      <th>term</th>\n", "      <th>home_ownership</th>\n", "      <th>emp_length</th>\n", "      <th>safe_loans</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>B</td>\n", "      <td>36 months</td>\n", "      <td>RENT</td>\n", "      <td>10+ years</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>C</td>\n", "      <td>60 months</td>\n", "      <td>RENT</td>\n", "      <td>&lt; 1 year</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>C</td>\n", "      <td>36 months</td>\n", "      <td>RENT</td>\n", "      <td>10+ years</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>C</td>\n", "      <td>36 months</td>\n", "      <td>RENT</td>\n", "      <td>10+ years</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>A</td>\n", "      <td>36 months</td>\n", "      <td>RENT</td>\n", "      <td>3 years</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  grade        term home_ownership emp_length  safe_loans\n", "0     B   36 months           RENT  10+ years           1\n", "1     C   60 months           RENT   < 1 year           0\n", "2     C   36 months           RENT  10+ years           1\n", "3     C   36 months           RENT  10+ years           1\n", "4     A   36 months           RENT    3 years           1"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["feature_cols = [\"grade\", \"term\", \"home_ownership\", \"emp_length\"]\n", "target_col = \"safe_loans\"\n", "data = data[feature_cols + [target_col]]\n", "data.head()"]}, {"cell_type": "markdown", "id": "49658dbe", "metadata": {}, "source": ["# Balance Data Points\n", "\n", "* undersample majority class"]}, {"cell_type": "code", "execution_count": 14, "id": "68ea5125", "metadata": {}, "outputs": [{"data": {"text/plain": ["safe_loans\n", "1    99457\n", "0    23150\n", "Name: count, dtype: int64"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["data[\"safe_loans\"].value_counts()"]}, {"cell_type": "code", "execution_count": 15, "id": "f0fbf1f5", "metadata": {}, "outputs": [], "source": ["## Use the percentage of bad / good loans to undersample the safe loans:\n", "bad_ones = data[data[\"safe_loans\"] == 0]\n", "good_ones = data[data[\"safe_loans\"] == 1]\n", "pct = bad_ones.shape[0] / good_ones.shape[0]\n", "\n", "risky_loans = bad_ones\n", "safe_loans = good_ones.sample(frac=pct, random_state=33)\n", "data = pd.concat([bad_ones, good_ones], axis=0)\n", "\n", "data_set = pd.concat([risky_loans, safe_loans], axis=0)"]}, {"cell_type": "code", "execution_count": 16, "id": "be80b4c6", "metadata": {}, "outputs": [{"data": {"text/plain": ["0.2327639080205516"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["pct"]}, {"cell_type": "code", "execution_count": 17, "id": "e1ff59dc", "metadata": {}, "outputs": [{"data": {"text/plain": ["safe_loans\n", "0    0.5\n", "1    0.5\n", "Name: proportion, dtype: float64"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["data_set[\"safe_loans\"].value_counts(normalize=True)"]}, {"cell_type": "markdown", "id": "3fdd2ef6", "metadata": {}, "source": ["# Preprocessing Features"]}, {"cell_type": "code", "execution_count": 18, "id": "c815c9d6", "metadata": {}, "outputs": [], "source": ["def label_encode(\n", "        data: pd.DataFrame,\n", "        columns: list[str],\n", "):\n", "    ##\n", "    for col in columns:\n", "        ##\n", "        data[col] = data[col].apply(lambda x: str(x))\n", "        new_cols = [col + \"_\" + i for i in data[col].unique()]\n", "        data = pd.concat([data, pd.get_dummies(data[col], prefix=col)[new_cols]], axis=1)\n", "        del data[col]\n", "    ##\n", "    return data"]}, {"cell_type": "code", "execution_count": 19, "id": "4c8a3cc5", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>safe_loans</th>\n", "      <th>grade_C</th>\n", "      <th>grade_F</th>\n", "      <th>grade_B</th>\n", "      <th>grade_D</th>\n", "      <th>grade_A</th>\n", "      <th>grade_E</th>\n", "      <th>grade_G</th>\n", "      <th>term_ 60 months</th>\n", "      <th>term_ 36 months</th>\n", "      <th>home_ownership_RENT</th>\n", "      <th>home_ownership_OWN</th>\n", "      <th>home_ownership_MORTGAGE</th>\n", "      <th>home_ownership_OTHER</th>\n", "      <th>emp_length_&lt; 1 year</th>\n", "      <th>emp_length_4 years</th>\n", "      <th>emp_length_3 years</th>\n", "      <th>emp_length_10+ years</th>\n", "      <th>emp_length_1 year</th>\n", "      <th>emp_length_9 years</th>\n", "      <th>emp_length_2 years</th>\n", "      <th>emp_length_8 years</th>\n", "      <th>emp_length_7 years</th>\n", "      <th>emp_length_5 years</th>\n", "      <th>emp_length_nan</th>\n", "      <th>emp_length_6 years</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    safe_loans  grade_C  ...  emp_length_nan  emp_length_6 years\n", "1            0     True  ...           False               False\n", "6            0    False  ...           False               False\n", "7            0    False  ...           False               False\n", "10           0     True  ...           False               False\n", "12           0    False  ...           False               False\n", "\n", "[5 rows x 26 columns]"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["cols = feature_cols\n", "data_set = label_encode(data_set, cols)\n", "data_set.head()"]}, {"cell_type": "code", "execution_count": 100, "id": "9018efcf", "metadata": {}, "outputs": [{"data": {"text/plain": ["((37040, 25), (9260, 25), (37040,), (9260,))"]}, "execution_count": 100, "metadata": {}, "output_type": "execute_result"}], "source": ["Xs = data_set.drop(columns=[target_col])\n", "ys = data_set[target_col]\n", "X_train, X_test, y_train, y_test = train_test_split(Xs, ys, test_size=0.2, random_state=33)\n", "X_train.shape, X_test.shape, y_train.shape, y_test.shape"]}, {"cell_type": "code", "execution_count": 101, "id": "0dc21133", "metadata": {}, "outputs": [], "source": ["y_train = y_train.to_frame()\n", "y_test = y_test.to_frame()"]}, {"cell_type": "code", "execution_count": 102, "id": "786abbb8", "metadata": {}, "outputs": [{"data": {"text/plain": ["(       grade_C  grade_F  ...  emp_length_nan  emp_length_6 years\n", " 88616    False     True  ...           False               False\n", " 57247    False     True  ...           False               False\n", " 12605    False     True  ...           False               False\n", " 86288    False    False  ...           False               False\n", " 20916    False    False  ...           False               False\n", " \n", " [5 rows x 25 columns],\n", "        safe_loans\n", " 88616           0\n", " 57247           0\n", " 12605           0\n", " 86288           1\n", " 20916           0)"]}, "execution_count": 102, "metadata": {}, "output_type": "execute_result"}], "source": ["X_train.head(), y_train.head()"]}, {"cell_type": "code", "execution_count": 103, "id": "1776bb82", "metadata": {}, "outputs": [{"data": {"text/plain": ["(        grade_C  grade_F  ...  emp_length_nan  emp_length_6 years\n", " 111240     True    False  ...           False               False\n", " 13030     False     True  ...           False               False\n", " 43847     False    False  ...           False               False\n", " 101348    False     True  ...           False               False\n", " 82421     False     True  ...           False               False\n", " \n", " [5 rows x 25 columns],\n", "         safe_loans\n", " 111240           1\n", " 13030            0\n", " 43847            1\n", " 101348           0\n", " 82421            1)"]}, "execution_count": 103, "metadata": {}, "output_type": "execute_result"}], "source": ["X_test.head(), y_test.head()"]}, {"cell_type": "markdown", "id": "87961c76", "metadata": {}, "source": ["# Decision Tree"]}, {"cell_type": "markdown", "id": "fd09aa94", "metadata": {}, "source": ["## Split features based on error: best_split_error()\n", "\n", "* left tree with target == 0\n", "* right tree with target == 1"]}, {"cell_type": "code", "execution_count": null, "id": "a11307a0", "metadata": {}, "outputs": [], "source": ["def count_errors(\n", "        labels_in_node: np.ndarray | pd.Series,\n", ") -> int:\n", "    ##\n", "    if labels_in_node.size == 0:\n", "        return 0\n", "\n", "    ## If we do the major class voting, how many errors we make?\n", "    positive_ones = labels_in_node[labels_in_node == 1].size\n", "    negative_ones = labels_in_node[labels_in_node == 0].size\n", "\n", "    ##\n", "    return min(positive_ones, negative_ones)"]}, {"cell_type": "code", "execution_count": 105, "id": "593b3167", "metadata": {}, "outputs": [], "source": ["def best_split_error(\n", "        data: pd.DataFrame,\n", "        features: list[str],\n", "        target: str,\n", ") -> str:\n", "    ##\n", "    best_feature = None\n", "    best_error_rate = 2.0\n", "    num_data_points = float(data.shape[0])\n", "    ##\n", "    for feature in features:\n", "        ##\n", "        left_split = data[data[feature] == 0]\n", "        right_split = data[data[feature] == 1]\n", "        ##\n", "        error_rate = count_errors(left_split[target]) + count_errors(right_split[target])\n", "        error_rate /= num_data_points\n", "        ##\n", "        if error_rate < best_error_rate:\n", "            best_error_rate = error_rate\n", "            best_feature = feature\n", "    ##\n", "    return best_feature"]}, {"cell_type": "code", "execution_count": 106, "id": "6f666e37", "metadata": {}, "outputs": [], "source": ["def entropy(\n", "        labels_in_node: np.ndarray | pd.Series,\n", ") -> float:\n", "    ##\n", "    n = labels_in_node.size\n", "    s1 = labels_in_node[labels_in_node == 1].size\n", "    if s1 == 0 or s1 == n:\n", "        return 0.\n", "\n", "    ##\n", "    p1 = float(s1) / n\n", "    p0 = 1. - p1\n", "\n", "    ##\n", "    return -p0 * np.log2(p0) - p1 * np.log2(p1)"]}, {"cell_type": "code", "execution_count": 107, "id": "9747642e", "metadata": {}, "outputs": [], "source": ["def best_split_entropy(\n", "        data: pd.DataFrame,\n", "        features: list[str],\n", "        target: str,\n", ") -> str:\n", "    ##\n", "    best_feature = None\n", "    best_info_gain = float(\"-inf\")\n", "    num_data_points = float(data.shape[0])\n", "    ##\n", "    entropy_before_split = entropy(data[target])\n", "\n", "    ##\n", "    for feature in features:\n", "        ##\n", "        left_split = data[data[feature] == 0]\n", "        right_split = data[data[feature] == 1]\n", "        ##\n", "        left_entropy = entropy(left_split[target])\n", "        right_entropy = entropy(right_split[target])\n", "        ## weighted average entropy of left and right split\n", "        entropy_after_split = (\n", "            float(left_split.shape[0]) / num_data_points * left_entropy +\n", "            float(right_split.shape[0]) / num_data_points * right_entropy\n", "        )\n", "        ##\n", "        info_gain = entropy_before_split - entropy_after_split\n", "        ##\n", "        if info_gain > best_info_gain:\n", "            best_info_gain = info_gain\n", "            best_feature = feature\n", "\n", "    ##\n", "    return str(best_feature)"]}, {"cell_type": "markdown", "id": "4aff22c9", "metadata": {}, "source": ["## Tree Node\n", "\n", "### is_leaf: True if the node is a leaf node, False otherwise\n", "### prediction: prediction based on voting\n", "### split_feature: The feature used to split the node\n", "### left: The left child of the node\n", "### right: The right child of the node"]}, {"cell_type": "code", "execution_count": 108, "id": "8861560b", "metadata": {}, "outputs": [], "source": ["class TreeNode:\n", "    ##\n", "    def __init__(self, is_leaf, prediction, split_feature):\n", "        ##\n", "        self.is_leaf = is_leaf\n", "        self.prediction = prediction\n", "        self.split_feature = split_feature\n", "        self.left = None\n", "        self.right = None"]}, {"cell_type": "markdown", "id": "71fd89fc", "metadata": {}, "source": ["## DecisionTree\n", "\n", "### fit(): learn from the training data\n", "### predict(): predict the labels on the training data\n", "### score(): score the accuracy of the predictions on the test data\n", "\n", "### create_tree()\n", "### create_leaf()\n", "### predict_single_data()\n", "### count_leaves()"]}, {"cell_type": "code", "execution_count": 127, "id": "6c27ef20", "metadata": {}, "outputs": [], "source": ["from sklearn.base import BaseEstimator\n", "from sklearn.metrics import accuracy_score\n", "from typing import Iterable, Optional\n", "\n", "class DecisionTree(BaseEstimator):\n", "    ##\n", "    def __init__(self, max_depth=3, min_error_reduction=0.0):\n", "        ## Hyperparameters\n", "        self.max_depth = max_depth\n", "        self.min_error_reduction = min_error_reduction\n", "\n", "    ##\n", "    def fit(\n", "            self,\n", "            Xs: pd.DataFrame,\n", "            ys: pd.<PERSON><PERSON><PERSON><PERSON>,\n", "            ws: Optional[pd.Series] = None,\n", "    ) -> None:\n", "        ##\n", "        data_set = pd.concat([Xs, ys], axis=1)\n", "        features = Xs.columns\n", "        target = ys.columns[0]\n", "        ##\n", "        self.root_node = self._build_tree(\n", "            data_set,\n", "            features,\n", "            target,\n", "            current_depth=0,\n", "            max_depth=self.max_depth,\n", "            min_error_reduction=self.min_error_reduction,\n", "        )\n", "        ##\n", "        return self\n", "    \n", "    ##\n", "    def prediction(\n", "            self,\n", "            Xs: pd.DataFrame,\n", "    ) -> pd.Series:\n", "        ##\n", "        return Xs.apply(lambda x: self.predict_single_data(self.root_node, x), axis=1)\n", "\n", "    ##\n", "    def score(\n", "            self,\n", "            X_test: pd.DataFrame,\n", "            y_test: pd.DataFrame,\n", "    ) -> float:\n", "        ##\n", "        target = y_test.columns[0]\n", "        y_pred = self.prediction(X_test)\n", "        return accuracy_score(y_test[target], y_pred)\n", "\n", "    ##\n", "    def _build_tree(\n", "            self,\n", "            data: pd.DataFrame,\n", "            features: Iterable[str],\n", "            target: str,\n", "            current_depth: int,\n", "            max_depth: int,\n", "            min_error_reduction: float,\n", "    ) -> TreeNode:\n", "        ##\n", "        remaining_features = features[:]\n", "\n", "        target_values = data[target]\n", "\n", "        ## termination 2\n", "        if len(remaining_features) == 0:\n", "            print(\"Termination 2 reached.\")\n", "            return self.create_leaf(target_values)\n", "\n", "        ## termination 3\n", "        if current_depth >= max_depth:\n", "            print(\"Termination 3 reached.\")\n", "            return self.create_leaf(target_values)\n", "    \n", "        ##\n", "        split_feature = best_split_entropy(data, remaining_features, target)\n", "        left_split = data[data[split_feature] == 0]\n", "        right_split = data[data[split_feature] == 1]\n", "        assert(split_feature in remaining_features), f\"{split_feature} is not in {remaining_features}\"\n", "        remaining_features = remaining_features.drop(split_feature)\n", "        print(\"Split on feature %s. (%s, %s)\" % (split_feature, str(len(left_split)), str(len(right_split))))\n", "\n", "        ##\n", "        if len(left_split) == len(data):\n", "            print(\"Perfect split!\")\n", "            return self.create_leaf(left_split[target])\n", "        if len(right_split) == len(data):\n", "            print(\"Perfect split!\")\n", "            return self.create_leaf(right_split[target])\n", "\n", "        ## Recursive call\n", "        left_tree = self._build_tree(\n", "            left_split,\n", "            remaining_features,\n", "            target,\n", "            current_depth + 1,\n", "            max_depth,\n", "            min_error_reduction,\n", "        )\n", "        right_tree = self._build_tree(\n", "            right_split,\n", "            remaining_features,\n", "            target,\n", "            current_depth + 1,\n", "            max_depth,\n", "            min_error_reduction,\n", "        )\n", "\n", "        ##\n", "        result_node = TreeNode(False, None, split_feature)\n", "        result_node.left = left_tree\n", "        result_node.right = right_tree\n", "        return result_node\n", "\n", "    ##\n", "    def create_leaf(self, target_values):\n", "        ##\n", "        leaf = TreeNode(True, None, None)\n", "        num_ones = len(target_values[target_values == 1])\n", "        num_zeros = len(target_values[target_values == 0])\n", "        if num_ones > num_zeros:\n", "            leaf.prediction = 1\n", "        else:\n", "            leaf.prediction = 0\n", "        ##\n", "        return leaf\n", "\n", "    ##\n", "    def predict_single_data(\n", "            self,\n", "            node: TreeNode,\n", "            x: pd.Series,\n", "            annotate = False,\n", "    ) -> int:\n", "        ##\n", "        if node.is_leaf:\n", "            if annotate:\n", "                print(\"At leaf, predicting %s\" % node.prediction)\n", "            return node.prediction\n", "        ##\n", "        split_feature_value = x[node.split_feature]\n", "        ##\n", "        if annotate:\n", "            print(\"Split on %s = %s\" % (node.split_feature, split_feature_value))\n", "        if split_feature_value == 0:\n", "            return self.predict_single_data(node.left, x, annotate)\n", "        else:\n", "            return self.predict_single_data(node.right, x, annotate)\n", "        \n", "    ##\n", "    def count_leaves(self):\n", "        ##\n", "        return self._count_leaves(self.root_node)\n", "    \n", "    ##\n", "    def _count_leaves(self, node):\n", "        ##\n", "        if node.is_leaf:\n", "            return 1\n", "        ##\n", "        return self._count_leaves(node.left) + self._count_leaves(node.right)"]}, {"cell_type": "code", "execution_count": 128, "id": "31753441", "metadata": {}, "outputs": [], "source": ["model = DecisionTree(max_depth=10, min_error_reduction=1e-15)"]}, {"cell_type": "code", "execution_count": 129, "id": "93405211", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Split on feature grade_A. (31776, 5264)\n", "Split on feature grade_B. (21587, 10189)\n", "Split on feature grade_C. (12308, 9279)\n", "Split on feature grade_D. (5553, 6755)\n", "Split on feature term_ 60 months. (1743, 3810)\n", "Split on feature grade_E. (459, 1284)\n", "Split on feature emp_length_10+ years. (358, 101)\n", "Split on feature emp_length_6 years. (328, 30)\n", "Split on feature home_ownership_OTHER. (325, 3)\n", "Split on feature emp_length_4 years. (297, 28)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_F. (2, 1)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature home_ownership_MORTGAGE. (23, 7)\n", "Split on feature home_ownership_OWN. (21, 2)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_F. (0, 7)\n", "Perfect split!\n", "Split on feature grade_F. (25, 76)\n", "Split on feature home_ownership_OWN. (21, 4)\n", "Split on feature home_ownership_RENT. (13, 8)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_G. (0, 4)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (47, 29)\n", "Split on feature home_ownership_OWN. (39, 8)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_G. (29, 0)\n", "Perfect split!\n", "Split on feature emp_length_nan. (1223, 61)\n", "Split on feature emp_length_1 year. (1117, 106)\n", "Split on feature home_ownership_OTHER. (1113, 4)\n", "Split on feature emp_length_2 years. (963, 150)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_F. (4, 0)\n", "Perfect split!\n", "Split on feature home_ownership_MORTGAGE. (75, 31)\n", "Split on feature home_ownership_OTHER. (74, 1)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_F. (31, 0)\n", "Perfect split!\n", "Split on feature home_ownership_OWN. (55, 6)\n", "Split on feature home_ownership_RENT. (17, 38)\n", "Split on feature grade_F. (17, 0)\n", "Perfect split!\n", "Split on feature grade_F. (38, 0)\n", "Perfect split!\n", "Split on feature grade_F. (6, 0)\n", "Perfect split!\n", "Split on feature home_ownership_MORTGAGE. (1919, 1891)\n", "Split on feature emp_length_nan. (1879, 40)\n", "Split on feature grade_E. (865, 1014)\n", "Split on feature emp_length_9 years. (837, 28)\n", "Split on feature emp_length_6 years. (789, 48)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_F. (4, 24)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature emp_length_5 years. (923, 91)\n", "Split on feature home_ownership_OWN. (790, 133)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature home_ownership_RENT. (14, 77)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_F. (23, 17)\n", "Split on feature grade_E. (5, 18)\n", "Split on feature home_ownership_RENT. (1, 4)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature home_ownership_RENT. (4, 14)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_E. (17, 0)\n", "Perfect split!\n", "Split on feature emp_length_4 years. (1775, 116)\n", "Split on feature emp_length_7 years. (1642, 133)\n", "Split on feature grade_F. (1100, 542)\n", "Split on feature emp_length_9 years. (1038, 62)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature emp_length_nan. (533, 9)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_E. (47, 86)\n", "Split on feature grade_F. (11, 36)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_F. (86, 0)\n", "Perfect split!\n", "Split on feature grade_G. (109, 7)\n", "Split on feature grade_F. (71, 38)\n", "Split on feature grade_E. (0, 71)\n", "Perfect split!\n", "Split on feature grade_E. (38, 0)\n", "Perfect split!\n", "Split on feature grade_F. (7, 0)\n", "Perfect split!\n", "Split on feature home_ownership_MORTGAGE. (4055, 2700)\n", "Split on feature term_ 60 months. (3136, 919)\n", "Split on feature emp_length_nan. (2992, 144)\n", "Split on feature emp_length_2 years. (2621, 371)\n", "Split on feature emp_length_3 years. (2316, 305)\n", "Split on feature emp_length_6 years. (2128, 188)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature home_ownership_RENT. (42, 263)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature home_ownership_OTHER. (370, 1)\n", "Split on feature home_ownership_RENT. (32, 338)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_F. (1, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (42, 102)\n", "Split on feature grade_F. (42, 0)\n", "Perfect split!\n", "Split on feature grade_F. (102, 0)\n", "Perfect split!\n", "Split on feature emp_length_nan. (894, 25)\n", "Split on feature emp_length_4 years. (831, 63)\n", "Split on feature emp_length_2 years. (750, 81)\n", "Split on feature emp_length_9 years. (716, 34)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature home_ownership_RENT. (10, 71)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature home_ownership_RENT. (9, 54)\n", "Split on feature grade_F. (9, 0)\n", "Perfect split!\n", "Split on feature grade_F. (54, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (6, 19)\n", "Split on feature grade_F. (6, 0)\n", "Perfect split!\n", "Split on feature grade_F. (19, 0)\n", "Perfect split!\n", "Split on feature term_ 60 months. (1571, 1129)\n", "Split on feature emp_length_5 years. (1457, 114)\n", "Split on feature emp_length_3 years. (1337, 120)\n", "Split on feature emp_length_1 year. (1254, 83)\n", "Split on feature emp_length_9 years. (1197, 57)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_F. (83, 0)\n", "Perfect split!\n", "Split on feature grade_F. (120, 0)\n", "Perfect split!\n", "Split on feature grade_F. (114, 0)\n", "Perfect split!\n", "Split on feature emp_length_7 years. (1056, 73)\n", "Split on feature emp_length_10+ years. (576, 480)\n", "Split on feature emp_length_2 years. (487, 89)\n", "Split on feature emp_length_6 years. (421, 66)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_F. (89, 0)\n", "Perfect split!\n", "Split on feature grade_F. (480, 0)\n", "Perfect split!\n", "Split on feature grade_F. (73, 0)\n", "Perfect split!\n", "Split on feature emp_length_nan. (8896, 383)\n", "Split on feature term_ 60 months. (6804, 2092)\n", "Split on feature home_ownership_MORTGAGE. (4058, 2746)\n", "Split on feature emp_length_2 years. (3553, 505)\n", "Split on feature emp_length_7 years. (3355, 198)\n", "Split on feature home_ownership_OTHER. (3347, 8)\n", "Split on feature emp_length_4 years. (3019, 328)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature emp_length_< 1 year. (6, 2)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature home_ownership_RENT. (31, 167)\n", "Split on feature grade_F. (31, 0)\n", "Perfect split!\n", "Split on feature grade_F. (167, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (54, 451)\n", "Split on feature grade_F. (54, 0)\n", "Perfect split!\n", "Split on feature grade_F. (451, 0)\n", "Perfect split!\n", "Split on feature emp_length_10+ years. (1779, 967)\n", "Split on feature emp_length_3 years. (1578, 201)\n", "Split on feature emp_length_7 years. (1396, 182)\n", "Split on feature emp_length_8 years. (1243, 153)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_F. (182, 0)\n", "Perfect split!\n", "Split on feature grade_F. (201, 0)\n", "Perfect split!\n", "Split on feature grade_F. (967, 0)\n", "Perfect split!\n", "Split on feature home_ownership_MORTGAGE. (756, 1336)\n", "Split on feature emp_length_5 years. (693, 63)\n", "Split on feature emp_length_< 1 year. (606, 87)\n", "Split on feature emp_length_9 years. (574, 32)\n", "Split on feature emp_length_6 years. (538, 36)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature home_ownership_RENT. (10, 22)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature home_ownership_RENT. (5, 82)\n", "Split on feature grade_F. (5, 0)\n", "Perfect split!\n", "Split on feature grade_F. (82, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (7, 56)\n", "Split on feature grade_F. (7, 0)\n", "Perfect split!\n", "Split on feature grade_F. (56, 0)\n", "Perfect split!\n", "Split on feature emp_length_2 years. (1263, 73)\n", "Split on feature emp_length_1 year. (1210, 53)\n", "Split on feature emp_length_7 years. (1131, 79)\n", "Split on feature emp_length_3 years. (1047, 84)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_F. (79, 0)\n", "Perfect split!\n", "Split on feature grade_F. (53, 0)\n", "Perfect split!\n", "Split on feature grade_F. (73, 0)\n", "Perfect split!\n", "Split on feature term_ 60 months. (321, 62)\n", "Split on feature home_ownership_RENT. (176, 145)\n", "Split on feature home_ownership_OWN. (131, 45)\n", "Split on feature grade_F. (131, 0)\n", "Perfect split!\n", "Split on feature grade_F. (45, 0)\n", "Perfect split!\n", "Split on feature grade_F. (145, 0)\n", "Perfect split!\n", "Split on feature home_ownership_OWN. (54, 8)\n", "Split on feature home_ownership_RENT. (44, 10)\n", "Split on feature grade_F. (44, 0)\n", "Perfect split!\n", "Split on feature grade_F. (10, 0)\n", "Perfect split!\n", "Split on feature grade_F. (8, 0)\n", "Perfect split!\n", "Split on feature term_ 60 months. (9134, 1055)\n", "Split on feature emp_length_nan. (8739, 395)\n", "Split on feature home_ownership_MORTGAGE. (4700, 4039)\n", "Split on feature emp_length_2 years. (4091, 609)\n", "Split on feature emp_length_6 years. (3795, 296)\n", "Split on feature emp_length_4 years. (3405, 390)\n", "Split on feature home_ownership_OTHER. (3392, 13)\n", "Split on feature emp_length_8 years. (3207, 185)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature emp_length_10+ years. (7, 6)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature home_ownership_RENT. (42, 348)\n", "Split on feature grade_C. (42, 0)\n", "Perfect split!\n", "Split on feature grade_C. (348, 0)\n", "Perfect split!\n", "Split on feature home_ownership_OTHER. (294, 2)\n", "Split on feature home_ownership_RENT. (44, 250)\n", "Split on feature grade_C. (44, 0)\n", "Perfect split!\n", "Split on feature grade_C. (250, 0)\n", "Perfect split!\n", "Split on feature grade_C. (2, 0)\n", "Perfect split!\n", "Split on feature home_ownership_OWN. (547, 62)\n", "Split on feature home_ownership_RENT. (1, 546)\n", "Split on feature grade_C. (1, 0)\n", "Perfect split!\n", "Split on feature grade_C. (546, 0)\n", "Perfect split!\n", "Split on feature grade_C. (62, 0)\n", "Perfect split!\n", "Split on feature emp_length_7 years. (3789, 250)\n", "Split on feature emp_length_6 years. (3510, 279)\n", "Split on feature emp_length_< 1 year. (3231, 279)\n", "Split on feature emp_length_10+ years. (1792, 1439)\n", "Split on feature emp_length_5 years. (1458, 334)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_C. (1439, 0)\n", "Perfect split!\n", "Split on feature grade_C. (279, 0)\n", "Perfect split!\n", "Split on feature grade_C. (279, 0)\n", "Perfect split!\n", "Split on feature grade_C. (250, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (248, 147)\n", "Split on feature home_ownership_OWN. (168, 80)\n", "Split on feature grade_C. (168, 0)\n", "Perfect split!\n", "Split on feature grade_C. (80, 0)\n", "Perfect split!\n", "Split on feature grade_C. (147, 0)\n", "Perfect split!\n", "Split on feature emp_length_7 years. (998, 57)\n", "Split on feature emp_length_2 years. (917, 81)\n", "Split on feature home_ownership_MORTGAGE. (344, 573)\n", "Split on feature emp_length_4 years. (312, 32)\n", "Split on feature emp_length_nan. (296, 16)\n", "Split on feature emp_length_10+ years. (201, 95)\n", "Split on feature emp_length_8 years. (187, 14)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature home_ownership_RENT. (30, 65)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature home_ownership_RENT. (8, 8)\n", "Split on feature grade_C. (8, 0)\n", "Perfect split!\n", "Split on feature grade_C. (8, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (3, 29)\n", "Split on feature grade_C. (3, 0)\n", "Perfect split!\n", "Split on feature grade_C. (29, 0)\n", "Perfect split!\n", "Split on feature emp_length_3 years. (526, 47)\n", "Split on feature emp_length_4 years. (484, 42)\n", "Split on feature emp_length_9 years. (460, 24)\n", "Split on feature emp_length_8 years. (428, 32)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_C. (24, 0)\n", "Perfect split!\n", "Split on feature grade_C. (42, 0)\n", "Perfect split!\n", "Split on feature grade_C. (47, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (46, 35)\n", "Split on feature home_ownership_OWN. (39, 7)\n", "Split on feature grade_C. (39, 0)\n", "Perfect split!\n", "Split on feature grade_C. (7, 0)\n", "Perfect split!\n", "Split on feature grade_C. (35, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (35, 22)\n", "Split on feature home_ownership_OWN. (30, 5)\n", "Split on feature grade_C. (30, 0)\n", "Perfect split!\n", "Split on feature grade_C. (5, 0)\n", "Perfect split!\n", "Split on feature grade_C. (22, 0)\n", "Perfect split!\n", "Split on feature emp_length_nan. (5037, 227)\n", "Split on feature home_ownership_RENT. (3142, 1895)\n", "Split on feature term_ 60 months. (3056, 86)\n", "Split on feature emp_length_2 years. (2781, 275)\n", "Split on feature home_ownership_OWN. (2430, 351)\n", "Split on feature emp_length_4 years. (2263, 167)\n", "Split on feature emp_length_3 years. (2070, 193)\n", "Split on feature emp_length_6 years. (1900, 170)\n", "Split on feature emp_length_< 1 year. (1724, 176)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_C. (170, 0)\n", "Perfect split!\n", "Split on feature home_ownership_MORTGAGE. (1, 192)\n", "Split on feature grade_C. (1, 0)\n", "Perfect split!\n", "Split on feature grade_C. (192, 0)\n", "Perfect split!\n", "Split on feature home_ownership_MORTGAGE. (1, 166)\n", "Split on feature grade_C. (1, 0)\n", "Perfect split!\n", "Split on feature grade_C. (166, 0)\n", "Perfect split!\n", "Split on feature emp_length_9 years. (338, 13)\n", "Split on feature emp_length_4 years. (317, 21)\n", "Split on feature emp_length_5 years. (284, 33)\n", "Split on feature emp_length_6 years. (269, 15)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_C. (33, 0)\n", "Perfect split!\n", "Split on feature grade_C. (21, 0)\n", "Perfect split!\n", "Split on feature grade_C. (13, 0)\n", "Perfect split!\n", "Split on feature home_ownership_OWN. (229, 46)\n", "Split on feature grade_C. (229, 0)\n", "Perfect split!\n", "Split on feature grade_C. (46, 0)\n", "Perfect split!\n", "Split on feature emp_length_< 1 year. (81, 5)\n", "Split on feature emp_length_8 years. (79, 2)\n", "Split on feature emp_length_1 year. (77, 2)\n", "Split on feature emp_length_9 years. (75, 2)\n", "Split on feature emp_length_4 years. (70, 5)\n", "Split on feature emp_length_6 years. (65, 5)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_C. (5, 0)\n", "Perfect split!\n", "Split on feature grade_C. (2, 0)\n", "Perfect split!\n", "Split on feature home_ownership_OWN. (1, 1)\n", "Split on feature grade_C. (1, 0)\n", "Perfect split!\n", "Split on feature grade_C. (1, 0)\n", "Perfect split!\n", "Split on feature grade_C. (2, 0)\n", "Perfect split!\n", "Split on feature home_ownership_OWN. (3, 2)\n", "Split on feature grade_C. (3, 0)\n", "Perfect split!\n", "Split on feature grade_C. (2, 0)\n", "Perfect split!\n", "Split on feature term_ 60 months. (1872, 23)\n", "Split on feature emp_length_10+ years. (1573, 299)\n", "Split on feature emp_length_< 1 year. (1263, 310)\n", "Split on feature emp_length_3 years. (1068, 195)\n", "Split on feature emp_length_7 years. (995, 73)\n", "Split on feature emp_length_6 years. (886, 109)\n", "Split on feature emp_length_1 year. (681, 205)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_C. (109, 0)\n", "Perfect split!\n", "Split on feature grade_C. (73, 0)\n", "Perfect split!\n", "Split on feature grade_C. (195, 0)\n", "Perfect split!\n", "Split on feature grade_C. (310, 0)\n", "Perfect split!\n", "Split on feature grade_C. (299, 0)\n", "Perfect split!\n", "Split on feature emp_length_5 years. (22, 1)\n", "Split on feature emp_length_4 years. (21, 1)\n", "Split on feature emp_length_8 years. (20, 1)\n", "Split on feature emp_length_10+ years. (17, 3)\n", "Split on feature emp_length_< 1 year. (12, 5)\n", "Split on feature emp_length_3 years. (8, 4)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_C. (5, 0)\n", "Perfect split!\n", "Split on feature grade_C. (3, 0)\n", "Perfect split!\n", "Split on feature grade_C. (1, 0)\n", "Perfect split!\n", "Split on feature grade_C. (1, 0)\n", "Perfect split!\n", "Split on feature grade_C. (1, 0)\n", "Perfect split!\n", "Split on feature term_ 60 months. (220, 7)\n", "Split on feature home_ownership_RENT. (148, 72)\n", "Split on feature home_ownership_OWN. (110, 38)\n", "Split on feature grade_C. (110, 0)\n", "Perfect split!\n", "Split on feature grade_C. (38, 0)\n", "Perfect split!\n", "Split on feature grade_C. (72, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (5, 2)\n", "Split on feature grade_C. (5, 0)\n", "Perfect split!\n", "Split on feature grade_C. (2, 0)\n", "Perfect split!\n"]}, {"data": {"text/plain": ["0.6193304535637149"]}, "execution_count": 129, "metadata": {}, "output_type": "execute_result"}], "source": ["model.fit(X_train, y_train)\n", "model.score(X_test, y_test)"]}, {"cell_type": "code", "execution_count": 130, "id": "b337b4f1", "metadata": {}, "outputs": [{"data": {"text/plain": ["186"]}, "execution_count": 130, "metadata": {}, "output_type": "execute_result"}], "source": ["model.count_leaves()"]}, {"cell_type": "code", "execution_count": 133, "id": "579cf7d5", "metadata": {}, "outputs": [], "source": ["model_1 = DecisionTree(max_depth=3, min_error_reduction=1e-15)\n", "model_2 = DecisionTree(max_depth=7, min_error_reduction=1e-15)\n", "model_3 = DecisionTree(max_depth=15, min_error_reduction=1e-15)"]}, {"cell_type": "code", "execution_count": 134, "id": "78ab9dac", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Split on feature grade_A. (31776, 5264)\n", "Split on feature grade_B. (21587, 10189)\n", "Split on feature grade_C. (12308, 9279)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature term_ 60 months. (9134, 1055)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature emp_length_nan. (5037, 227)\n", "Split on feature home_ownership_RENT. (3142, 1895)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature term_ 60 months. (220, 7)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_A. (31776, 5264)\n", "Split on feature grade_B. (21587, 10189)\n", "Split on feature grade_C. (12308, 9279)\n", "Split on feature grade_D. (5553, 6755)\n", "Split on feature term_ 60 months. (1743, 3810)\n", "Split on feature grade_E. (459, 1284)\n", "Split on feature emp_length_10+ years. (358, 101)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature emp_length_nan. (1223, 61)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature home_ownership_MORTGAGE. (1919, 1891)\n", "Split on feature emp_length_nan. (1879, 40)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature emp_length_4 years. (1775, 116)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature home_ownership_MORTGAGE. (4055, 2700)\n", "Split on feature term_ 60 months. (3136, 919)\n", "Split on feature emp_length_nan. (2992, 144)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature emp_length_nan. (894, 25)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature term_ 60 months. (1571, 1129)\n", "Split on feature emp_length_5 years. (1457, 114)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature emp_length_7 years. (1056, 73)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature emp_length_nan. (8896, 383)\n", "Split on feature term_ 60 months. (6804, 2092)\n", "Split on feature home_ownership_MORTGAGE. (4058, 2746)\n", "Split on feature emp_length_2 years. (3553, 505)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature emp_length_10+ years. (1779, 967)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature home_ownership_MORTGAGE. (756, 1336)\n", "Split on feature emp_length_5 years. (693, 63)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature emp_length_2 years. (1263, 73)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature term_ 60 months. (321, 62)\n", "Split on feature home_ownership_RENT. (176, 145)\n", "Split on feature home_ownership_OWN. (131, 45)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_F. (145, 0)\n", "Perfect split!\n", "Split on feature home_ownership_OWN. (54, 8)\n", "Split on feature home_ownership_RENT. (44, 10)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_F. (8, 0)\n", "Perfect split!\n", "Split on feature term_ 60 months. (9134, 1055)\n", "Split on feature emp_length_nan. (8739, 395)\n", "Split on feature home_ownership_MORTGAGE. (4700, 4039)\n", "Split on feature emp_length_2 years. (4091, 609)\n", "Split on feature emp_length_6 years. (3795, 296)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature home_ownership_OWN. (547, 62)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature emp_length_7 years. (3789, 250)\n", "Split on feature emp_length_6 years. (3510, 279)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_C. (250, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (248, 147)\n", "Split on feature home_ownership_OWN. (168, 80)\n", "Split on feature grade_C. (168, 0)\n", "Perfect split!\n", "Split on feature grade_C. (80, 0)\n", "Perfect split!\n", "Split on feature grade_C. (147, 0)\n", "Perfect split!\n", "Split on feature emp_length_7 years. (998, 57)\n", "Split on feature emp_length_2 years. (917, 81)\n", "Split on feature home_ownership_MORTGAGE. (344, 573)\n", "Split on feature emp_length_4 years. (312, 32)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature emp_length_3 years. (526, 47)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature home_ownership_RENT. (46, 35)\n", "Split on feature home_ownership_OWN. (39, 7)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_C. (35, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (35, 22)\n", "Split on feature home_ownership_OWN. (30, 5)\n", "Split on feature grade_C. (30, 0)\n", "Perfect split!\n", "Split on feature grade_C. (5, 0)\n", "Perfect split!\n", "Split on feature grade_C. (22, 0)\n", "Perfect split!\n", "Split on feature emp_length_nan. (5037, 227)\n", "Split on feature home_ownership_RENT. (3142, 1895)\n", "Split on feature term_ 60 months. (3056, 86)\n", "Split on feature emp_length_2 years. (2781, 275)\n", "Split on feature home_ownership_OWN. (2430, 351)\n", "Split on feature emp_length_4 years. (2263, 167)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature emp_length_9 years. (338, 13)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature home_ownership_OWN. (229, 46)\n", "Split on feature grade_C. (229, 0)\n", "Perfect split!\n", "Split on feature grade_C. (46, 0)\n", "Perfect split!\n", "Split on feature emp_length_< 1 year. (81, 5)\n", "Split on feature emp_length_8 years. (79, 2)\n", "Split on feature emp_length_1 year. (77, 2)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_C. (2, 0)\n", "Perfect split!\n", "Split on feature home_ownership_OWN. (3, 2)\n", "Split on feature grade_C. (3, 0)\n", "Perfect split!\n", "Split on feature grade_C. (2, 0)\n", "Perfect split!\n", "Split on feature term_ 60 months. (1872, 23)\n", "Split on feature emp_length_10+ years. (1573, 299)\n", "Split on feature emp_length_< 1 year. (1263, 310)\n", "Split on feature emp_length_3 years. (1068, 195)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_C. (310, 0)\n", "Perfect split!\n", "Split on feature grade_C. (299, 0)\n", "Perfect split!\n", "Split on feature emp_length_5 years. (22, 1)\n", "Split on feature emp_length_4 years. (21, 1)\n", "Split on feature emp_length_8 years. (20, 1)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_C. (1, 0)\n", "Perfect split!\n", "Split on feature grade_C. (1, 0)\n", "Perfect split!\n", "Split on feature term_ 60 months. (220, 7)\n", "Split on feature home_ownership_RENT. (148, 72)\n", "Split on feature home_ownership_OWN. (110, 38)\n", "Split on feature grade_C. (110, 0)\n", "Perfect split!\n", "Split on feature grade_C. (38, 0)\n", "Perfect split!\n", "Split on feature grade_C. (72, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (5, 2)\n", "Split on feature grade_C. (5, 0)\n", "Perfect split!\n", "Split on feature grade_C. (2, 0)\n", "Perfect split!\n", "Split on feature grade_A. (31776, 5264)\n", "Split on feature grade_B. (21587, 10189)\n", "Split on feature grade_C. (12308, 9279)\n", "Split on feature grade_D. (5553, 6755)\n", "Split on feature term_ 60 months. (1743, 3810)\n", "Split on feature grade_E. (459, 1284)\n", "Split on feature emp_length_10+ years. (358, 101)\n", "Split on feature emp_length_6 years. (328, 30)\n", "Split on feature home_ownership_OTHER. (325, 3)\n", "Split on feature emp_length_4 years. (297, 28)\n", "Split on feature emp_length_2 years. (248, 49)\n", "Split on feature emp_length_5 years. (217, 31)\n", "Split on feature grade_F. (51, 166)\n", "Split on feature emp_length_7 years. (44, 7)\n", "Split on feature home_ownership_MORTGAGE. (13, 31)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature home_ownership_RENT. (6, 1)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature emp_length_7 years. (151, 15)\n", "Split on feature home_ownership_OWN. (137, 14)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature home_ownership_RENT. (8, 7)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_F. (6, 25)\n", "Split on feature grade_G. (0, 6)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (10, 15)\n", "Split on feature home_ownership_OWN. (8, 2)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_G. (15, 0)\n", "Perfect split!\n", "Split on feature home_ownership_OWN. (44, 5)\n", "Split on feature grade_F. (11, 33)\n", "Split on feature home_ownership_RENT. (3, 8)\n", "Split on feature grade_G. (0, 3)\n", "Perfect split!\n", "Split on feature grade_G. (0, 8)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (7, 26)\n", "Split on feature grade_G. (7, 0)\n", "Perfect split!\n", "Split on feature grade_G. (26, 0)\n", "Perfect split!\n", "Split on feature grade_F. (1, 4)\n", "Split on feature grade_G. (0, 1)\n", "Perfect split!\n", "Split on feature grade_G. (4, 0)\n", "Perfect split!\n", "Split on feature grade_F. (7, 21)\n", "Split on feature grade_G. (0, 7)\n", "Perfect split!\n", "Split on feature home_ownership_OWN. (19, 2)\n", "Split on feature home_ownership_RENT. (7, 12)\n", "Split on feature grade_G. (7, 0)\n", "Perfect split!\n", "Split on feature grade_G. (12, 0)\n", "Perfect split!\n", "Split on feature grade_G. (2, 0)\n", "Perfect split!\n", "Split on feature grade_F. (2, 1)\n", "Split on feature grade_G. (0, 2)\n", "Perfect split!\n", "Split on feature grade_G. (1, 0)\n", "Perfect split!\n", "Split on feature home_ownership_MORTGAGE. (23, 7)\n", "Split on feature home_ownership_OWN. (21, 2)\n", "Split on feature grade_F. (1, 20)\n", "Split on feature grade_G. (0, 1)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (1, 19)\n", "Split on feature grade_G. (1, 0)\n", "Perfect split!\n", "Split on feature grade_G. (19, 0)\n", "Perfect split!\n", "Split on feature grade_F. (0, 2)\n", "Perfect split!\n", "Split on feature grade_F. (0, 7)\n", "Perfect split!\n", "Split on feature grade_F. (25, 76)\n", "Split on feature home_ownership_OWN. (21, 4)\n", "Split on feature home_ownership_RENT. (13, 8)\n", "Split on feature grade_G. (0, 13)\n", "Perfect split!\n", "Split on feature grade_G. (0, 8)\n", "Perfect split!\n", "Split on feature grade_G. (0, 4)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (47, 29)\n", "Split on feature home_ownership_OWN. (39, 8)\n", "Split on feature grade_G. (39, 0)\n", "Perfect split!\n", "Split on feature grade_G. (8, 0)\n", "Perfect split!\n", "Split on feature grade_G. (29, 0)\n", "Perfect split!\n", "Split on feature emp_length_nan. (1223, 61)\n", "Split on feature emp_length_1 year. (1117, 106)\n", "Split on feature home_ownership_OTHER. (1113, 4)\n", "Split on feature emp_length_2 years. (963, 150)\n", "Split on feature home_ownership_MORTGAGE. (630, 333)\n", "Split on feature emp_length_10+ years. (475, 155)\n", "Split on feature emp_length_9 years. (446, 29)\n", "Split on feature emp_length_7 years. (402, 44)\n", "Split on feature emp_length_8 years. (367, 35)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature home_ownership_RENT. (6, 38)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature home_ownership_RENT. (5, 24)\n", "Split on feature grade_F. (5, 0)\n", "Perfect split!\n", "Split on feature grade_F. (24, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (25, 130)\n", "Split on feature grade_F. (25, 0)\n", "Perfect split!\n", "Split on feature grade_F. (130, 0)\n", "Perfect split!\n", "Split on feature emp_length_< 1 year. (303, 30)\n", "Split on feature emp_length_9 years. (287, 16)\n", "Split on feature emp_length_4 years. (252, 35)\n", "Split on feature emp_length_5 years. (222, 30)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_F. (35, 0)\n", "Perfect split!\n", "Split on feature grade_F. (16, 0)\n", "Perfect split!\n", "Split on feature grade_F. (30, 0)\n", "Perfect split!\n", "Split on feature home_ownership_OWN. (135, 15)\n", "Split on feature home_ownership_RENT. (37, 98)\n", "Split on feature grade_F. (37, 0)\n", "Perfect split!\n", "Split on feature grade_F. (98, 0)\n", "Perfect split!\n", "Split on feature grade_F. (15, 0)\n", "Perfect split!\n", "Split on feature grade_F. (4, 0)\n", "Perfect split!\n", "Split on feature home_ownership_MORTGAGE. (75, 31)\n", "Split on feature home_ownership_OTHER. (74, 1)\n", "Split on feature home_ownership_RENT. (7, 67)\n", "Split on feature grade_F. (7, 0)\n", "Perfect split!\n", "Split on feature grade_F. (67, 0)\n", "Perfect split!\n", "Split on feature grade_F. (1, 0)\n", "Perfect split!\n", "Split on feature grade_F. (31, 0)\n", "Perfect split!\n", "Split on feature home_ownership_OWN. (55, 6)\n", "Split on feature home_ownership_RENT. (17, 38)\n", "Split on feature grade_F. (17, 0)\n", "Perfect split!\n", "Split on feature grade_F. (38, 0)\n", "Perfect split!\n", "Split on feature grade_F. (6, 0)\n", "Perfect split!\n", "Split on feature home_ownership_MORTGAGE. (1919, 1891)\n", "Split on feature emp_length_nan. (1879, 40)\n", "Split on feature grade_E. (865, 1014)\n", "Split on feature emp_length_9 years. (837, 28)\n", "Split on feature emp_length_6 years. (789, 48)\n", "Split on feature emp_length_3 years. (711, 78)\n", "Split on feature home_ownership_RENT. (113, 598)\n", "Split on feature grade_F. (29, 84)\n", "Split on feature emp_length_10+ years. (14, 15)\n", "Split on feature grade_G. (0, 14)\n", "Perfect split!\n", "Split on feature grade_G. (0, 15)\n", "Perfect split!\n", "Split on feature emp_length_7 years. (80, 4)\n", "Split on feature emp_length_1 year. (71, 9)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_G. (4, 0)\n", "Perfect split!\n", "Split on feature emp_length_8 years. (565, 33)\n", "Split on feature emp_length_1 year. (513, 52)\n", "Split on feature emp_length_< 1 year. (439, 74)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_F. (16, 36)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_F. (4, 29)\n", "Split on feature grade_G. (0, 4)\n", "Perfect split!\n", "Split on feature grade_G. (29, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (9, 69)\n", "Split on feature grade_F. (2, 7)\n", "Split on feature grade_G. (0, 2)\n", "Perfect split!\n", "Split on feature grade_G. (7, 0)\n", "Perfect split!\n", "Split on feature grade_F. (13, 56)\n", "Split on feature grade_G. (0, 13)\n", "Perfect split!\n", "Split on feature grade_G. (56, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (8, 40)\n", "Split on feature grade_F. (2, 6)\n", "Split on feature grade_G. (0, 2)\n", "Perfect split!\n", "Split on feature grade_G. (6, 0)\n", "Perfect split!\n", "Split on feature grade_F. (8, 32)\n", "Split on feature grade_G. (0, 8)\n", "Perfect split!\n", "Split on feature grade_G. (32, 0)\n", "Perfect split!\n", "Split on feature grade_F. (4, 24)\n", "Split on feature grade_G. (0, 4)\n", "Perfect split!\n", "Split on feature grade_G. (24, 0)\n", "Perfect split!\n", "Split on feature emp_length_5 years. (923, 91)\n", "Split on feature home_ownership_OWN. (790, 133)\n", "Split on feature emp_length_7 years. (736, 54)\n", "Split on feature emp_length_6 years. (684, 52)\n", "Split on feature emp_length_8 years. (650, 34)\n", "Split on feature emp_length_9 years. (612, 38)\n", "Split on feature home_ownership_RENT. (1, 611)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_F. (38, 0)\n", "Perfect split!\n", "Split on feature grade_F. (34, 0)\n", "Perfect split!\n", "Split on feature grade_F. (52, 0)\n", "Perfect split!\n", "Split on feature grade_F. (54, 0)\n", "Perfect split!\n", "Split on feature emp_length_3 years. (120, 13)\n", "Split on feature emp_length_8 years. (112, 8)\n", "Split on feature emp_length_9 years. (107, 5)\n", "Split on feature emp_length_2 years. (92, 15)\n", "Split on feature emp_length_6 years. (82, 10)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_F. (15, 0)\n", "Perfect split!\n", "Split on feature grade_F. (5, 0)\n", "Perfect split!\n", "Split on feature grade_F. (8, 0)\n", "Perfect split!\n", "Split on feature grade_F. (13, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (14, 77)\n", "Split on feature grade_F. (14, 0)\n", "Perfect split!\n", "Split on feature grade_F. (77, 0)\n", "Perfect split!\n", "Split on feature grade_F. (23, 17)\n", "Split on feature grade_E. (5, 18)\n", "Split on feature home_ownership_RENT. (1, 4)\n", "Split on feature grade_G. (0, 1)\n", "Perfect split!\n", "Split on feature grade_G. (0, 4)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (4, 14)\n", "Split on feature grade_G. (4, 0)\n", "Perfect split!\n", "Split on feature grade_G. (14, 0)\n", "Perfect split!\n", "Split on feature grade_E. (17, 0)\n", "Perfect split!\n", "Split on feature emp_length_4 years. (1775, 116)\n", "Split on feature emp_length_7 years. (1642, 133)\n", "Split on feature grade_F. (1100, 542)\n", "Split on feature emp_length_9 years. (1038, 62)\n", "Split on feature emp_length_3 years. (947, 91)\n", "Split on feature emp_length_< 1 year. (891, 56)\n", "Split on feature emp_length_2 years. (795, 96)\n", "Split on feature emp_length_5 years. (705, 90)\n", "Split on feature grade_E. (96, 609)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_E. (9, 81)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_E. (6, 90)\n", "Split on feature grade_G. (0, 6)\n", "Perfect split!\n", "Split on feature grade_G. (90, 0)\n", "Perfect split!\n", "Split on feature grade_E. (7, 49)\n", "Split on feature grade_G. (0, 7)\n", "Perfect split!\n", "Split on feature grade_G. (49, 0)\n", "Perfect split!\n", "Split on feature grade_E. (8, 83)\n", "Split on feature grade_G. (0, 8)\n", "Perfect split!\n", "Split on feature grade_G. (83, 0)\n", "Perfect split!\n", "Split on feature grade_E. (8, 54)\n", "Split on feature grade_G. (0, 8)\n", "Perfect split!\n", "Split on feature grade_G. (54, 0)\n", "Perfect split!\n", "Split on feature emp_length_nan. (533, 9)\n", "Split on feature emp_length_3 years. (495, 38)\n", "Split on feature emp_length_1 year. (463, 32)\n", "Split on feature emp_length_8 years. (433, 30)\n", "Split on feature emp_length_6 years. (388, 45)\n", "Split on feature emp_length_2 years. (340, 48)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_E. (45, 0)\n", "Perfect split!\n", "Split on feature grade_E. (30, 0)\n", "Perfect split!\n", "Split on feature grade_E. (32, 0)\n", "Perfect split!\n", "Split on feature grade_E. (38, 0)\n", "Perfect split!\n", "Split on feature grade_E. (9, 0)\n", "Perfect split!\n", "Split on feature grade_E. (47, 86)\n", "Split on feature grade_F. (11, 36)\n", "Split on feature grade_G. (0, 11)\n", "Perfect split!\n", "Split on feature grade_G. (36, 0)\n", "Perfect split!\n", "Split on feature grade_F. (86, 0)\n", "Perfect split!\n", "Split on feature grade_G. (109, 7)\n", "Split on feature grade_F. (71, 38)\n", "Split on feature grade_E. (0, 71)\n", "Perfect split!\n", "Split on feature grade_E. (38, 0)\n", "Perfect split!\n", "Split on feature grade_F. (7, 0)\n", "Perfect split!\n", "Split on feature home_ownership_MORTGAGE. (4055, 2700)\n", "Split on feature term_ 60 months. (3136, 919)\n", "Split on feature emp_length_nan. (2992, 144)\n", "Split on feature emp_length_2 years. (2621, 371)\n", "Split on feature emp_length_3 years. (2316, 305)\n", "Split on feature emp_length_6 years. (2128, 188)\n", "Split on feature emp_length_7 years. (1974, 154)\n", "Split on feature emp_length_4 years. (1738, 236)\n", "Split on feature home_ownership_OWN. (1478, 260)\n", "Split on feature home_ownership_RENT. (4, 1474)\n", "Split on feature grade_F. (4, 0)\n", "Perfect split!\n", "Split on feature emp_length_< 1 year. (1182, 292)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature emp_length_9 years. (248, 12)\n", "Split on feature emp_length_10+ years. (130, 118)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_F. (12, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (25, 211)\n", "Split on feature grade_F. (25, 0)\n", "Perfect split!\n", "Split on feature grade_F. (211, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (23, 131)\n", "Split on feature grade_F. (23, 0)\n", "Perfect split!\n", "Split on feature grade_F. (131, 0)\n", "Perfect split!\n", "Split on feature home_ownership_OTHER. (187, 1)\n", "Split on feature home_ownership_RENT. (26, 161)\n", "Split on feature grade_F. (26, 0)\n", "Perfect split!\n", "Split on feature grade_F. (161, 0)\n", "Perfect split!\n", "Split on feature grade_F. (1, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (42, 263)\n", "Split on feature home_ownership_OWN. (2, 40)\n", "Split on feature grade_F. (2, 0)\n", "Perfect split!\n", "Split on feature grade_F. (40, 0)\n", "Perfect split!\n", "Split on feature grade_F. (263, 0)\n", "Perfect split!\n", "Split on feature home_ownership_OTHER. (370, 1)\n", "Split on feature home_ownership_RENT. (32, 338)\n", "Split on feature grade_F. (32, 0)\n", "Perfect split!\n", "Split on feature grade_F. (338, 0)\n", "Perfect split!\n", "Split on feature grade_F. (1, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (42, 102)\n", "Split on feature grade_F. (42, 0)\n", "Perfect split!\n", "Split on feature grade_F. (102, 0)\n", "Perfect split!\n", "Split on feature emp_length_nan. (894, 25)\n", "Split on feature emp_length_4 years. (831, 63)\n", "Split on feature emp_length_2 years. (750, 81)\n", "Split on feature emp_length_9 years. (716, 34)\n", "Split on feature emp_length_< 1 year. (632, 84)\n", "Split on feature home_ownership_RENT. (121, 511)\n", "Split on feature emp_length_5 years. (107, 14)\n", "Split on feature emp_length_7 years. (94, 13)\n", "Split on feature emp_length_8 years. (89, 5)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_F. (13, 0)\n", "Perfect split!\n", "Split on feature grade_F. (14, 0)\n", "Perfect split!\n", "Split on feature emp_length_7 years. (473, 38)\n", "Split on feature emp_length_10+ years. (283, 190)\n", "Split on feature emp_length_1 year. (224, 59)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_F. (190, 0)\n", "Perfect split!\n", "Split on feature grade_F. (38, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (8, 76)\n", "Split on feature home_ownership_OWN. (1, 7)\n", "Split on feature grade_F. (1, 0)\n", "Perfect split!\n", "Split on feature grade_F. (7, 0)\n", "Perfect split!\n", "Split on feature grade_F. (76, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (6, 28)\n", "Split on feature grade_F. (6, 0)\n", "Perfect split!\n", "Split on feature grade_F. (28, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (10, 71)\n", "Split on feature grade_F. (10, 0)\n", "Perfect split!\n", "Split on feature grade_F. (71, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (9, 54)\n", "Split on feature grade_F. (9, 0)\n", "Perfect split!\n", "Split on feature grade_F. (54, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (6, 19)\n", "Split on feature grade_F. (6, 0)\n", "Perfect split!\n", "Split on feature grade_F. (19, 0)\n", "Perfect split!\n", "Split on feature term_ 60 months. (1571, 1129)\n", "Split on feature emp_length_5 years. (1457, 114)\n", "Split on feature emp_length_3 years. (1337, 120)\n", "Split on feature emp_length_1 year. (1254, 83)\n", "Split on feature emp_length_9 years. (1197, 57)\n", "Split on feature emp_length_4 years. (1090, 107)\n", "Split on feature emp_length_7 years. (998, 92)\n", "Split on feature emp_length_2 years. (868, 130)\n", "Split on feature emp_length_8 years. (799, 69)\n", "Split on feature emp_length_< 1 year. (699, 100)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_F. (69, 0)\n", "Perfect split!\n", "Split on feature grade_F. (130, 0)\n", "Perfect split!\n", "Split on feature grade_F. (92, 0)\n", "Perfect split!\n", "Split on feature grade_F. (107, 0)\n", "Perfect split!\n", "Split on feature grade_F. (57, 0)\n", "Perfect split!\n", "Split on feature grade_F. (83, 0)\n", "Perfect split!\n", "Split on feature grade_F. (120, 0)\n", "Perfect split!\n", "Split on feature grade_F. (114, 0)\n", "Perfect split!\n", "Split on feature emp_length_7 years. (1056, 73)\n", "Split on feature emp_length_10+ years. (576, 480)\n", "Split on feature emp_length_2 years. (487, 89)\n", "Split on feature emp_length_6 years. (421, 66)\n", "Split on feature emp_length_5 years. (346, 75)\n", "Split on feature emp_length_8 years. (294, 52)\n", "Split on feature emp_length_1 year. (248, 46)\n", "Split on feature emp_length_4 years. (199, 49)\n", "Split on feature emp_length_nan. (170, 29)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_F. (49, 0)\n", "Perfect split!\n", "Split on feature grade_F. (46, 0)\n", "Perfect split!\n", "Split on feature grade_F. (52, 0)\n", "Perfect split!\n", "Split on feature grade_F. (75, 0)\n", "Perfect split!\n", "Split on feature grade_F. (66, 0)\n", "Perfect split!\n", "Split on feature grade_F. (89, 0)\n", "Perfect split!\n", "Split on feature grade_F. (480, 0)\n", "Perfect split!\n", "Split on feature grade_F. (73, 0)\n", "Perfect split!\n", "Split on feature emp_length_nan. (8896, 383)\n", "Split on feature term_ 60 months. (6804, 2092)\n", "Split on feature home_ownership_MORTGAGE. (4058, 2746)\n", "Split on feature emp_length_2 years. (3553, 505)\n", "Split on feature emp_length_7 years. (3355, 198)\n", "Split on feature home_ownership_OTHER. (3347, 8)\n", "Split on feature emp_length_4 years. (3019, 328)\n", "Split on feature home_ownership_RENT. (426, 2593)\n", "Split on feature emp_length_< 1 year. (369, 57)\n", "Split on feature emp_length_1 year. (331, 38)\n", "Split on feature emp_length_3 years. (290, 41)\n", "Split on feature emp_length_6 years. (251, 39)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_F. (41, 0)\n", "Perfect split!\n", "Split on feature grade_F. (38, 0)\n", "Perfect split!\n", "Split on feature grade_F. (57, 0)\n", "Perfect split!\n", "Split on feature emp_length_< 1 year. (2145, 448)\n", "Split on feature emp_length_1 year. (1802, 343)\n", "Split on feature emp_length_8 years. (1638, 164)\n", "Split on feature emp_length_9 years. (1532, 106)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_F. (164, 0)\n", "Perfect split!\n", "Split on feature grade_F. (343, 0)\n", "Perfect split!\n", "Split on feature grade_F. (448, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (45, 283)\n", "Split on feature grade_F. (45, 0)\n", "Perfect split!\n", "Split on feature grade_F. (283, 0)\n", "Perfect split!\n", "Split on feature emp_length_< 1 year. (6, 2)\n", "Split on feature emp_length_1 year. (3, 3)\n", "Split on feature grade_F. (3, 0)\n", "Perfect split!\n", "Split on feature grade_F. (3, 0)\n", "Perfect split!\n", "Split on feature grade_F. (2, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (31, 167)\n", "Split on feature grade_F. (31, 0)\n", "Perfect split!\n", "Split on feature grade_F. (167, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (54, 451)\n", "Split on feature grade_F. (54, 0)\n", "Perfect split!\n", "Split on feature grade_F. (451, 0)\n", "Perfect split!\n", "Split on feature emp_length_10+ years. (1779, 967)\n", "Split on feature emp_length_3 years. (1578, 201)\n", "Split on feature emp_length_7 years. (1396, 182)\n", "Split on feature emp_length_8 years. (1243, 153)\n", "Split on feature emp_length_5 years. (1009, 234)\n", "Split on feature emp_length_9 years. (901, 108)\n", "Split on feature emp_length_1 year. (747, 154)\n", "Split on feature emp_length_< 1 year. (582, 165)\n", "Split on feature emp_length_2 years. (364, 218)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_F. (165, 0)\n", "Perfect split!\n", "Split on feature grade_F. (154, 0)\n", "Perfect split!\n", "Split on feature grade_F. (108, 0)\n", "Perfect split!\n", "Split on feature grade_F. (234, 0)\n", "Perfect split!\n", "Split on feature grade_F. (153, 0)\n", "Perfect split!\n", "Split on feature grade_F. (182, 0)\n", "Perfect split!\n", "Split on feature grade_F. (201, 0)\n", "Perfect split!\n", "Split on feature grade_F. (967, 0)\n", "Perfect split!\n", "Split on feature home_ownership_MORTGAGE. (756, 1336)\n", "Split on feature emp_length_5 years. (693, 63)\n", "Split on feature emp_length_< 1 year. (606, 87)\n", "Split on feature emp_length_9 years. (574, 32)\n", "Split on feature emp_length_6 years. (538, 36)\n", "Split on feature emp_length_1 year. (489, 49)\n", "Split on feature emp_length_3 years. (419, 70)\n", "Split on feature emp_length_10+ years. (209, 210)\n", "Split on feature home_ownership_RENT. (34, 175)\n", "Split on feature emp_length_4 years. (25, 9)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature emp_length_7 years. (144, 31)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature home_ownership_RENT. (51, 159)\n", "Split on feature grade_F. (51, 0)\n", "Perfect split!\n", "Split on feature grade_F. (159, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (16, 54)\n", "Split on feature grade_F. (16, 0)\n", "Perfect split!\n", "Split on feature grade_F. (54, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (5, 44)\n", "Split on feature grade_F. (5, 0)\n", "Perfect split!\n", "Split on feature grade_F. (44, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (3, 33)\n", "Split on feature grade_F. (3, 0)\n", "Perfect split!\n", "Split on feature grade_F. (33, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (10, 22)\n", "Split on feature grade_F. (10, 0)\n", "Perfect split!\n", "Split on feature grade_F. (22, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (5, 82)\n", "Split on feature grade_F. (5, 0)\n", "Perfect split!\n", "Split on feature grade_F. (82, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (7, 56)\n", "Split on feature grade_F. (7, 0)\n", "Perfect split!\n", "Split on feature grade_F. (56, 0)\n", "Perfect split!\n", "Split on feature emp_length_2 years. (1263, 73)\n", "Split on feature emp_length_1 year. (1210, 53)\n", "Split on feature emp_length_7 years. (1131, 79)\n", "Split on feature emp_length_3 years. (1047, 84)\n", "Split on feature emp_length_9 years. (970, 77)\n", "Split on feature emp_length_4 years. (893, 77)\n", "Split on feature emp_length_8 years. (811, 82)\n", "Split on feature emp_length_10+ years. (246, 565)\n", "Split on feature emp_length_< 1 year. (171, 75)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_F. (565, 0)\n", "Perfect split!\n", "Split on feature grade_F. (82, 0)\n", "Perfect split!\n", "Split on feature grade_F. (77, 0)\n", "Perfect split!\n", "Split on feature grade_F. (77, 0)\n", "Perfect split!\n", "Split on feature grade_F. (84, 0)\n", "Perfect split!\n", "Split on feature grade_F. (79, 0)\n", "Perfect split!\n", "Split on feature grade_F. (53, 0)\n", "Perfect split!\n", "Split on feature grade_F. (73, 0)\n", "Perfect split!\n", "Split on feature term_ 60 months. (321, 62)\n", "Split on feature home_ownership_RENT. (176, 145)\n", "Split on feature home_ownership_OWN. (131, 45)\n", "Split on feature grade_F. (131, 0)\n", "Perfect split!\n", "Split on feature grade_F. (45, 0)\n", "Perfect split!\n", "Split on feature grade_F. (145, 0)\n", "Perfect split!\n", "Split on feature home_ownership_OWN. (54, 8)\n", "Split on feature home_ownership_RENT. (44, 10)\n", "Split on feature grade_F. (44, 0)\n", "Perfect split!\n", "Split on feature grade_F. (10, 0)\n", "Perfect split!\n", "Split on feature grade_F. (8, 0)\n", "Perfect split!\n", "Split on feature term_ 60 months. (9134, 1055)\n", "Split on feature emp_length_nan. (8739, 395)\n", "Split on feature home_ownership_MORTGAGE. (4700, 4039)\n", "Split on feature emp_length_2 years. (4091, 609)\n", "Split on feature emp_length_6 years. (3795, 296)\n", "Split on feature emp_length_4 years. (3405, 390)\n", "Split on feature home_ownership_OTHER. (3392, 13)\n", "Split on feature emp_length_8 years. (3207, 185)\n", "Split on feature home_ownership_RENT. (462, 2745)\n", "Split on feature emp_length_7 years. (428, 34)\n", "Split on feature emp_length_< 1 year. (359, 69)\n", "Split on feature emp_length_5 years. (319, 40)\n", "Split on feature emp_length_3 years. (256, 63)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_C. (40, 0)\n", "Perfect split!\n", "Split on feature grade_C. (69, 0)\n", "Perfect split!\n", "Split on feature grade_C. (34, 0)\n", "Perfect split!\n", "Split on feature emp_length_3 years. (2302, 443)\n", "Split on feature emp_length_7 years. (2081, 221)\n", "Split on feature emp_length_10+ years. (1338, 743)\n", "Split on feature emp_length_1 year. (957, 381)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_C. (743, 0)\n", "Perfect split!\n", "Split on feature grade_C. (221, 0)\n", "Perfect split!\n", "Split on feature grade_C. (443, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (26, 159)\n", "Split on feature grade_C. (26, 0)\n", "Perfect split!\n", "Split on feature grade_C. (159, 0)\n", "Perfect split!\n", "Split on feature emp_length_10+ years. (7, 6)\n", "Split on feature emp_length_3 years. (6, 1)\n", "Split on feature emp_length_5 years. (5, 1)\n", "Split on feature emp_length_< 1 year. (1, 4)\n", "Split on feature grade_C. (1, 0)\n", "Perfect split!\n", "Split on feature grade_C. (4, 0)\n", "Perfect split!\n", "Split on feature grade_C. (1, 0)\n", "Perfect split!\n", "Split on feature grade_C. (1, 0)\n", "Perfect split!\n", "Split on feature grade_C. (6, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (42, 348)\n", "Split on feature grade_C. (42, 0)\n", "Perfect split!\n", "Split on feature grade_C. (348, 0)\n", "Perfect split!\n", "Split on feature home_ownership_OTHER. (294, 2)\n", "Split on feature home_ownership_RENT. (44, 250)\n", "Split on feature grade_C. (44, 0)\n", "Perfect split!\n", "Split on feature grade_C. (250, 0)\n", "Perfect split!\n", "Split on feature grade_C. (2, 0)\n", "Perfect split!\n", "Split on feature home_ownership_OWN. (547, 62)\n", "Split on feature home_ownership_RENT. (1, 546)\n", "Split on feature grade_C. (1, 0)\n", "Perfect split!\n", "Split on feature grade_C. (546, 0)\n", "Perfect split!\n", "Split on feature grade_C. (62, 0)\n", "Perfect split!\n", "Split on feature emp_length_7 years. (3789, 250)\n", "Split on feature emp_length_6 years. (3510, 279)\n", "Split on feature emp_length_< 1 year. (3231, 279)\n", "Split on feature emp_length_10+ years. (1792, 1439)\n", "Split on feature emp_length_5 years. (1458, 334)\n", "Split on feature emp_length_1 year. (1253, 205)\n", "Split on feature emp_length_4 years. (1000, 253)\n", "Split on feature emp_length_9 years. (828, 172)\n", "Split on feature emp_length_8 years. (648, 180)\n", "Split on feature emp_length_3 years. (342, 306)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_C. (180, 0)\n", "Perfect split!\n", "Split on feature grade_C. (172, 0)\n", "Perfect split!\n", "Split on feature grade_C. (253, 0)\n", "Perfect split!\n", "Split on feature grade_C. (205, 0)\n", "Perfect split!\n", "Split on feature grade_C. (334, 0)\n", "Perfect split!\n", "Split on feature grade_C. (1439, 0)\n", "Perfect split!\n", "Split on feature grade_C. (279, 0)\n", "Perfect split!\n", "Split on feature grade_C. (279, 0)\n", "Perfect split!\n", "Split on feature grade_C. (250, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (248, 147)\n", "Split on feature home_ownership_OWN. (168, 80)\n", "Split on feature grade_C. (168, 0)\n", "Perfect split!\n", "Split on feature grade_C. (80, 0)\n", "Perfect split!\n", "Split on feature grade_C. (147, 0)\n", "Perfect split!\n", "Split on feature emp_length_7 years. (998, 57)\n", "Split on feature emp_length_2 years. (917, 81)\n", "Split on feature home_ownership_MORTGAGE. (344, 573)\n", "Split on feature emp_length_4 years. (312, 32)\n", "Split on feature emp_length_nan. (296, 16)\n", "Split on feature emp_length_10+ years. (201, 95)\n", "Split on feature emp_length_8 years. (187, 14)\n", "Split on feature home_ownership_RENT. (34, 153)\n", "Split on feature emp_length_9 years. (31, 3)\n", "Split on feature emp_length_1 year. (25, 6)\n", "Split on feature emp_length_6 years. (21, 4)\n", "Split on feature emp_length_< 1 year. (10, 11)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_C. (4, 0)\n", "Perfect split!\n", "Split on feature grade_C. (6, 0)\n", "Perfect split!\n", "Split on feature grade_C. (3, 0)\n", "Perfect split!\n", "Split on feature emp_length_6 years. (127, 26)\n", "Split on feature emp_length_5 years. (102, 25)\n", "Split on feature emp_length_9 years. (88, 14)\n", "Split on feature emp_length_1 year. (64, 24)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_C. (14, 0)\n", "Perfect split!\n", "Split on feature grade_C. (25, 0)\n", "Perfect split!\n", "Split on feature grade_C. (26, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (3, 11)\n", "Split on feature grade_C. (3, 0)\n", "Perfect split!\n", "Split on feature grade_C. (11, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (30, 65)\n", "Split on feature grade_C. (30, 0)\n", "Perfect split!\n", "Split on feature grade_C. (65, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (8, 8)\n", "Split on feature grade_C. (8, 0)\n", "Perfect split!\n", "Split on feature grade_C. (8, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (3, 29)\n", "Split on feature grade_C. (3, 0)\n", "Perfect split!\n", "Split on feature grade_C. (29, 0)\n", "Perfect split!\n", "Split on feature emp_length_3 years. (526, 47)\n", "Split on feature emp_length_4 years. (484, 42)\n", "Split on feature emp_length_9 years. (460, 24)\n", "Split on feature emp_length_8 years. (428, 32)\n", "Split on feature emp_length_nan. (412, 16)\n", "Split on feature emp_length_1 year. (375, 37)\n", "Split on feature emp_length_10+ years. (118, 257)\n", "Split on feature emp_length_5 years. (80, 38)\n", "Split on feature emp_length_< 1 year. (33, 47)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_C. (38, 0)\n", "Perfect split!\n", "Split on feature grade_C. (257, 0)\n", "Perfect split!\n", "Split on feature grade_C. (37, 0)\n", "Perfect split!\n", "Split on feature grade_C. (16, 0)\n", "Perfect split!\n", "Split on feature grade_C. (32, 0)\n", "Perfect split!\n", "Split on feature grade_C. (24, 0)\n", "Perfect split!\n", "Split on feature grade_C. (42, 0)\n", "Perfect split!\n", "Split on feature grade_C. (47, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (46, 35)\n", "Split on feature home_ownership_OWN. (39, 7)\n", "Split on feature grade_C. (39, 0)\n", "Perfect split!\n", "Split on feature grade_C. (7, 0)\n", "Perfect split!\n", "Split on feature grade_C. (35, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (35, 22)\n", "Split on feature home_ownership_OWN. (30, 5)\n", "Split on feature grade_C. (30, 0)\n", "Perfect split!\n", "Split on feature grade_C. (5, 0)\n", "Perfect split!\n", "Split on feature grade_C. (22, 0)\n", "Perfect split!\n", "Split on feature emp_length_nan. (5037, 227)\n", "Split on feature home_ownership_RENT. (3142, 1895)\n", "Split on feature term_ 60 months. (3056, 86)\n", "Split on feature emp_length_2 years. (2781, 275)\n", "Split on feature home_ownership_OWN. (2430, 351)\n", "Split on feature emp_length_4 years. (2263, 167)\n", "Split on feature emp_length_3 years. (2070, 193)\n", "Split on feature emp_length_6 years. (1900, 170)\n", "Split on feature emp_length_< 1 year. (1724, 176)\n", "Split on feature home_ownership_MORTGAGE. (3, 1721)\n", "Split on feature emp_length_10+ years. (1, 2)\n", "Split on feature grade_C. (1, 0)\n", "Perfect split!\n", "Split on feature grade_C. (2, 0)\n", "Perfect split!\n", "Split on feature emp_length_9 years. (1605, 116)\n", "Split on feature emp_length_7 years. (1453, 152)\n", "Split on feature emp_length_5 years. (1230, 223)\n", "Split on feature emp_length_10+ years. (283, 947)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_C. (223, 0)\n", "Perfect split!\n", "Split on feature grade_C. (152, 0)\n", "Perfect split!\n", "Split on feature grade_C. (116, 0)\n", "Perfect split!\n", "Split on feature home_ownership_MORTGAGE. (4, 172)\n", "Split on feature grade_C. (4, 0)\n", "Perfect split!\n", "Split on feature grade_C. (172, 0)\n", "Perfect split!\n", "Split on feature grade_C. (170, 0)\n", "Perfect split!\n", "Split on feature home_ownership_MORTGAGE. (1, 192)\n", "Split on feature grade_C. (1, 0)\n", "Perfect split!\n", "Split on feature grade_C. (192, 0)\n", "Perfect split!\n", "Split on feature home_ownership_MORTGAGE. (1, 166)\n", "Split on feature grade_C. (1, 0)\n", "Perfect split!\n", "Split on feature grade_C. (166, 0)\n", "Perfect split!\n", "Split on feature emp_length_9 years. (338, 13)\n", "Split on feature emp_length_4 years. (317, 21)\n", "Split on feature emp_length_5 years. (284, 33)\n", "Split on feature emp_length_6 years. (269, 15)\n", "Split on feature emp_length_7 years. (245, 24)\n", "Split on feature emp_length_8 years. (225, 20)\n", "Split on feature emp_length_< 1 year. (182, 43)\n", "Split on feature emp_length_1 year. (162, 20)\n", "Split on feature emp_length_3 years. (129, 33)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_C. (20, 0)\n", "Perfect split!\n", "Split on feature grade_C. (43, 0)\n", "Perfect split!\n", "Split on feature grade_C. (20, 0)\n", "Perfect split!\n", "Split on feature grade_C. (24, 0)\n", "Perfect split!\n", "Split on feature grade_C. (15, 0)\n", "Perfect split!\n", "Split on feature grade_C. (33, 0)\n", "Perfect split!\n", "Split on feature grade_C. (21, 0)\n", "Perfect split!\n", "Split on feature grade_C. (13, 0)\n", "Perfect split!\n", "Split on feature home_ownership_OWN. (229, 46)\n", "Split on feature grade_C. (229, 0)\n", "Perfect split!\n", "Split on feature grade_C. (46, 0)\n", "Perfect split!\n", "Split on feature emp_length_< 1 year. (81, 5)\n", "Split on feature emp_length_8 years. (79, 2)\n", "Split on feature emp_length_1 year. (77, 2)\n", "Split on feature emp_length_9 years. (75, 2)\n", "Split on feature emp_length_4 years. (70, 5)\n", "Split on feature emp_length_6 years. (65, 5)\n", "Split on feature emp_length_5 years. (56, 9)\n", "Split on feature home_ownership_OWN. (50, 6)\n", "Split on feature emp_length_7 years. (44, 6)\n", "Split on feature emp_length_2 years. (39, 5)\n", "Split on feature emp_length_3 years. (32, 7)\n", "Termination 3 reached.\n", "Termination 3 reached.\n", "Split on feature grade_C. (5, 0)\n", "Perfect split!\n", "Split on feature grade_C. (6, 0)\n", "Perfect split!\n", "Split on feature emp_length_7 years. (5, 1)\n", "Split on feature emp_length_10+ years. (1, 4)\n", "Split on feature grade_C. (1, 0)\n", "Perfect split!\n", "Split on feature grade_C. (4, 0)\n", "Perfect split!\n", "Split on feature grade_C. (1, 0)\n", "Perfect split!\n", "Split on feature grade_C. (9, 0)\n", "Perfect split!\n", "Split on feature grade_C. (5, 0)\n", "Perfect split!\n", "Split on feature grade_C. (5, 0)\n", "Perfect split!\n", "Split on feature grade_C. (2, 0)\n", "Perfect split!\n", "Split on feature home_ownership_OWN. (1, 1)\n", "Split on feature grade_C. (1, 0)\n", "Perfect split!\n", "Split on feature grade_C. (1, 0)\n", "Perfect split!\n", "Split on feature grade_C. (2, 0)\n", "Perfect split!\n", "Split on feature home_ownership_OWN. (3, 2)\n", "Split on feature grade_C. (3, 0)\n", "Perfect split!\n", "Split on feature grade_C. (2, 0)\n", "Perfect split!\n", "Split on feature term_ 60 months. (1872, 23)\n", "Split on feature emp_length_10+ years. (1573, 299)\n", "Split on feature emp_length_< 1 year. (1263, 310)\n", "Split on feature emp_length_3 years. (1068, 195)\n", "Split on feature emp_length_7 years. (995, 73)\n", "Split on feature emp_length_6 years. (886, 109)\n", "Split on feature emp_length_1 year. (681, 205)\n", "Split on feature emp_length_9 years. (628, 53)\n", "Split on feature emp_length_4 years. (463, 165)\n", "Split on feature emp_length_8 years. (409, 54)\n", "Split on feature emp_length_2 years. (170, 239)\n", "Split on feature grade_C. (170, 0)\n", "Perfect split!\n", "Split on feature grade_C. (239, 0)\n", "Perfect split!\n", "Split on feature grade_C. (54, 0)\n", "Perfect split!\n", "Split on feature grade_C. (165, 0)\n", "Perfect split!\n", "Split on feature grade_C. (53, 0)\n", "Perfect split!\n", "Split on feature grade_C. (205, 0)\n", "Perfect split!\n", "Split on feature grade_C. (109, 0)\n", "Perfect split!\n", "Split on feature grade_C. (73, 0)\n", "Perfect split!\n", "Split on feature grade_C. (195, 0)\n", "Perfect split!\n", "Split on feature grade_C. (310, 0)\n", "Perfect split!\n", "Split on feature grade_C. (299, 0)\n", "Perfect split!\n", "Split on feature emp_length_5 years. (22, 1)\n", "Split on feature emp_length_4 years. (21, 1)\n", "Split on feature emp_length_8 years. (20, 1)\n", "Split on feature emp_length_10+ years. (17, 3)\n", "Split on feature emp_length_< 1 year. (12, 5)\n", "Split on feature emp_length_3 years. (8, 4)\n", "Split on feature emp_length_1 year. (6, 2)\n", "Split on feature grade_C. (6, 0)\n", "Perfect split!\n", "Split on feature grade_C. (2, 0)\n", "Perfect split!\n", "Split on feature grade_C. (4, 0)\n", "Perfect split!\n", "Split on feature grade_C. (5, 0)\n", "Perfect split!\n", "Split on feature grade_C. (3, 0)\n", "Perfect split!\n", "Split on feature grade_C. (1, 0)\n", "Perfect split!\n", "Split on feature grade_C. (1, 0)\n", "Perfect split!\n", "Split on feature grade_C. (1, 0)\n", "Perfect split!\n", "Split on feature term_ 60 months. (220, 7)\n", "Split on feature home_ownership_RENT. (148, 72)\n", "Split on feature home_ownership_OWN. (110, 38)\n", "Split on feature grade_C. (110, 0)\n", "Perfect split!\n", "Split on feature grade_C. (38, 0)\n", "Perfect split!\n", "Split on feature grade_C. (72, 0)\n", "Perfect split!\n", "Split on feature home_ownership_RENT. (5, 2)\n", "Split on feature grade_C. (5, 0)\n", "Perfect split!\n", "Split on feature grade_C. (2, 0)\n", "Perfect split!\n"]}, {"data": {"text/html": ["<style>#sk-container-id-1 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: #000;\n", "  --sklearn-color-text-muted: #666;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-1 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-1 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-1 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-1 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-1 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-1 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-1 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-1 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-1 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-1 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: flex;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "  align-items: start;\n", "  justify-content: space-between;\n", "  gap: 0.5em;\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label .caption {\n", "  font-size: 0.6rem;\n", "  font-weight: lighter;\n", "  color: var(--sklearn-color-text-muted);\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-1 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-1 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-1 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-1 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-1 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-1 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-1 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-1 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-1 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-1 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-1 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 0.5em;\n", "  text-align: center;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-1 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-1 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-1 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-1 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-1\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>DecisionTree(max_depth=15, min_error_reduction=1e-15)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator  sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-1\" type=\"checkbox\" checked><label for=\"sk-estimator-id-1\" class=\"sk-toggleable__label  sk-toggleable__label-arrow\"><div><div>DecisionTree</div></div><div><span class=\"sk-estimator-doc-link \">i<span>Not fitted</span></span></div></label><div class=\"sk-toggleable__content \"><pre>DecisionTree(max_depth=15, min_error_reduction=1e-15)</pre></div> </div></div></div></div>"], "text/plain": ["DecisionTree(max_depth=15, min_error_reduction=1e-15)"]}, "execution_count": 134, "metadata": {}, "output_type": "execute_result"}], "source": ["model_1.fit(X_train, y_train)\n", "model_2.fit(X_train, y_train)\n", "model_3.fit(X_train, y_train)"]}, {"cell_type": "code", "execution_count": 136, "id": "4e050108", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["model_1: training score =  0.6173326133909287\n", "model_2: training score =  0.6229481641468683\n", "model_3: training score =  0.6266198704103672\n"]}], "source": ["print(\"model_1: training score = \", model_1.score(X_train, y_train))\n", "print(\"model_2: training score = \", model_2.score(X_train, y_train))\n", "print(\"model_3: training score = \", model_3.score(X_train, y_train))"]}, {"cell_type": "code", "execution_count": 137, "id": "e6aabc43", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["model_1: testing score =  0.6173866090712743\n", "model_2: testing score =  0.6206263498920086\n", "model_3: testing score =  0.6187904967602592\n"]}], "source": ["print(\"model_1: testing score = \", model_1.score(X_test, y_test))\n", "print(\"model_2: testing score = \", model_2.score(X_test, y_test))\n", "print(\"model_3: testing score = \", model_3.score(X_test, y_test))"]}, {"cell_type": "code", "execution_count": 138, "id": "539d16c4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["model_1: complexity =  8\n", "model_2: complexity =  74\n", "model_3: complexity =  386\n"]}], "source": ["print(\"model_1: complexity = \", model_1.count_leaves())\n", "print(\"model_2: complexity = \", model_2.count_leaves())\n", "print(\"model_3: complexity = \", model_3.count_leaves())"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}