{"cells": [{"cell_type": "code", "execution_count": 4, "id": "4d98a412", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "\n", "from pathlib import Path\n", "from typing import Optional, <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 5, "id": "6a1f8bc5", "metadata": {}, "outputs": [], "source": ["data_dir = Path(\"../.inputs/digit-recognizer\")\n", "\n", "def load_training_data(\n", "    data_dir: Path,\n", "    num_samples: Optional[int] = None,\n", ") -> <PERSON><PERSON>[np.ndarray, np.ndarray]:\n", "    ##\n", "    train_data = pd.read_csv(data_dir / \"train.csv\")\n", "    num_samples = train_data.shape[0] if num_samples is None else num_samples\n", "\n", "    Xs = train_data.values[0:num_samples, 1:]\n", "    ys = train_data.values[0:num_samples, 0]\n", "\n", "    return Xs, ys"]}, {"cell_type": "code", "execution_count": 6, "id": "810b7290", "metadata": {}, "outputs": [], "source": ["def load_test_data(\n", "    data_dir: Path,\n", "    num_samples: Optional[int] = None,\n", ") -> np.ndarray:\n", "    ##\n", "    test_data = pd.read_csv(data_dir / \"test.csv\")\n", "    num_samples = test_data.shape[0] if num_samples is None else num_samples\n", "\n", "    Xs = test_data.values[0:num_samples, :]\n", "\n", "    return Xs"]}, {"cell_type": "code", "execution_count": 7, "id": "21ae853c", "metadata": {}, "outputs": [], "source": ["iis_Xs, iis_ys = load_training_data(data_dir)\n", "oos_Xs = load_test_data(data_dir)"]}, {"cell_type": "code", "execution_count": 8, "id": "f0028e7e", "metadata": {}, "outputs": [{"data": {"text/plain": ["((42000, 784), (42000,), (28000, 784))"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["iis_Xs.shape, iis_ys.shape, oos_Xs.shape"]}, {"cell_type": "code", "execution_count": 9, "id": "1b4fb1df", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[0, 0, 0, ..., 0, 0, 0],\n", "       [0, 0, 0, ..., 0, 0, 0],\n", "       [0, 0, 0, ..., 0, 0, 0],\n", "       ...,\n", "       [0, 0, 0, ..., 0, 0, 0],\n", "       [0, 0, 0, ..., 0, 0, 0],\n", "       [0, 0, 0, ..., 0, 0, 0]])"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["iis_Xs"]}, {"cell_type": "code", "execution_count": 10, "id": "281fd5d2", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([1, 0, 1, ..., 7, 6, 9])"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["iis_ys"]}, {"cell_type": "code", "execution_count": 11, "id": "f37c4a67", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["iis ys[34966] = 0\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["print(f\"iis ys[34966] = {iis_ys[34966]}\")\n", "\n", "plt.imshow(iis_Xs[34966].reshape((28, 28)))\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 12, "id": "4979e2f6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['0', '1', '2', '3', '4', '5', '6', '7', '8', '9']\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAgMAAAF1CAYAAAB1SOkWAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjEsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvc2/+5QAAAAlwSFlzAAAPYQAAD2EBqD+naQAA6VJJREFUeJzsvQecHGd9//+etn33dq9X3an3Llnu3QZsmqmmhECAX5JfEiAF8gNCQgqQ3kMIgRDypzdjGxv3bsuWZPV+0hVd79vrlP/rmVV1IbaR7vZ0z5vX4r3ZudPMzszzfL71URzHcZBIJBKJRDJnUWf6ACQSiUQikcwsUgxIJBKJRDLHkWJAIpFIJJI5jhQDEolEIpHMcaQYkEgkEolkjiPFgEQikUgkcxwpBiQSiUQimeNIMSCRSCQSyRxHigGJRCKRSOY4UgxIJBKJRDLHmXYxUCgU+MM//EOam5vx+/1s2bKFBx98kNlGOp3mT/7kT3j9619PdXU1iqLw3//938w2tm/fzm//9m+zcuVKgsEg8+bN413vehdHjx5lNnHgwAHe+c53smDBAgKBALW1tVx99dXcfffdzHa+8IUvuPfXqlWrmE089thj7nG/1OvZZ59lNrJz507e/OY3u8+8uM/ENfnnf/5nZgsf/OAHX/aaiNfAwACzhc7OTm6//XZaW1vda7Fs2TL+7M/+jGw2y2zj+eefd+eSSCRCOBzm5ptvZvfu3dN6DPpM3Iw/+tGP+MQnPsHixYvdCfSWW27h0Ucf5corr2S2MD4+7t54YvJcu3atO/DNRv7qr/6Kp59+2p1I16xZw/DwMP/6r//Khg0b3AF7tkxAvb29pFIpfvVXf9UVmmJA+PGPf+wO3P/xH//B//k//4fZSH9/P1/84hddoTZb+djHPsbmzZvP2bZo0SJmGw888ABvetObWL9+PZ/73OcIhUIcP37cvUazhV//9V/nxhtvPGebWJ7mN37jN+jo6KClpYXZQF9fH5dccglVVVWuMSPE2datW10DTUysd955J7NJYF555ZW0tbW5x2/bNl/+8pe55ppr2LZtG0uXLp2eA3Gmkeeee04siuT8zd/8zeltuVzOWbhwoXPZZZc5s4l8Pu8MDQ2577dv3+6e1ze+8Q1ntvH00087hULhnG1Hjx51vF6v8773vc+ZzZim6axdu9ZZunSpM1t597vf7Vx//fXONddc46xcudKZTTz66KPuc/HDH/7Qme0kEgmnoaHBue222xzLspyLiSeffNK9Tl/4whec2YI4VnHM+/fvP2f7Bz7wAXf75OSkM1u45ZZbnFgs5oyPj5/eNjg46IRCIedtb3vbtB3HtIYJhEdA07RzrDSfz8eHP/xhV9UJtTdb8Hq9NDY2Mtu5/PLL8Xg852wTHhsRNjh06BCzGXGvCbUdj8eZjTzxxBPuM/OP//iPzHaE18Y0TWYr3/nOdxgZGXFDNqqqkslkXAvuYkCcmwgRvPe972W2kEwm3f82NDScs72pqcm9Pi8c0yqZJ5980vXW1NTUnHMewjPws5/9zA1JTwfTKgZ27drFkiVL3LjI2Qh3j2C6YySSl0a4DcXAJ+Lusw0xSIsQjnDf/sM//AM///nPueGGG5htWJbF7/zO7/CRj3yE1atXM5v50Ic+5D7zQvhfd9117Nixg9nGQw895J6DiKkLt60IEYiff/M3f5N8Ps9spVQq8YMf/MA1CkSYYLZw7bXXuv8VhqSYN4Qh+f3vf59///d/d8NSsymsVigU3Py5FyLyIIrFIvv377/4cgaGhoZcxfNCTm0bHByczsORvAzf/va33UFP5ETMNn7/93/fzREQCAvhbW97m5sDMdv4yle+4uZBiElotiKss7e//e1uTpAQlgcPHuRv//Zvueqqq3jmmWfc2PtsSlYTno23vOUt7gT0pS99yc0T+pd/+RfX8/Td736X2cj999/PxMQE73vf+5hNiGS7P//zP3fzae66667T2z/72c/yF3/xF8wmli5d6uZnCQNAeDMFQgQ899xz7vvpSuqcVjGQy+Vc9/oLERbDqc8lM8vhw4f5rd/6LS677DI3GW+2IRJT3/GOd7jCUlg84gETD9ZsQgzOf/zHf+wmqdXV1TFbEdameJ1CJHOKayMSVT/96U9z3333MVsQrlqRlCoS7U5VDwihKe4tIT6FcBbhtdkYIjAMw60gmm0IT4aoGBKCU7jY77nnHlcciPCtSCqcLfzf//t/XQ+TEJmf+tSn3PCTEDTCeJ7WeXHashMcx02AEslQL+TAgQNu0sdXvvIVZzYymxMIz0YkRC5YsMBpa2tzBgYGnIuBm266ydm8ebNj27YzW/iN3/gNZ9GiReckds7GBMKX4/bbb3c8Ho+b4DlbEN+9eMYff/zxc7aLn8X2b37zm85sI5VKOYFAwHnjG9/ozDa++93vOn6/3+nr6ztn+wc/+EH3nM5OxpsNfOYzn3EMw3DvJfHatGmT89nPftZ9f8cdd0zLMUxrzoAIB5xSO2dzapsoCZPMDIlEgje84Q2uy1NYbBfLtRCWqOilMFv6Jgh39Fe/+lU37im8Gz09Pe5LxKVFfFe8n5ycZDYjkjqFRS3yO2YLp56HFyas1dfXu/+dmppitvHTn/7U9XbMthCBQJTeiTCT6DFwNsL7JM5J5KfNJr7whS+4eVoimXDv3r3umHUqQVXk2U0H0yoG1q1b5w7KpzJBT3EqNiI+l0w/YqIR9dPi2ojs1RUrVnCxcMrFJsTObEDEB8UgIMTA/PnzT7/EMyKuj3g/G3M5zqarq8sNDYokvNnCxo0bXzJ+eyrPaTaGc0RukLgGYgKdbYiJU4QAX4gQzILZWLkSi8XcfgOnEoZFvpAQO6KZ0kUnBoSVJi6gsHzOzqT8xje+4XYiFBaDZHoR1+Pd7363W9r5wx/+0M0VmI2Mjo6+5MDwP//zP26m7mwROKLJ0x133PGilyj1FA2uxHsRW5wNjI2NvWjbnj173IQv0WFNJHjOFk7F1L/+9a+fs/1rX/sauq6fzm6fLYhrIyab2267zc1an20Ia1lY/y/0+IlETnFfibyU2cz3v/991zsgcqCm6zmZ1gRCMeGLTncieUgM3qIL2Te/+U3X9fnCh2w2ILLUhVv9lHUgWt+e6kYmysJEd6zZkH0vBmfhGRDu529961vnfP7+97+f2dJZTXicREKR6KImOikKy0ckRP7d3/3drLFCRdb9W9/61hdtP9Vr4KU+q1SEyBRCTCQRCne6qCYQhoCYfP7yL/+S2YRwSf/ar/0a//Vf/+VanaIGXFQTCAEtxrPZFlYTk404j9kYIhB88pOfdMuGRWWKSBYUCYTCqym2iXLc2XQ9nnjiCdfbJwSyOA9RWSAMZFEx8fGPf3z6DsSZZkTHwT/4gz9wGhsb3S53Irnrvvvuc2Yj7e3tpxM+Xvjq7u52ZgMiMe3lzmEGbo9fKqHoxhtvdLvE6brudvQSP995553OxcBsTCD8p3/6J+eSSy5xqqur3WvS1NTkvP/973c6Ozud2UixWHQ+//nPu8+9SPYSSZ7/8A//4MxGLr30Uqe+vn5WJXG+VEfbN7zhDe5cIq7HkiVL3M6EpVLJmU0cO3bMufnmm53a2lp3Tly2bJnzpS996UWdYS80ivi/6ZMeEolEIpFIKo3ZE7STSCQSiURyQZBiQCKRSCSSOY4UAxKJRCKRzHGkGJBIJBKJZI4jxYBEIpFIJHMcKQYkEolEIpnjSDEgkUgkEskc5xV3ILxJfSezhQftH17053ExncvFch4X07lcLOdxMZ3LxXIeF9O53HSRnIdAegYkEonk1aAoaIaDx2/hGCqOqsz0EUkkvzRSDEgkEskrRVXRmhq46WMp/uj+/dgfXEpu/bnL6Eoks5FpXahIUjnkVobwNiu8vq4HVXEwHYVjpSBTx4Ikj/lx0hmxMgEViaaCrkM4SLFKxwwolKptFleN0BqYIqbadA7Wc6y/Dk9fAkomnFwbXCL5ZVC9Ds2vS9G6MU97m8nllx7nRM7LcGcYJ50Gu0KfGYlkNogBx/VPKO7SOEqlTkAXGenrqvHdqPJbm57CUCyyjs63kq0c/HaM9HdrsLI5sb4xlYR7Z6gKmtdACflR5jeRXegn26KTXl1g0cJ+XtfUx3JPkW8/Us/BB9vxJI6hJHM4hcJMH75kluMooIdslv3GEG21Weo8Grff8CSPJ5dw97MbsHI5sM2ZPkyJZHaKAdujMv7uFaiKjpG0CT94GDVTnOnDuuj5YMdWNiyPoyomwmbOWyqPjC4l7USwanwofQpUlhag2Bpi/O2LWLnqBMsbx/lQ/S4cj4KtK9g+hyojR0hX8Cge3nPZYa5dOsgnPDeR3ZUgsLO8tLRE8loprG5BXxvi9uq7qfEWXHGadjwUTAWlUKxcT5pEUulioNjgx2rzc/klo2iag52yOXY8hDlYQJvIMFsoNPsoNfgIDDgo6QJKNk+lU+PJ0ORLAh73Z12xWeOfJLUgS/Yyg0N+P2ZShZSGdzCDUjRn1FNg1UUIL9BZvqmfJQvHmF89RXN4ioFSiKmSn7FUBMeJ4dFMLqnpJRTI0m5Y1K1NMTlewt6jVZynY67gaAqF+VHsoIPut7giNoBHLYdtROpd0YG07dB1oJlcXEVJVeCzr6oE2k2q1+aoNkr4VYuCrbJrqpneRAQ7k5UhghlE0XVKMS+lqBfbA4rXRvNatAUnielFarWSu994JsCegUY8QxnU3MyOaeegKO49ZleHKUUV7CqbTTW9rtgcKUZIjIdg0sQ7mOaiEwOuql5Xg3lrPX9yy3fweZOMFS1+f+BDTOwwCTxzjNlC+pJq4q9vov2HFlrXGHQPMNsIaSU+07qd/ibovMbD3qNvJNkdRTnio+7O46gTmfKAN0MUV7ax9NIpvnDtjwmqXlQUsnaJR9J1bE208sihZdiOQo0ny7evOk6TBzy6wZprj3J8OEDfgz5s4cadBdabOEI3P11xyr7pl9nnFJWdy+7g+A3ity7GnG8RmJfmYxu2UWPk3E9VRWXSsjlWtPnyH68ht8uAg11UFIqCYhg0r56g48Y8ql4WMkVb55vdl1PsS1MzOchFh7j/zuZl7sUZR1FQA34KqxpIbqyjUA1aTQFfY4a18wfYEJjkGv+ku+tT3R08/pOl1P6sG70/iZ2duTHtHFQV/F7sNfNJrtEorS3y3iseo7NUx/3xRk48uRDt2SSeOzov2PM+7WJACfhxogEG39bAyvXDbFz1ND6jiE/RqNF07ICK5WN2oKmoLU3cvmqASy/fxR9a12I9BFXdzFrqVAj5ivzr4kex5mnYl2g8cX2Ynu3VHPlBB07fIAiX6DTj3dlF36CHz468jXyj180BCDzWyaTpI2XlaM0ddidI3avyl8+9jduuPcobr+jkV6p6+dmyJey/YSHBR45UfAjKrDGY+HgbmxpP0K5P8eQ3NuJ0TaIOjLufKz4vSijI2LUNlKI6ls+h4ce9GGM5HLOC4tUeAxa0sf5NnSy7/DgrWrvQvA6a1yas57Fx3EHNciyCKiwxFIodHtKjOuGDVA6Kghn1MvL+Rbxpy17eGuvCo9iUHItCoUDTt3Ik9petzlnPSevUWTKPxZuGueG2ncw3NMbNAPdnGuj5xwiZQyp2JlNRHgGjRmPJl5Ksbe1nVXUKRVfAcMAwqfVmCKoiFFoWNmuaB/nme3/MPxa30LWrGf/TM2x06hpaQz326xxClxb51JKfoQUtnKDDQq9Fq3eC9b7nOXjjfnbMb+GnHWto+mEfxkgOp1SanWLA0VTsWJDWjgKx9jh1l3hZ3T7OupoRDNV2LT0D1XUpzpa6XcWA0HqH9vlp1kRHaVgwSbLBQvEY5QtV+UboaYStM2Xr6Jh4FItlwUmUYHn7RMxEMb0Md1mkJhysGcjFU5M58hZ07qgjW6/jFFWCO0MnVbKNj/IA5Xh0emvmEV/hd6skmkUeQZVNri1AQAwSFYwV9aO1eli+Mc6qxiQdepr4oSmGi3nGTzqbzGoDe3GAFZszqDGwvA4Tz/owCwrKpAj7zDxmtR+jxsOyS+Os2Jxk2do0qzxZxNevnLxiYyU/fYUwpaKO4yiuhz1vGi/wecw8wiPgq9JZtzHBkqYULUbZkuwfi9DZF0Y9mkcfK+cPVBzC2hRft6KW3eEv9IqdnPzdl8+DHQXqYfnqNCs2pli+IUOHrjFu2QxmAoxXx8h4dE4+ajOLmCMCfqwmHaMDlq8bYVV0ghWeBHuTDZg5HXXCYAA/tuFg+WF1bJSwt8DqpmF8NQpWeOatTtULwY1QszFH87oUy5pGSFhexooBjvQ0o4h8FEchF7UJNZis3Rxn/LCXog+MY7NVDPgMClcs47p3PcYtV++mSvWeHhiEEKjIh+l/QQvadHxykGh9CkO1uKXjAIdaajkUq8can6iceNQroOQobM0HqdHSNGo5WnUvGorbiOK6wDh1G00SixT27NbJTM1Mewolk4N9xwj+on1MG1/nGPr4mdhaKQLpeRDTQaNyya9upXaTxl+v+AbVhk5A0bnx4yf4XqmFHz5XrmVPr/GR/lCU/950J/P8KWxb4aPb38ZxXcP7bGWIgeyGVmou0fm9j/4XMUPFp4hh5lwh9kSyhX8ZXM/ocBTTVF0N0HrMITCaoJJQqsI0tcK/X3cPhnGmPPXB55bzzbvW4ju2F6VQgZ6Bk6ENYTkrhu6G+F5oSSqqVvY0BfzYbfWYVzjYt+b5vaX30eJJn7xmNo16hvdWdXMg1Mao3wdTzDzi3BZ1UHizhX5FlnfU9hLUTFKmh48duJ70sTCBw0JcQqHWIbPA5sfX/pCV0bKHrVLQqy3aPjPCW2t6uSwwio3NE4kWfjKyhNH/mYcSV1FNm6Fbbd6wqJOvX30nt4feSu9TIRr/OjmLxICuo9XEcG6xia0t8Pald7K6JU5I8ZwWAmejaTa/+voH2b6ikZ9vXETwoIF32CTQm8SaisN5dov8spiOyuF4HesjKezAGMcy9QynPNiJZMXXtf/o8CU83uTw3vXb0RSHdMnDf+29guAAVE+UaL51lE2xEa4Ll03SiJZjiW+IzshicsHy4DJrsEGZBbrMWZaDzSox3YdPgRIq+4shRiyv+7miadSG87Q3DOPRTp6QAsn5CukRFe+zVASrNnax8IYCUd3Ao0DK8vCXfZux9vkw9uvoo0mGkxqBxASNmXEc8ag44B0HNVtBoQ4gMz9McqVetqJFfw5b4+50E4e6bby7ess9LCoNQ0eLVFH7gSz1y5JcFT7O97vWs3ei4/QuiuoQjBS4pa6X62oO88PiMjrqk1zaPkSNXs7nOMWJUoifJ9sYTKo4ouR4hrBDXgrrO0i3g785x/s3P0VLcwl/FD7XdSX6Ph1jj0Jg1xDe1BBaRkUNBmm7MsHGTSeoDyYZNP08la0nczhL8OgMXTtDR62tZfIqL+E1BW6veYJ53hwJ08MfHb6Kyccg/3gWb2+nGATcmbIm1EGkpKCucPjs4mfYk2jgB2tXoh4fQEnnKlwMaBpKyEBd5qVuS4a2DRk2NpygWsTZFZ1h00c+7cHMGixsnKKgKMQtHX9dlmqR6R4qUBMEZcAmH1JJ9fixEjr65MzdjC9EXCRdsVBFRMqBuOknJ6IDxcpPVOvvCjMa8XBQC6EpNpmSl55dETy9OqExm8HVBvVqHk6KgYBi0makodFHacSDVqFiQPSsKFZ7sAJnbm09a+MbKaKYlXlNHF3FqvLT2Jalo62AT9XceypvKRwYrWc0c9IXYhjU+QssqkrhUS33FrMcG8sAe8aLhM9QVxuntSWDR1WZNL30ZcM8v7cGa7sPY5eGPqRCLo+WyxEoiZI8Kg8x+Rs60XkFapfkTyfTlWyVnSMNDI9oaGNJnAqsIFBU1bX4W1ZPsXBLkrWhCXY1ZkhOlM4RA+GqPCvqkqyvGWff1ChLvAk2B0fO+Vujpp+uVBW7eptJJS0ozlDOjcfAEzOo21gktwjCLXlWrxnCb3vI5ILs3leNsk3Hv8PB6OzFsGzQFLQVCo3RKS6t7yOom/Rmozw/3ERu2ESbLLlh0GnFMCDixVoSoHajRfPaAot9cRKlIH3pGDt21+DZkSayI4t28rsWYXPvsI0RV9zbcl1khEK9j2JHFE//CNp5KjC4YEOIGgqhLvWgf77ErzQd5MrwoJsVIEjbGt+IL+bwUx2M7arnu5/8Lj2Gh0cy9Tw4vIxVwUH+es2Pmb/BoLcY5X/ii9j2yArSu1Vqv3sQHBH/YsbxqSVua9rFynA5iGY5KpbwCFS4V0AQu68P7oc7aTq9rYHD7n8dQ6OveT2T18ThZKfVWs0i5rNJ3VLPeEyn4ZuV5W47he3VGHlDM+klw6e3hY6kaP3+AFa6Mt0DQggk37Ka39/8EDcv6EQXSWuORTzv4Wt3vx710AghZQA1HGJLdT+/Xn8ERXHcJLaMXSLYaxMcrIAH4iRhLU+1lnGDf3dMLeTn3e0Yn51AT5bAtKZ/AH4tGDp6UxO3vX4bm27oR1PLQ2WxYPDYg+tRDw8TLlZ274q3RnpZHx1FwcNnlj6P4zx/7g5uDkf59ZHq8rN/NuKO+nZ8EYcONXHsBwvxdh1CmyHPgNpYT/O6PJ/42HdoNgzCqoaDzReOreMHh5fQ+rkulLS4v8rWvhsiCXnRPm1Qs0Rlrae8fXy0igd/tomargN44tMfVlMb6jCXBJj4mMafLdzOzdU9KGh8eXg1Pz6ykJo/PY6SPZXueC7iOqknVxAoVamMbjBo2KOgTVSqGFBFrMpD4F0OTZtSvL2pm6X+qXOCAmZep/MnC0k8b+Hr7oKiRUbxMpiJon1limPVIf75+jfw2Q3P0RzI8O5oFzdeMcGJYJCf3F2LLW7ISsqcnoW418O9485cmdPvTJvgE91sUwL8v9gNfHLNM9T4cu7nnsYcRr3HjUU6L5WUNMMEvAU+fO1DbGqfpCBE5/hSDk8Gyu2VK1Ck2e0NVC9VeNttD7CobdTN0xA8EO/g6YF6fPf2oPTl3MTa5No68vMSrhAQHMzW8aPxdhI7JvEcqkyhU7I0ciUdze1TUXnf/8siar6DPuq9BvMM9VSUAKVgEXugh2JP5XgoX4hj2TjZLP/97Dp+mFCwvbBlyRHaassCfrQYYdIMkij5mYqHiMeDKCWF5Q0DbJl3jCVGEY/iuImdh4abOH4siGfbcdTUDIYIxicZG1D5+tAVvL++mzWBCSbsIsauErUPF1GES1aMRyInMhgkszJE9vIg/2/hTlZERZKDwt8f2MKhnWGi93SiTczMuUxs8VGz0ebzC55ldWiMggPHSx5yO3Uij6soJ8MCZyMed89ggolRk8dyNWzwxi+ILayf99BAQENrMmi4tMCijSmucD0CwmpWSNgGpYTG5HCQ8edClA5PEJqYZCAbYCgfZnIghPp0knh1lJ5YO10Lj7DSO8Za3yS0T3I4E+XOjmbsERs7rc58mZirrMVlqawJ8ZdFsR28PZMMNtTTuXAeH1m6nYg35+YWtFTHyVcHKQX8IEIFM50kqSpupUqpWqfKk6O+PsPVizqpD6kkLB/P9DYxOWrhFCprrQW3YsbrIbzIoHlNlitXdVLvTjrloeBosoanhlrxHj6BknNwvCr6UhW18cxQMZwP8ejYAnx9feijlVkyKbxlpq2i2tasajXu6GK9Cw9+r0aVyLYHJks+BjN+vEensJMVfC62jVMosutgK/lEEDMAmjlGqrk8AZ4oRBkuVjFRDDI6WsXYRJVbnmf78yy3u1zPjVirJGurTAwESfYYNPSfJ/PztZLNkZn0sr13AbcEx3ACExQcG3XMxt/jxstOms4q6jwvgTUagatsttSMUqUX6U3FeOJQK4P7FOoPz1wfi2ybTt2iIjdVl9eESZd0jgzFSB/W8e0tuk7vF+E4aFMZUnGLQ4UqVniSZ3cjqUwxoNXWoK/XCfy/PB9sPuCqt1OHm7QNvhZfQvd/1jL6sxDOUBdaqUTWa/DRIzfiPWoQuk/BnsoQmEgS/I+jfHHBejavGecv2p9x/4Y6HwL/DGMPzKe4RyV4794X9cWYNjQNVVfdzn1ikrzocBwiR/PUZKc4dr3oD6Gy0LD50oKn2DHVwl8svR4Od8MMd4tTAwFKzUH6fr+Z65c+w60tR1jpV+k0feyNh0j/SxTzaAbHjFNRVAWx1i7mrb99P+vW9LPKo522PgWZ3jBTO+tosPpRsNANi0tu28WStnLzFEE+6WPkSC0thSE0KlMMzFZKYY2hG8PkGsuhTcHXh1bwo6MdVJV6USrQy3Qa23ab6UTu6yRy8p7ap8B+pcV9Xx6tijgU8TtTtAdV6r8dZHP7EJf6cq4jut/0sCMXJvLTIo17rYowd5QJDeObVfDrBkodVKsadmuQxMoo0f4QSslCCYDvLxReN/8Y7689gq7a3HNiMZ/ZdgNNX+mh4USmIs7lFMnJIN/8wi2Y+4dQTvS8rMEiqtPiUyrHcvUUgkNcCM6rGCi9TqVxY453NBxhnjd9epLcla+mczzK8e81En/OpigSb0RlgHigiuD75jhaXMXuFZLUKrf1zBbQ7rYYHfbxo3ct4JrgIPWeLB9sOMSzl6Y4EfIz9PMZMMpFAmR1NdoWC//6PKtCOeo1kYiiMpUPkCkV0SqiEPeXx8nksIfHyZR08o64VYqu+HHqbJKvE2VgDnpqmrNwqyKYVX43QTAfU+lYNUHTgnF+ZU0/K2OjzPOpGIpCT7aWJ6fayHr8mA0GBD2oeQsllYXxma2NUvx+6uYX2fwrz7C2I06rR3HLps+mtjnOvLWjxK+ch2e4hKeYJ+Lrxq+dmfT1LAT7FdQK0QF20ENudQsNDRMsMFKuIeB0+bC3h8Byf2I2IEJggYjNhrXHiMVS7vhcwkQ9qOJ90hB1uMwWD98pRIunl4tDK5bthp7E61TR8GhXjKcfWk76cA7GKyQkmyuiHTiBkhD5KGAoGjetOU5jNMN/17ajayaxYI73dBxjUXiCAiX+/dhmDu2KEb1zCG04U1ELlh0thunMROBEEiVZeEkhoPh9aFUBNtzeRdPaLO3hInvy1XT1V1H72AhGvFhZYsB1eXp0vFtKNG5McVOkz90utHPO1jk4Vc2unjpG7olgDU+W47cnUUzwP/zi2mKnZGJsLZKwdR6+bh5rPJMs8cfdv11caqI5VQz5ozhigJ/OWKTI1I2EUNZa6NcptHgLBFWLrOkhHfdRyDoEuEgoFHHieYqWQsk5U6FvVUFmk43vjuk7FNFMSA97CM734jQGsKIetGad1iuHWbFwgvdXnTgpPsvDWaoUYDhfTajegpDYFkJNlzDHTMRwUCqInAelXHZYNM8ZOC844QBVbQmuvv6A2+Et8hJNthrqEixRRzl0yWK8PQUikzY1HnGvldwxI2UbFNIq/v4SaoVU3Np+g/zKRqLVKo1anqzto9TvgQPeilv06hfi9RCscli/4ASxUFY0VHaTNbVuy81Wp0Lmxl8a4YoS/QhE1r3quG7rUySGQxx8eD7KQCeI/h4VgFIyUfrHyIw5TCX8qKESq9pHaG+Z4ofh+Xi8CvVBk+tq+8UVYyDv487DC0jvdKh/ZghHZOfPZKhKVfDoJj69/MD2pCIcGIuijiWxc8JT8xJ4PGixMKvfnmJ+6xTtepqvj23k+FCEyO4JrLRZWWLArq+itGkhH1p2F6sazpSmTFhevp9s58A3Ohh+PIR5vO9VJXFZU1NMHYnSe+cK0u86Cm1lV+9y7xBqbZ7Hrr8KdVcv+nTGs4TwmEoQ39lAlign2kyingJm3sb3gwDm3gp2H75GNGx012o42SRKDCDizTQ29CttXET9pgxv/+ijbPSVqNEcd97XdBtVfXGo5h01x3mzyNRdWu6pLgb0YTPL4Xw1jyYX8twDy8gOBfBNOPifPIw2Pn0ujtS6BljrZ41HP22JvZBbI/28LjyA/es7yVlFCnaJKr+GR1Uw0fjb0dV0H/QRue8QiDUXKqWsswoMj07JCfCdZAfd3Qqhg2NYFViC93JYCxqpXm7yO9Xb3O6o4sjzjoXZPwwHhIC8CJ5x0c8/HHYbDmm1Hur8/W6b6NnAf/73Jr693c+SP+zklmgfG/3jfG/N3WXfh+KQdbLcNbqcr3VtIfzFXmoGizgz7c3RRe+HMJct7mPJogSaorD3rmU8+Vgrytjhl0+uFQn5uspmb5Ymj6hb9/Lc/mX0HjCojx89r8628yMGqh2Kl5RojFo06sIhVZ4lsnkfzx1eRKbLwhxMvPpsYsdBnczhf2qAn9ctpH9FFW/YuN/tVbAsluODb9nNs5MOXdNZ4eM4bjWDkzFRs16iikbO8TNU9MPRUZThCjHTziM2ivs6dd+JifXkm+k7Bq+GEXBoCyepMlS8ikPcNtmTaWEgF2NqNHLO8cSiaWqjSVb4B/Eopmh0TY3XYbk3j9c/zMpLHYopHTVrc3dtNWNd9UR35MvNlC5wpUppgUWpwxJl0C+LodgY4nOvhUdMRCKj+GQpmPj+LwsOoHbUc/iGBUQeH0JPVECs4FSdmlt2a7PRN4l5iZeSR+HYsy1uG9tTUQ7VdFBLNkrfKIoIDVYSwvsnrDjFdq1lkZvm3lyzpGz4lfa2yK6oo2VxgvalA1xVPch8T8pN9P7R+BIOjtSiTiRxKu3aiGDlcBrLKtL3zSDHbggQXedlgVFwhbXopPpMup3je0PoP5tCGzdRZ1oIAFbIILeqjjX1g6wLjrrPsFlSMYsaulNep+OlKFQb5JcF3YdfjGFiMbbQviKBw2IRs/N7jOdFDKgxE++mFLURh+qTLs+caTCRCrF3XzvVfccJTr62BC49nkff1s8DVcvoSVVz/YY9RFSVmqoii2/ZycRDbXRRw7ThODjFAqplYWATUzUSjkF/MYTTO4o6UYENb1+xGf/iG0zcpjnTQ94qnRYBwtMmyo6m8xETbTo12yKcNylZfuIo9BVMHh5rYMfkPHoPNbnHdIqOtlEWzxtEj424YRzRF39xIEi9plDvn+DqdSOuxLEx2dr4Bkb3xwgOxskNliil7QsWehLfmdlYpNRQIm+fe69Yluq2Fz7VjVO4bg3FQkfDUM/sK67DNaE+sou83HFrDOvQuLscq1qsnInKp1hs8U+Q2WKQXqLT5VmIOqVipMp3jThWLWtipiZxsja2pbpNodxwzSyqOpi14QGfjr02TP2VI6y9pI+rgqP4FJuirfKjoSWMj3gIJ9LuQoWiWmdaQ7H/G5NJrLjK6FgVXU0BwisCdOjFk8JNZVt2Hv0HVCI/GaNSsP0GmcVRlsQyrPaKJOBXdo8Xqw2yywNYnvJzL34rejBHpFOMXRUoBi6NDPHxldsJawXE+lAFx+Szz97Izt31tHz1IErql3c/BR/vxJtJMfp/HBqFy1qxy2J9upOShLuntQnfFTbBG8ZQDQXVFB5rYTtXHorP5/YmVzzllrYvj+OWIwmh4xTPeDcsW+U/nr2Rmwt9XLLxcXebmLDyIuY+jUua+p85yuAhP5/b9R7SC/zuYxHZPkp2LI2SGaLNFGr7DIpq061ZfEVdiirO3+cl+bpFqEst/GsTvKdpG8v8CVYZCn+/8klySxziN2f5z6/ewN6tbWi7z68L7vRxiQ59/5Uiv8/in/9k0TkabeeuxQwM1Lp5NCvX9LB8YT/viOwnqmoExKpYZ/0Nv+LhluYBLq/9Ph9WX0f/VpWm/6qwpX+BKwOjXOIb5zc+dMQN15wqL0xZFuOmzTeGNtN7opXhPQ3UPzqGNpLCnjhTMSE5/yhNdYQW+vnER37AglieFp+FV4ynAkehuKeK/FQYbVMNjihrm0jj29+Pnc9XjmdEU4X7j+OOQj4V4qbAblSEt02hyRsn7Q8zFQyWlyiuAHGpj6So/s4utl2hkWmp4cbA6Cv6PTNsk2s1cfO3LzDn5Z/wqDbVet4doAuOQ8p2yO/KYD6bRE3kf7ladOGyM3SK7RFK7Zrbs11IgMmSl3tHlnAsc2aQnB5EApFNtT9LY1RkTNvEzQAnCjWui63SyKyMUtdsctOCE79wv6KjsCMVY3w8RHzCi2IreIYyeAdSpBWDvLvYTBklr2L0GSj56TtfpWhhxYskjmTJTQmx4kBvCVsk0Agr8yV0srjrMqLrpbBsDIXsjhRKv02xs8ie1iaGq+o5WG+waUUn1ZE0TT4bdaVDIQ3+3RcuJUKZyJI/6HDsW55ztk+dyFOYSiDG5ZEuE7XRzwPtK9mwbJI1S6ZebNxpFvW+HOsWDhAY8DKdhR0viegAmxOPu03RsfAomutq1zUL019gzAwzaYZY7B0lolj4HYXr9SHGg1mmQpPs84RIHQT7/pk9DaVkuwnMWaeEzxGZMsrLJk6XFtbR1DLJoo5BIqpoTV6eeAbNIGPFEJ3Jetelq02aMF0d706uRijyAUQFjki+LUY0CDtEF8Tx1RaobiiyIJalzmviO6umVeTevHHJEdJ1PjzLyuciDvvocofB4WpK4xA+mHYNhxkTBqqKHlaouSXJisUTLPRPMmRp2I5OwdFZ7Y2TWexj/+tCeO/Po2RmPtSh2I7bGGn/aCPF0SpuaB8jsChNeCpJ4XkvTv5k06STiCZj2ZV1tK4scM383QSNC18Fcd71hoi2xm0IbB0h+vh5OAFdRY0EKF3ShLXe72ZeC6E3kPfzlwcvoTreT4RptCTEP57PU6fFWRCcQlFsRophDqebMR0xHM+8Cj2b+JZa2i9J8MEbz7QbPTtCJUoFBWnbYGx8NaljtaS7m91yNeW5YQLjSYxACc17Jo6uZRV8x0Wp3jSvXigGoL4h/OVilVfuJhMd2awCgR19Z6zw1nZKDWFSy2N8snaI9aEkCwwv9mqbvFPC/22l3MjkAuBksmQPQe+hF4aUxolR7hIn6m2OGV46169Ff9++F4mBM+1J4cq2o1Q1h3iEOmYSxXLwJG0KeZuUBWHRrMdRsByHoZLN7mwVh3ONvL16jHpdo1rTeUtVD0oUnAXw2YWXcexxP6UHlemt7ngBat7EyZWYsExqFeOcyfLsZEnHp2FtaqH96hS3vK6fdr1Q7twHbM03sjvhZXdvA8Hv2uiHcpSSqek5L5F05vei1VehhP04IS/5di/Ms6l5U5aYN0WDJ0Orz8H7gnPTVJuPbtl++mfxjB0uRPnm1BKO7asnc8CgqrcfWzxTM7VOgaGh16nM+8AoV8X6WeOd4OlMkLjto4iHt0dGGV0bIx8OY2ydRKsAMXCKXQPzmDpRxG7fSWRdnLqwl/E7fBTjDmZO5KSUH2pxb+WvbWXFpfv4tSVPUqUJo/fCGl8VtLzJy7BCw/pUgD9Z+AirqiYIqAYPZZrYe7yK1s8cRp2a7jofxQ0V+HSLoCbEjsNIbw37ty+gprgftVLqjoRlIFoGawonrBD/MLHa3Zy3DHrS1e77mJ7hI01P0ahDRDX5eM1BCpGjFFar7gI4Q6/TOPEJnVVNh6nznQkd2DET86oUzuMmDDIrsYZHUEo5glVBfjaymp5YPb9Xf5CVsWFyjUVO1FRjiZjpTNYlCxdtMo8qrIZZgBbPEb5zN1/1rOU/+y/FOz+DftiH1mugTVqYXeOY/XE6L3snxUYPVjP80S0/oj2coVbT+XD9c+xY1spXX3cT3uc60SZnpl+H0zdIj8/Dbx56G59q381VVeXFus4m+bparDdW8dUtd9ESSRMRHSLxnR6ur/enucLbxa/GBojPz3OsM8aX/+lm1MO9KJMX1odTaAky9q4lvHXLDhY3DFNjZGjzQdTr4AmW3BCr6OwvvDa/CCFb9hR8DFsKHb5xnlcWkilp2CnRxGvmxrnJ2+uovkblN+oeYMqxeWYizLe+dAOTsQBOu8It7/wJl0S7+fSiQb5jdJDgXA/cTNJ0X4aakQL7L9G5IdzNm1afwPoR/Mu+Tfzs6FoanrPQ1mcJXJLkb1feQXMwQ1QXxeonl/a8gILgvIiB4USInx9YypqOLvz+LCFVYd5lObIRL335ajiqwKiD8ypLoMzGKJ55GpG2PE21Kep9YnBQiKp5qj0+rFgEsmlUEcuaLoRnIuijxlek3Ui6lplZ0ijkPNMaQ//fsMIeivNjWFGNbMah69EaxFqxxZLC8FTZwZH2eti6fAlRr0JQL7tDXbtGdbBa8+jhLC3VKdo9OQKuE+CkJ0ADJ2Cd/nFWIdZ39/vIzPMQboGV64/RWpNgvifrPmai3DDTG8I+1RRrBhFu6Gyzh2L4jAehrxDiSC5KJuuFkuqGajr7DEb2z/zFEFavliqQ2l+kkDUwjtqofSbaMHjSNuqwjTIO2aBFccDCHFDZe1kDjneKWn+KBl2sVGq5K7J5jBlMxC2ZlDIGQ0O1ZOu9KFUimVvHbA2TWVOLkYEFy0zmrxxgfn2CiCYm2LNKKUSOk2ITVItU60WqvEVSxQDmRgV9WEG7wI5M26NQbNZpa82wvCFOlZqjQXPK7TZ+AUOlAN3FCNmSByel4Ywa9E4qpIoaqZIX/UCawPEsjilK3Kbfc2MFNTKrq1i5Ps3SJRlq9TwH+hrZdryByQM2iTYdJezFsRUiWoF5vgJqQxV2SkOdOk9L+/2SaGMZCkdtdt7fQu1yqGkw2dzSzwZzBCviJRq0UBcVMJZkSQcUxjUN09KIuuXTF9bvfF7EwIGherbetZQ/e+93Wd2Spl7T2PLROFV5hcHRhfAfKs5TFtZg/mQq+v/yB08+V+bqVoIrS7QHj+ATWXonWe0bx1dr8dUbFxF8qAtd5CVM5wImsTDtoTwbvaNoZyV2VRLFxgCTr++g1FKiMGIz+K0at12nky+gCKtYhAYCXr575fVu61XTe9ZI4XFQ3jTJ9c2HeHfTDryKyGefWaFzak2lX6bzs9BqasCH1lhH4k3V1K8a4i2X3cc6r0OV4riJkX3PN3PsmQZC8T0z1+r6JLahMLEhRK7lTPLnjnQd/za8mhN9NThxA8+4RuMTSYx+0birMlbRC28bJrzt5T/XD/RieDwoES8Pv2sJxdAwm/z7CakGfo9BvlolOMOPlVNUKfUEcRbp7noRAQyKGxqZMhYR6oXrNjzHBxfvQFfEk1F+NkTZV/mXT/6Rk6WgVaqHSI1O8ZYs2g4LLnCep6M7WBGLxf4s6zynDDDFbewqbupzZQunj31ftobvTC6hPx3F7AzgPBkmfCyJlimhFEsYQ8NU52fOU1aq9TL60fn8v7WP8samY2657fP7l3DX48uIHdmF6gFtQb37/YvKiGrdwlnRil00K0YMOPEEUwcMfvYXa0h/1EvNlVn+OdDLLW2HuG1eJ/ZlZQMk72j868QqonqKZf5hNntL+E+GoCo7gbAvQez7e/h342rWbpjiT69/lNXePIuNUa72P879/6eVA2+rpideh/OoD+V5DUQDj2IJx7ZQ21rK2aFCJ/gMnOUmyutzfHzx/cyPpqmptqnR8pQcmym7xEOpxRwabKRmewLGpjluJTLtDx7jge5ajo5eyu/X76ES8fWlafxeJ/w/L6XFfgZfV0vs3i4842d1e8wX0bceRBfejrOtBjFpPmfQ/fow33nfpXyibj81+sy28cxdsQgn7MdI2+5k8moebhEuIeBl6D1tLF86ym2rHmdJvULMX6LOa+NXRIuIWv743uvJPJgj3DlYEakfimETWTeJt+mMR63YFyD1SB2N9xxDTZbcygMtLRLeKicu+gtRFLTGBpLrAiSv8vHZJQ+ysKq8Cttj2Vr2DYep35ZEFUkHM4iRsmh+JIm2Kkd+QQmfYvD2ldvZPH8XFBUWVRXdks/yImw2eRv+4Oj1TJyI4DvodZ+n2rYpNtxwiJuD48Q8Wd7YtI8DPg/jFzg66xux6PhOln+1NvCfC1ed3i5K72L+HO+o7nSXlD9FtmjwB3fcysQ+m9KuFN70EIawr9IaFKxywyi3J/PMhQZEg6SGRoeb1zzJwtgow8Ugv995NeltBrFnh93SR9+hEfwTYyi/buILacREFdGbU9gBByppmDZFJ9QxPP+hkv2Rzh9vfCeKobm9LU73crFsErtHya5r5Kev28DXNv4cv//CCprz02egaKGOZZk8GmAwZnO8GKFZz1KtldzXitZJ9DqL2mwJsl5KfoPnjwYoZm1U0+bSNQk8enn1P9tjwGIT1uZZXjdJsydzuqxqMufl2d4WDvXG6O/yoA0msLPTLAbEQ5HNMzweozQYxa6rnNDA2Sh5E30kTSnpI1htsnhtH/6pIYqtRQ6nG1HcB/2lj11RwVdnUxUxadCLJB2VRDzMyHA1ta1TjFr/W5nieUTXXLf+krVxgg0p/FnI18ZJT5Q4kmh0kxnVAnji58bVzaCGGVSx/A5NkSwLaieYd0mARR2TLF8QZ5EhGvkYdBdibmjgWHeMnuc0QicKeOKZ817D+1oQfQbqYwmC/jOeLzuvYU0ZeAbyaKKf+SzCCXhRI17mXZ7EWZ3DXquzIJSkRs+5uZpH+urp7o6iDyZwCjObeyOqV4SRc7SnitC8Fi5vG3Pjt5GA5d4bUVV4DMqhjIF0hAOTMY496yPRo+M5prlJbsWiwbLrHbeqRVcsqj2Zk/0iLqwYUHMW+vEk49uhNHAmXq74YGBdkOtCZzxIaVtlrGRw4miE/KEcgaNTKGINgAooxzubYkMArc1iY9UY1UaBUt7L8e46AgMJAhMpHE3DDhhYEbHg18lOqeIUciruOsGVhFMORanDYE869KkebNEI5ey25JaNcRSmar3E09WYL+hJciE4r3dlbH+ags/iB2/p4PZINws8ZSVzdXDYfSHy1t4J42/1c9vetzAVD+LLw+/e+D1qvOUe4C/mjL9wYLyGL33/7YQfOIKnRwTeZq4N62RPlNzOCPaqmY/VvuzKZbk8oz1RVtdP8XdvuIPJmx0OZmp5+tAWtAMB1JGzL/9Jn6Z4iAyL2jd2c0n9IO+tPsLT+QDbjsznzjuu4sYPbiUTmr54rhIIoLY386G33s3y5RNEVS9HSw67E7U8uuNK/D0G/kGHuu2Jc9z66SV+4kt95NpNrliwi99duoNa7RD6yYFClL4dKUT4j4lldP10IfldDtFHDpY7TFIZ6KrFmtgAjf5pKkm7wNhNNWirarnlj37CkliahUbZ+jcdh7xt8czDq+jdbhDuPlP5MmMUS9i9/Xzr8RU8UPDwkw9+j5imE3uJIfPR/gV8cetltPzDbmITw+42NRSkWk/R6pnEq5QoTUeh+CnyBffYA187d3OpwcuJv1pOuuaMmO+3POzPBgnty6D0ZMt1+RVIYl01NVtMbgxMur0E+vMBvM8GUI6PYccTqOEQhTUN5C+tAt/z2OQoiFK9h0MouyskqfslEOEXZd/xl0y/cs1jK8x0cV7vUHt4jPRTCgd/J8JfbrwGrUPHWpnlhobDrAyNsNJTLMfQ9AL/suQRSqZY51whdnL72VJAvP+7gY10p6MkkgEa75qk1GkROX5wxrKMz0YkEXnjJzO+awsYC9MoXg1yYtXFyphO3AY3W1PYpsP3Ni/j2uAgm4NZvrbifmjXUErnegbKsU8HR3Hw1+SpNnJu06H/OXYZgzs8BB44yJHxRjLtAfJrgljFCy8KRBme1dXHl++6mpa+DFdfdZDV3iQ3RFMsuORe1LVlz4DxTvOcOKjpVzEDKrbPoc6XJqb5GbGKHM7X8Vh8KfHHa0h3WkzumEAZ68abrrzOd4WSzkMHl9DhHOSa+ZXTTe2VYEY8jL9jEXbUwvGXn4cr2k9wdcdONkYzhPXyNrEA0DOjbXz14Cbi903g76qAtspnEdkxQnHUz4fr3sgH1uzlpo7uF+1TW5dg1cYecn8Wxi6G3fvwlvqDLK1PstFbJKI6HMmE+P6xTeiZUdQZMmJEI65QMI9xUoQJdox18N3jS9GGUpCqjDUuXoprL9nL8iuzOIrNvak2dg3WEnyyByXj4LTVMfDmet60vos3rn6SnY6HrqdXceDhdlKPDKNNVNY99WpQdQfdXxKpapQch7RtYlkXZo45v3K1UKA0ppJM+RiwA5RGfFhZnYYFtaiNDtH2yZPLZDosDw27VprIYZ+wApiOh7IDDvIlg3guwL59MbonIiQSPsaeLeE9kcPIJCti0Bb9rtW8aLCkEgtmWNM0ylBzwG2HqU1Vjrr2DmYxjykcPtBAUxM0+3MnL7qD7jOpr44T0kz8brmR6KNYbnop8jMms0H2xBs5sT9K+miR2EiS5N4A+SkFw5NDzU5DXFfc+JksPfsaSVoeaqpi+Bv81ARNdJ9NIFp0a6PTMa/bP0G4YztCE8SzQSYzIRzVIZENuOuGj8UzHEpG2TsaIvFcBOd4Ht8u0TkyIwokKg5xPumSl4Jd+RXAL9X7vtgSoaljiur6snhfUT/JyvphoobproI5ZHkZGQuy73gdB7ZXUX9iAs9UZS2Wo08KQeywd1st+wshWsaCL9pnPAf+VJK8E3D7orhXTjUpJh1GdvoRS7d1ZgKMHfVQHVfxz8SJeD14gzqrIsPUeM4YU1P5IN3JWuabfagVvJhUS80E8+pSrpF4PBNlb6qWot+hrbFAdb1FZFOSRYsmaa0b5+6DbRzeHePgtirU4UnUwuwozX0pNM3C5yu582bK8nAs76VQuDD5G/oFcU9nswSfOpMyu23DYvasWUv3x7ejaDZ+1eTN4QPuOgaG4uVHqWYytlHOdhXNVsYaeOzIMtr/4ThVJ4YJFwqu2q4Me7uMWHDFyYo6XA+Xxnp5c9UAb3nLe8g9HSfyWA+Vgj0yRirt58hwB09etoZ8/ZkYYjSc5kNvfYBL/VMs8WTd8ijRflQIgoRd5M7OVXzl8eto+kEXNWPlOLozMIx3AFqfnd7zCD/ehbnVw5M/XciPb2wis9BHYX6RFc1DhH15tnV3uKWdVZ4cf7fp+9y/bxl37d1MoaOIZ1zF36NTt3USdSSOPTiClxc38ak0dM1m4fxhamIz3lvwVaNY4JuAD2/ezzs37C1vO7muggjRHC2FeSbdyB13XE1xW5bWBw9SiTilEupYicb/OsB9qNzHopfZU1jVZctaDGN30/SiPVroZKZQGmqpXwh/vvjHVHmEYDlpEtgKjlgXIhZFEZ17K2QFzBdSryVp0pOu97JztI4dyQUoH9b41fVP8r6F+9xKocNFP0/Eozz8F2vJdmXRRo4y2wl6i1RFk6iqxaF8HV8ZWUNmooCTOv/JhNNicmjHh7HHNQ70GuUltL1+jl31RhQRe9Ygcc8Idl5YmWVHb7qQoyndiT6YcbOkKzFFT+8ZxUnE+bm9gYEbhlh8xThrrzzMaMJg8vkwdiYt6nWoCPJF7L5BwplRAmeVEBq6ydMPRzmoBQm5rVTLfQbKA7bNYNJDbPwIjGaxZzihSyDqm63JOKHH8vi3q1gBm5KvQEqzaMx2uQ4jQ7X4cayd0YRFbeKIu4/opqhlVZgsYs8iK8EyVYb2NJLSx8r5NrOIYDDPxtftoL1d2MWiTbnJfcNLeHBoMd5ejWJnmty+BEZ/D1p85u+tV8arGYkqbNSaiDPe7eFPnngT71l+iGtby+3JG6sSrG7ppxAvYmcqyyvzcny4Yx+3NnTjmLA0OkHK9vBXvZvJPG1TfMokf3Ss3Kb8IiCT85IbjWLO01jmm+Rj9bv4r9o1DA15ccYKs08MqIkMJGDiRNkZa3t1juvNlCIalmZR++gkau6ciC8hXtsqh9OFmshCOk/XE83ojSWKyyxaWsaw66uY8teAm4hTIeVeluUqSc9LGJgDB30M4HvZX/VXkgVti8WUCnj6zjwEQm6JiGBQ3GAn6aTK/cQ/nW2qLwCOpZAeC1Kc9vU3fjkcr4ERsVm+4ASmx+JYMoY1UWBPdzVPdjcQ6DTwHxgnuDOLRqIiQzQXG04uT34Ctu9byGV1QySaxVoKJgFvkZpQmiHRgrlYIePVSzCeDTOSVlgSS7I6Mu6+SlhM5EIcmazhmSOtqNtzBB5PYqfTFRFKPh+YeZ38pM8dC8K+Igt8cbxiLXNRLn2emZFgpFowCf9s3+mfZ7q5y2vGst2s3R1Pt/Gcvorfuv0ucn4vncEATFbQJCqRTCPO/GaM1RorfNv4n57LeOTIChZ8JY49PEXbpKjYmOkjnIPYNlqySNsd43Q1Gty/sI7bQsNujlNBLPJTEitMVe6F+cbO65jvLXLl637oOl0sHOJWgX85eAV37VvLvO9NwmAWOzX7Qmq/CM+USuiojno1nCgFeDxTTzxlQrbSEwhfBbNWALwE3mOT2OkcWw9FSQ9o2OMTYFeuypbMDkSSas3zCZ6uqmcgehktVQmO94aJ7B1HLVRSBs25KEMTZAoKP/10OyMpk1iiD7O/ILrbuCsySmYIy8IeHWd/fzXHeurY41tIxk14rLCQxksQfmKIwmGbf36w3S3DE9NH3rHonbCITfbg9OUhN7v6brwSPH1xtAdN/r/eBizDYbjkI9OZx7kAqR2zL025AjFGMzCa4fjeU5nGldH6UjK7EV3VgsdSdDX52VMdZmn9KPZxBV9vAsus3FlVSaQpJmBnd62b9htmYqYPSXIqzJbOMNQfIXWomoNGDU2RJPVqsrKys1+CwNEE1lF4CHFPnY1FiNlVdvtqq1nEa+sBEfo8xYXJe5JiQCKpVCwbq3+Q0A8g+CNICXeacDiJ10XkWZNML9U/HSZ2d7k5kkjonhA308VnVEteJVIMSCSVjOO46w9UviNXMltQLMct/ZRIzqZCe+lKJBKJRCKZLhRHdJmRSCQSiUQyZ5GeAYlEIpFI5jhSDEgkEolEMseRYkAikUgkkjmOFAMSiUQikcxxpBiQSCQSiWSOI8WARCKRSCRzHCkGJBKJRCKZ40gxIJFIJBLJHEeKAYlEIpFI5jhSDEgkEolEMseRYkAikUgkkjmOFAMSiUQikcxxpBiQSCQSiWSOI8WARCKRSCRzHCkGJBKJRCKZ40gxIJFIJBLJHEeKAYlEIpFI5jhSDEgkEolEMseRYkAikUgkkjmOFAMSiUQikcxxpBiQSCQSiWSOI8WARCKRSCRzHCkGJBKJRCKZ40gxIJFIJBLJHEeKAYlEIpFI5jhSDEgkEolEMseRYkAikUgkkjmOFAMSiUQikcxxpBiQSCQSiWSOI8WARCKRSCRzHCkGJBKJRCKZ40gxIJFIJBLJHEeKAYlEIpFI5jhSDEgkEolEMseRYkAikUgkkjmOFAMSiUQikcxxpBiQSCQSiWSOI8WARCKRSCRzHCkGJBKJRCKZ40gxIJFIJBLJHEeKAYlEIpFI5jhSDEgkEolEMseRYkAikUgkkjmOFAMSiUQikcxxpBiQSCQSiWSOI8WARCKRSCRzHCkGJBKJRCKZ40gxIJFIJBLJHEeKAYlEIpFI5jhSDEgkEolEMseRYkAikUgkkjmOFAMSiUQikcxxpBiQSCQSiWSOo7/SHW9S38ls4UH7hxf9eVxM53KxnMfFdC4Xy3lcTOdysZzHxXQuN10k5/GqxIBEIpHMSRTAC5rioOJQKmjgOODM9IFJJOcPGSaQSCSSX4DSoOL7Rg033zXBb/7kCL6181BqYjN9WBLJeUV6BiQSieRlKLZW4VuicfWifpZGpojmCigBD44uh07JxYW8o18Djir8hk7ZfXjSi6i4LkSwHfFSUcSb2YoCjntS5XNTFefUqbqIM7PFDuLNqdeMXYcziGsgEO7cl9xfvC6G6yOZNvKr6glu0fmVup9TUC1G834cQxM32UwfmkRyXpFi4FVihTwk37Cc4gITs6GE31tiY3UvW2InuMqX4efHF/PVnWuo/9Yh9ESR2YZSHcWpjzFwawhPfYHqmjRfaH+aBiOLoig4jsPRQhXfnlrC+I8aKR5y0A50T8/BaSqqzw+aRqnWx+jrmzFDDrZRnthbmyZYUDfBJ+v3YaglbDHzn8QGtheC7Omax4PPrCV6zxH0ydz0HLdk1tK4boR511pU6V6eyNaybSpGaV8XJKyZPjSJZHaLgUJzALXB4KrGPjTVdieXU9goPDPcRnHMxteXoSLxKFjLNBYtmaKxOY7XMFkemWJ5JEObN0cs5aDqPmGmMmswDPDppBd5aW2xaGkbYcGmJMRKaOEcJ/zVTKhVrnfAwSGLxiVNE/RdqjAZ8HBiKAKpLJTM839swh0b8JNtUwlELTrqktQYNlpUZ/ISB8sPtl6+hxrq4rREE7RVZ9EVC9uVAGXEHski5ANJuooTZJ7zYOYcyOWpFAqtITy1Cpc396HgUHQ0tqUacXpNtL7ZIyzz84J4alWubOx3vUqvhrFcgOfHG/EfT6JlLsD99ErRNZyqEM11KeZXJ9EUhbHRGMePNWNnEzCDhzbn8ftw/AaZNp26cIqoP4PwXaaGg6SHAijJLJgW2Geef0kFioHUpfV4bozwyVu34tfz2Gf5mIuWxpvuu4zk4yW83z52jmu6YvDaKBtSvH3+ft5R1/mij42ERfS4hVpi1qCGgthNEUY+WscVy57nHfOfZ7nhELegu6Tz/3a/ndF8+PT+V9f08ZU1D3D4HQoHV9XzjSNvgKO9MJU878emBP2o7c1M3u7Hv2yC61bt4zJfknrt3Mnx7ClHRcVyZcu54Y2VnizheX346pLcc98mJjIB7IEhKoXkFU3UXGnwqbc+gaHaTJR8vOfQGqz/SeL/7iSzhcS1TdRcbfDp1z2DR3t1FvRjg/O498nFNP/bIfzHzv/99IrxeWBZBysan2RjuBsHm74DDRx4bBFV5m4UpGdgplBropjzogy/K8KKFQdZ1zrihi0P39vAkXvaMfb34mRy2Ll8Zc4hrwAxnrnHfraYdmO3F5EYuHbhQVZsyaBqBddyO1sMqKrDn152F082tPGtjnU0fX8Mz2AOO5tltqAtzuF5xwTKdgsq1LlxCjvoIX3dYt537QGuX7ebbKNGWi8xUIjyz09dhb09je+JIQKZfloc7fTv9S308p53v4Nf3/AQ4cYCEx+wCH/VwTN1fo9PrYpQt6XEyt/ezYbWKRqCOep9SULquQNxyYEBy2bIrGLKCrrlX3V6inotTbOmnhPebdCKXO2f4pGYBytioAww83gM1KYGLtvUx6JLJkGxSdglRkoaU2MhPJkCfiofbZ5K1ef8/OriXayvS7iC5tWyoXaEH934U/7lwEa6wjV4dk1TCOosFF2nui3LLX/8cza2JjHw85FDlzP5hEnogcNlq1My7VhVPtJXL+KNN+1i/tLnORBu5qroEGtCk25IMH/zBKnN+3hgPMa+kQZ2nVhGy4/j6KMZ7GSKSscxNMwNi0gt0Mh12CyZN8wVkSFujvRxV7qZ7sP1HHu4A2NXF2omP3vFgKOp2NUhmmonWB4bQcUm70DBVTuO8L7jURyWR8dJdfjZZ8eJH9axfF6UwwWwrFlR16sEbdTGImgVerCaCkE/xYiKWqex9JIplq6bZMGyOPuz1UwNBejp99H5XABlp03V7iAaBc5IAcgXVY4+5+dwSy3eOpPIwjRa8Pz7TRVFxQqoZFs0FsamaPGURWHBcZgseDg+1ohigWU6jKYtRlIe4lnDdU1XNXqpaQxTVzOGppyZlLyK7eYTqIbq3pMVYTnoGlZtmPa6HlZUj7khghPxGPvHoqj78mgjZ7wgpWqDUoMPr6eEmrRR+x0c0yxnRs4wgaDF2g0p1sUmWO6Lv6a/EfEUWFc9gjFPozDkx7OL6UVRKLb7UVfAysXjGJrOZDpE594YRvcYvokKV/gvg9Nu4KmBZf5JejNVDOeDaDkVLZ5HTxTcfaywBzPmR+h+R3XcwvPV1aME9bKb8/BwHemkhjE2/d+BE/LjafKyYHOCpWsmWdw+jq+osNArvITCsHRQagtYNWkGmhWY8OE0RND6ChSOOCT3e3GKxYp4Tl4yFNqsojdpLNmSID9fJTfPIdqcpyGSozGSZXk2jT/kQ01PMTDqozisoCZzs1QM+A3yVy2jdt4Qi40EquJl0lIYMkW2OtSqDi06VKkGN9YOc13Nz/j16M30bA0R+ksLK5MpCwLJL4fPC8sWEN/io2pFis+94RvU6CoJK8Qfdl+Fcwf47ioSHe562Zib70Sa5v84yDdb1xLcCFc2dXHcqxA/z1OrUygwmAhxcKCNN4UGT4uBCctm/2SYT991G3oSPCmHSE8B9WgfyuBYeZ+3tlB8ew1XXvE9fN7K9ixZHpV8R4TV1RmuDYy62+7ev4EfPbucxn/fhVo8c99nNsYYe18brY2T+J4tov5bAWsqDkIQzDANeo5PNXS6Hr5fCgWSCxUS4wpnglPTgKKg6AaTt9cTvTzPWq/D/dkqdg1VU/3VEubQ2X7M2YX1viqir3P4s4Xb+NKhy3m+aymBbg+RJ04Qfqbf3Se3pIbEDfPdPBzL6+AEbP7lxn2sjE2go/KRb69i+LkwsR/vn/bjtxe1UrOhyP993x0s8kC1KsJ+Qygn/ycQ/xWVRK8PDvOG4DD2vL18d2ULhx+qY+9ft2EOj0CpwuK3qooWDmG920fwVos/WvAzIq5HTeeuTD26UmR/UePG4CDmuhMMr9rOF3PvYeB5D57njs4+MWC3NxBeZPC+X7uHDW3j2HaALwxuIv2URmlr+eEvLvRRWBdg09IuVoQmuMw/zMdbnmfPlbV874+XwJfzKF2VLwYM1SJkFMh5DGxdKVttFYJSX0PNYpvX//6D+KptCMKD6aUM7m9m9HAM9b4xGLCxJ0T93Stw8Z7wEa4vcNmiYyQaV5FsjGIPlyez84FdKGAcgOq/UfiPTZfhaYZ0G/gPWZR6LGqeOoRSAtVyUEUyYEYo5V8sSNI2jJkOVraEWqgIv8BLInJO9LM8gbahEn/9Ii69epA3rr+boNdiO038d2kVNV9PYQxXzn32S+NAzV0nqH02NK3/rBULkN/Yzu+s38uGhSOuh+bIE/PZsa0Jc2AUJ1e2oGcTjt+Ls6iVdy88xKbmQTeb5j3z9nFNbTf2KoeJyxwmJkRZtE00NkBt/U40TUNRhTfXoS0Ud4O54u5q2zhIQo2ReawaO5XCKU7fxHrTbdtZdkWahYZNUFHc1GAhzdSTmUGuZ8D9+dR7MRLYXBMYxbteZ/8fNKP8jYNT1j2VQ7WC+Uk/t67r5dLmIYYsjfufW8ae7QtJHo675eqGD+79EKxpGeWNsU5KlxXJ2eB5jlkkBkRGvddDcIFG3VqT9cv7iBo2qaKPXXujFLfpqE+Xd02OBYibAYzcBFa9TazFZlFsCrUJtl/aTO99BvmkijFeOdnfL0VUL7DIn+BQKEDBp0K6cgbpUkMQbWGB1esGyFpeprJ+tu5v4PiOakb3VxHeMYJivgrrJ60hjO4OTxJ/2MCJBGH4PB6wbaNMlvDsznLUaibf5CU1AjX7LLz9WXzHe1769xQFTbfxGOa5CTjiPrMMjuaDFNIllKwYSCoAHZxa001OPYWIbIgQyGlUhfyCCHVtfVxR00fB0TkRiVCq9+Pold1I1LRUuodimLqK5S1vq/HmaAqkX/Z3hPfJ18u0Yvs08h1VrKhPsjo8TtzWmOgJMHkggCeTm329KXQdI6ITXWeyvCnO6sC4O1EuDE2xIDRFybEYbPExZHnLeTZakVY9j4F2TjFUeZp16GgcJ9MK+2LNOPkcTKMYWNAxzvLFCaq0cj+XkqPRlQ/j5HWwFEKRDF7NxFBssrZBwdIpWgYtvina6lMs3BznyAIPxbyGMV45ok7z2dRtTrG8aZIV/knuGa/l+aFmnu+eR+Cw4T73TsjA7lEIeizsWBelehuz9vxXSlxYMeD1oLW3seb2o6y4ZpilXocu0+DouIHze6M4aQ3r5PMVGILA4yCe/0OrO/jPt97Id97+A1bVjvFP8x/j1951G/s6IjR+bfpdVK+GS4PDbPCM8Zsr3sGwWUI9PM0j2i9g5Eo//st0lnoc/rhrEw/vb6H2E10opUHCzuBrcvL7FVhqKPgjXsyov5ysdz7HTNPETqXxPXMUrwJVnNPv6WUTwMLhEp7aJNoLXNZHc438++AG7GPCC1IhLsOoCW+ewmn9BcejQCkMjg80VPYXfQz1+qi/T0NPVq6HQ5DI+PjYP76J4QYv6fbytg8s3s/n1p+0BCoEy6u4nifTBylb5aFciKnjSQKHVKzZJgTERFMTo25NiXf90f10+M41osQdYyga7UbJfYkKHPFg2UIIvMzfe3e0k4ONSXYsXIuWTKC6nrjpoUNXWCTEpFstpDBeCvI7x28kdbQKZULn+lt30h4Yp8FIsC/XxtFEHZ1T9fzrih+xxD/EX8+b4m0ffjtjT+s0f/UIlUJUy/F/W7Yzzw/pYpC/fOot5NJe9CUKpVArpYBCIQqRpx3G4nH2dmjkLlBVwQUVA6WIysitAdrn51io5/hi3xZyjxTJP2ti5oQf6sy+7umd/Nk7XKDmsXF+ekk7azx+Xh/pZd7CEZLJEqXqKHYqM2OxH9tWyKR8lEov/dW5HftOvam0XgMKDJeCfKHvUrp/qBHeKmJo5QVXXsuRKq15si1FdhUCpCcL6OOpC5qf80pK1m2vRvqaDq5d18cNLfsJvkSNp3Oqe2KFICyalqoxwkYey3FIOyZW3sZIO+45K+EQ3voAt2zYzZp5g1iOyh0D6+g7GkTf3YVSIe7rMdPHV8aXcVPVAIu95bLAJ9ON7BiuwdnRS0RR8QfL+x4JF/hSrKwM7Jow5oIGkh0KqukQ6oee4YRQgtN38G0NLFhV4neu/TkLYyPEJ/088JNLmTiUdcXorELkPhgGE1si+DcVWOnLU6WdsSRHLY24rVJyVOo1i2rV4nDJS9ERwQKF+UYSv+LgVc5OG4aMbbov1bRf0bN4PhGeiVPVZ986vppt3U3oP8wQGktDDvoOR5k0AvgoMDFhkipkCVjDfOU9V3Pl4mE+sPQgv7tqK/vGg/xcxEcrhLxjsCPbgc+YZKGR5S82P4JlqiimgicHlnB8GJBNFQlFUoRVkcx9Yb78CycGDB01quHbkCdcU0Q34fEjLehPZfA8nsU2Xz6pS0+W8BxNsv9YDM1vcm24l3n1E2RaFQ421aOUCigzlQhiKThjOnRU2ET/CtCTJvkRL4+rrXi2jeHb8RoHOSFyNI1IXQZvTZ6uUoBcSmS3Z2e0+toR91zYQ+0mjZULklwVqbQA4UujKzY1vgxe1XQH4wlLp5QyMSbzbvazVeVFmxdmU9seOmIJd58DiWamxi1qhirnHNOmh8cm6lnjmzotBiZMLyeKQcx8DjUH3pPtEgbQGXSa0KcK2PVVFFe1MrWynCtRdRS88TzqNIqBUkuEqkVx3rDgCLqicmy0lmNP1aMN9qGJLPTZhK5BOEBouU3NigL1mnlOee1o0k9P0k+p4JAK2qQCDodHNfKWMGMUvO1Far0m9fq5rujBop/BnBclnZ+28koRArMDHmxdBDLcLeztrubJHQ20PtmHls3jWBbxkRriigfsANb4BNhFvHqJHWvXURtSYekBrmw8gbc5xt2xlWipohsSnWlKJZVjx6tpbXQIRzSWhyZwypcBzVIIevOE/Bnidomi6JHmKGh5BSWvzB4xoMxrpnm1yXsv+SknzFru7Gkj9NkU1lQWO/uL4/5OPo81kif1V7UMX+1l35d03hbtpGtpkg+8fx31/1+K4MGZaSWrpx3afmIRjjmwkFlF453DKPfq5bUHUsXXrC8VrxetroZbO/YRbsqzP9fK1EQBa3yGB815DVQtD/LlX/0xwUBl55a8HCLe+UhmHqO70wQeOujGqSdX+yi9Kcw11XFqPRkKdmXmCGRzHjr3LmAq2AOR8rY3VZ1gi2ec1//eu8iUjHP2V0yHtr/diz4UxzeSoOmRkx8Ib8g0uuXFvzRyo8r4Zo2A6iVvl7CSGbyP7cOp/JzlF2GH/ZibFvH5a+9l46q+k10gz0weT/58Hfc8uAzv/j4Sm6pJrq6i8ct7UEUejQe2fXsBWxaPc3v03KZq/9Z3FfsORQhuPzRt18esC5F6/Qqshn5URLmqSu2dfTTfmcE5FWMWNtpIuYroHGyoPmgTaSgnFgpKbVWMfngdNd8/iLdvBptanaQ4BIfeo7BryyaKyxsoRBXMgPBwgnccbl27k1+77lEaNT+2Y7ueGd+uAPqBc5+lihYDmc1gbrFZ5i3y/JFG9u5txIpP4hReoUUvEtrjafr6vfx/e67k48v2EAwUWLqsHzMsOhfODO4tJQaICnIzv1LcbOiT37/zSyh726tj10dYGCxhFB1+smMR5tSImA6YCaywztRbGrhyyQib548R8BXdXIGXukTzvZO8r34nP7xsNVMBhcBecdyVhbD8xQ1+esAVtbea8qIFoyoNPV6k5se9eFsyMK+8TZR6RT0FPr3xWUr2uW5nsapX+DPj7B+s4SfdS4g9Muj+jZnAtcbUcpHa/mKMg/kgjvv9v8JvXAjseY2UqjwUqjXCSxIEQnmqT5bDZksGx+J1hPaCZ9CEgfNXdfNCvLUF6t88QKghj8fNmVHcnIByOo9oAa9glRTMRB59zzihgSS2KLMpKa7TT9x/zgtL9lApDgUpDAQITaNQ05J5As/1oL4le7KnqAPVUZTGOhgcRfH73R4EiVURnLPcH0bWwZt1aLtpmPqVY6d/140Qil4rFYOCIyqiOicxJgqoXuENEWOsSmFlK7qtEVZ0ck6JY90NPPL0UrKPJPD1WrNHDKjLixhrCkRVmOqu4sTeGqpzQ69KUTq5POPjUQ4eXMYH53VSU52irXWcYX9xxpr7iZvJ8ivYs3GJJ9M8LxrG8WqUGgNERVu8vEbf3nqqEnF8MyAGRAa4XedBuyXI0kWjXNl4GE3zvOz+TZ4UN0bjPLphPRmRKXZQBOVE055pPeyLEi1tUvXUCMZ7zvXK+HWT2xceeulfWgq+viD3PB9G69RR7BJOcmYvxpFcNfuz4VccmkLcbn4ILw1gNvrItBlUX54lWl2gNVAW3/GCzsCQn5BHQd9fIitCjW7//PN/rkZViZot43iixdMTunrynSs0T8mCQh5PbxbPyRxn26NCWHObDPkV8/TvChE3ZQYo9hs4/dM7kaqZIr6Dw9jpAiXHcQVxqNlDbLGfbMnBV6Wi1xqUrojgGCfFgAPehENgymLhll4aGybOLK5q2mjpIspZXoVKwBhJua9T2B4Nc1UjHrE0i6LRV1I5cKKa+36+HH3XUTzp8+8Zv2BT2ttadtLUWuSH6RamHh4hdm/iNbmWfBMOLY+X8F0GarVQuDN7Ec2gwolbPaQ6XmDlzCFyNQrDN+rc42kg0F+g6e4B7JGZccsnbuggskXny5u/Q4vHJqoa7sD3ckWDhqISVVW++Na72NXczD/vvRa7p+9kjwLJTHBjSw9XNPbzpdWrOf6Ej/hnZ/ZaPHZ8Gc8djdHEkZf3CwgTWnSOW7uY4hYb54Ycf7nkXtp8OQKaF0W33V3EnXgKs3EPx5fbdPZE+K/PvA6rexgmXlunxl/EqWW6y7X4ZR9quRpfVN6fXBzuJR6P9MY6Um9q4S86vs2CcBK7nArNs/Fmfu/gjcS+M0DNoZlZy+OE6RAzYbGh8tEP7eAd7/Pwo0QjlwZ6WOFNu4l2ZZlzss+AaKDoKCgekS9xJkzg60tS/5+7UEqVHf9RDJvQNWP4Fqex0PnTnhsZ3qegP7UPxbowfvHzLgYcvwe7tZbaqj5CJZN7Di0jPp5GLb22jGclk0frHIRs4Yy6m0EUzcFbm0fzv/TN1FsMsS9dQ2Ekg5qonB4DL5to1FDjJuk4ojY/W8RJZ0Xw9xf/nlgyOGIS9aYI2VmYSExrzfHZmNVgN9nEjBJ+VXQgEw5NhZQjktdU7j2xhmXhSa6pP+HuX178AyKGjS+qklwSxD+ioWUqJ5mwxZiiqy6G3daA2n/h3MmVgq7ahJQiN9UP0rIuzCMfiZG7p4g1NDNPuyky7V8Y0jiFoqCGQxSXGrBC5d1rDuNtL+HML9EYzhK3vTxbaGDoSC2ljIFWcNwYcF1VilsX76XRr5Grs8hcp6CbOkbKOO/Ne/KWwYlUlHyVASf7OgjiJS/fHlzGwb6gm6dxyjgT7vXcmiYWbc6wZsM2mgN5t2T47O8jlfMSydt4CjMzpj2/dQkJsvivO0yNYRE1CmzRJ2g18vi14slGQ6e8H+e+78pHeTLRhrbbw9BzyjkdPSuRUmMEdUGId7ceZ11kFEQF27EI+X6LoDV+wf7d8y8Ggl5Ky1qIhhV8JYfth5ZQnewkyGsUA7kCSvcgTl6IgXIsayYVgaI6+Kvy6J6Xfih6imEeSLaSH82ixkszltvwcrglqobmdhdT/Tr+RVEcn+7G0bTxDOaohTmecwcAkeCliBTWs1FVdI9DMJwjJh7OUho7KRJxZiaa7XhL2D4VO2dQ8Ko4uoaOxnjRpjur8Y1Da3nLvONcW993zu+JGmtCOsklPowd6jlrL0w34p42Lc215sTSy/OMKYL1Puz2+tPtlcuciuHO1Lf9yshbupssGNBLr7i6Vux3XXiI2mV59n20jqGdpRkTAy+LyCnQNbyNAewrPKivh3csP0iVXnDTiJI5hf3pCD+bbGH3tsXkJvwYKSjUwNKWYbY07aPer9AQAeXaAspRFeW477yLgUJRZ3Q0ylTMT8bw4DFKWI7OUD7I14+uJdKTIDI85S7uIxDPfnZ9M4s27OODK58jpIj1PcpeAaEXHFOBjAam+GFmrsnuZxYwlDFZeEk3eqBInWGy3jflTvmnmiKVefH7Y9kqvjGwEt+9frRDaVSmf/GrV4PdGkbfVMs7Gh+hKpglZ6pYx3zY/RfW4DrvYqAQURjZouGNGUTVCreMLwDJko/edAwrMQ65yjt/0XI19frlmKvz1HdM8vkVPyCmqRiKzsECdObqOJqt5+nB+Wj3J4l85wVZukvaWbYmzm1rf8QPvnc9/TtEze7xmTodar7dj3q3l09veg/ZaywKq8qq37dNx7NNozYJ1a8vYq95sSyzwibFVVmc4MxaCqmcjwf3LuOG9WNsCEywwoD7mjQSi7zEnjvTxUn0hxd+D9flWcGK4Iu7L+PrUY3vXn8XxjltFP93lvkS/FXzdj7vaedgBdWDuzTX41sY5LY/e5LltXkWhUpux9Eu08OuTIh7/v5K4gdLFA8PEikeIizc1HY5OTET8fKpe27nE7/zFOs3DPHjVffyn4s28FhnMxxOn9fcAU+3Rf3n0/z11ddStyLD+299mHsGV3PkeD1NfzqAMpHDzohFe07+gmiU2ubgVKuEFM9pASc+7rNs4oMW7XeaaOMzJ87UQ72MTgT4UvV7ed8tj3Lpsi43ZKCekxVRDhO88L017iP9bA2+5w6hDFWIC/ClUED1+li4eZj57+vB4y8xYUNPwSH83DjFIxfWDj7vYsDnL9G6YIxAoISesYnsnsKYeu0Zwo7Pg1MXBa9oPVshHeN+AUKliqYwOGIUmHnLRomEMKMeUvM93NjcTWNdgtxqGGl2sGMF+nwRejojFAeCDFoKvZEoA5EqVrcPEb4qQcSY4umuZZgj4D+RofmqKUJrcxzNN5A4XKJ4JOPmT80UWs7GMYskD4mSO5vS0fLkY3VrlHo01HAYVTRWegnczGIR8pjp5OKCgt5loCwuT/y6mOu1k0mq57SFLds87tnM/K31spj7skw6Xv6nZSWa5rgTYmAEtPEU6lR5MLarAtj1YUIrkrSEUqz2l9e/Fu1kq9Qiwet1fAGd/CPTL6gV3UE9q0mP28Qn4CeypkjdpUVWNmWo9ubJ2zb3PrqEgQmVE+MW4zvzFAZLqJPiljr3uB0TEkeyFNMmqmJRY+Txijr+C5HZXjRRhlKUnof4UInnx2KMJKAwmsU7msMpnrvKpVhP5R2LD7KpYdAtXBEUnPI6Hs89u5gju2rQj49CbuZKhxXTwkkUKG5L8PzSBrLVGguaj6GcLJssPxnlgxfvxy0vI5afRUYSPVAi3Cw8AqK3QOWGCBxdJb2mjsYFA1xaPYpHsTkyVMsjR5vJDRVEL/UL+u+fVzEg4s7BYIH2JScIBXPowzY1T4xgJfOvfewK+bCXt+IEduM4JWxTKXeQq1DKjQcdbPGQq/bM9jNXFfSGKObiEIlbQ9xw2X4ur+/HsrvZk/fTXfCzNdHOiUdbGXuixv2VqeUeVyz87g330HhdguANRZ6+52qKz0MkMcjiN/ZTnOfhgYlVlA5O4Dk68w1Z3GSgrgF8XeA7+wNxDVYsptLR8qLRjoJxWXkgEyiqjSrun7MGOetkDzaR2FXJgiCyfZxSt5e/XLIFRxcxWqh/3sFzYADjyKC7j+g4WFrXRsuvdnN124DrEdCFlHZbLkLknQaRDs+MiAGxpoXHK9a1EGrRce8jpTpC/ZWTLHrzFCsCRcZNi86kzte+t55sZwHtxAh2fhT1ZQwANxQwMOz2UBFDgtvt70J1wjQt7ESSwPNJeB623i2e7Rxhci/+5zRhwFl8YvWzVNWWTn+edxx3RdmH7lnL4C4D9XgXM42oLAg+28O2zW10tTTzwabjp5cnF507HVvURSpul9gTBT/bCtU0xDLokQJ1K8Yo+ERTr8rF9mqkrm6kdfEA1wVG3dbQR3tbueOBS6ka3IOWKcweMWCtW0DLxhR/WPcoEa3ICcuHFU/g/BLZj868Es5HprCaiqTHfBz69jKM/i40RLvSyqPGm2ZlbJjtb16OfbBEZM84ViIx/csvV4VQOlp42yefYsHSSVoiCvfl2rmj80qO7JpHaGsCoytNbjSBmRKqf8L9tchhhfDPFe773goya4OkrgzwpWsfpnCVzY4PRHnz/H6OjTXw0ydbiSbSeJh5MXAxsmb9MXLVCZ7/1mysYQV9qug2FDpZxeYKHkVYpCfR+sZRR+LEt5Z44vog3Z/YyMfrDtLiKXsOPlpzhAO1Qf6ctmmPh3xuyZMcsav4+/ZLsUYnKPpNhj5Wx9s293FLuJ9vxDvoeqyJvkfqyD91AiUjjBTrZT2BalUVxeYQozc2YM4fZ6xk8/n+LcRPaChD4+Xs/ulGJEJGIgTe6lD91hJGlci0KctNQd7xMGx6sfb1oBw5KwuxAvjUgu1ctiaJVy1XCVgoHDVVnr53BdvuXYpzuIti0aaoa8T/PYCv2eaK6uNsj9SS9OtuyXqloXgMAlGNN71hK0ubhsnaCv84tobuvQFi93WiZC+8V/z8egZ8HgyfTo1+KhvdKU+Cr9GSV3xeqqpsWtt6GVK8ZCe92LtSEK+8WPzZa7pvDo6RXR9lyvSQPOqFlDqtYkAJBamaD+039NG+aIJAwOTQ8Xa6jvjpG1KZPFbEPGjhHXKwXU0ljq18fJoQn2mFVMlLVlfI+Q0Sa3Ui0SzrquJ4PAWcrIXRJZI7p+NkyklbhcYwShQ8jec+yHa/B2dCQR199SVaak7B26dfkNaerwbRFtUYTrJ7pBom2rm2+gStwSS5JouRW+qoXzGCrymOR60sF6dVX4XjLQ8h2lgSJX9mwBJ13KKJyi/y5oiXnYHUPo3eezSKbwNqy5/H9ALV2vQJoeAJG63RYXCtRtTIs6BOYeXrhjncFSCv+rhiRReRqhRjOYNjD4YY2qYzdcBESRdetq2t4zVwaiLkFgYJdsAbLjlBLJYhlVUYer4aZyAJueyMeHgcj0J6c4j5ayfZsGgMQy+vVipeCVuhbyLMns555OLqOSJuJhHNzkqtUWI1gzR5026lQMZRSBQ0tu9o58jzUYYOWHDCA8IANeBwvIpFNUkuj/Tz3PJ55Aoavv2VJwZKdUG0RV421HbTHEi73o1jXfVM9Nuo8dTJBlgXlvNfTVDuceW+dx8R19X22v6WWhWmuTbO2xt28mRqCSeGQgSePkYls9CTYr4nRezaSfZYLTz28KpyB7npQsSbG+po3TzC237rKeq1Akd6WvnzH91M+P5DGANxaii34XxZf43jYMcT+A9ahMdtHn5jE5cExnhHpJf9RZhMOMQO5UXv3At/PmJtdb+fwiXzUFYqBK47t2Ng4a5qrB0qyvjL9LEQt99LfP1iTz2hEt5hoKdmVgyouRL+A4P88OB8Hq6dx+XRAeYbCerqkkz8mZ9GfcCtMAiIZIIKwW2+taIFuzrk/qw9cxhltFh2fb/CRaVOUdprkThgYV1unxYD04k44uptNgYOu28y2Ox1qG0scOsnn2dg93XEkzX83or/ocus4fGRGo79TZjSZBalMPWi8zx1r7nhjqgfe+18Jq51aFg8yl9teYgRM8+xviqcH4ewj8RR/rcy3guE5VcZf1+Mpct6+ZXqQ25aqhi3hbv9REllV1c9P/vRFQTi+9BmqLPoC7FDXjLXLqZU343DoDvXjJoK3QkPd/z3lRQPjaP2DJyTv3VgopHWJotrG8f515tjTIUMmvZfuPK810puSQyuiHBdZIyAUSJd8NK/rYlcZ4JwYXqO97yKASNZpJQs0VmCFk1krvuZeucaAltP4DkRf1UeAbUqQuQPTBpXF1hpmPzg4Q46t0XQqWwxIBBled947lpGngdPX/+01+CbUR/+Kg9LDZsDxSh9E36qtw6JNmi/xF8VAwX8W+e1dO31o+3vhPyFDxFkFwSZvK2DP7/hSZY2jKOFz7VS/vHqS9kabaBh67nqRixj7IQ8DN+ukVh3bpKWGL8PF730TEH4wCRkZ9jysSysVIqan4Cz08/tv3EbbQ0TtMcmeV/1EYJqEY/ioFXIKphWXYTS6nl85jefZUF7OfFPzRQ4Phnhd5+9gVAX+AcKeJ7rRHkFLnCrIYq1qgUnJHpBzFDDoa4+BosBvvavb8J+2yOs6hhmo9fgC2u2UbI02gwvqpLBjjr0//bl2IMefEMQPZg6naBq+w2y81QmNmn85spHWRI9Rkd4L05YxTRsni3oPPw3yzn2VBVO11GUV9qa/TyTX9WMZ3WIP1pzL6ujZ7reCSxb4z9+cisD21QCD+xDTVWOFe1oUKgSSb/l9srigX/mrtU89HAH5tYulOwLxiNxWY4EUap8aAsV3rBqD4fHAxyZ0ULiFyPGqi0bj7DsjRkM3WJ3vprtk7UEnp5AOz59Quz8egYSGXLxIscK1cR8KRyvQmmZB/vAq8iY1XX0Bh3vOli2aora2gK7etuZ2q9R6ixd2DWXXwEiScVMerCLv+CGchSSPWEyAyU8hRmIqQvXutsX3iaogs+jkq/TMIZ5xc11hCBzGg2sFQodwTTVWo6ESNJRhSVlw/+y2NT5QlE1FMNHW02CRbXlvIazWdo0xGjWYWxdGDWpoJ50+TsRFbVWZf3yIdobXiBEHdg30ciRkWrU8SR2qQLcoJaNNpLHKkH/MyHsOj92dYRjixrd/vKaZrN+0SC6XgGhAstGzRdprZ1iceuZa6KnNC43xwk0gG+4iBFK0hmPMpgO44mDmsq5ouFsKzq/MELNQo0ll0wQCJ65DrsmGjgwMY2lhfkixXGd9E6Vw/NqsbMKC5aatPmyVGniGVao1mzafEW2rBmDZg/ecYVITQb1ZGtb4cbOtmjEVyusWpyhI5CmSTM5lG5kNB7gxBEvXbtCDB/1QGHmJllfm014TYmVkUnqPecKEqHdxvujJAdLBOJnrOxKQNwvosLGLdY6FdKYDDJ6ogpSwy/Zmc9SFPcl9l4emcAMFTgyE+6nl8ExNDf00dQcZ2XduNstcXQ8zKFjjTCUdcXYdEWRzu/c2j/MZKPCw8kVLNAPQMDGXp/BeRUZwVoohO8Sh5o/yPD++kP0DtTy8bveQu2D+/H3zUwrzLNxTJV8TxirxYCWl95HJLjW7DUpdc3MwK0WLKyi6S57ucKTI9dq0/eOWprHxglOvQLLS1VQa6opXWFQeq/Cm9tO4NPTdJtwactRGhuDbOOV9W7/ZfHEHWp32+g3v/TntzbtZVFVL5/6g7ejH/HhGSj3JS0tLOJdlOYL635MjVgY/CTiwRI5+t/fdxm9e3Wah49QKYjGWupggeZ/SaB4PPQHffzp1W9wS46CviL/3+e+TTQ88y5bbTKNtvXIi6zG9nCC/7zq5+ds+/zzV7Lr4DLqtzt49vejHj5ryWVNZfTdC9i88QR/del95/ze3+zZwp7dYVo5wHShxbP4nzzCQ4PLeHK1Qdtn+vhg9TE2+cuCp15TqQ+YfGXDw//7H3NEIbRNyi7xNz3rObS7hua/GcNOpXBmeEnkutXjtL3OYr7PxvdCO80B/7iFJ1EBovNlEAmX5WD0L+5JKz4pNFiUopa77+X+MTy+IvdUkBiwAwaJmxbRtnCQLb5y+HbgQAM7711BcHA36ll5OBea825oZ8cCHP7JfJJv6mJ56yhfXfYQX/mVFTy/Yj4NX+vm5XqQKMEAWm2Ejo9PsGTZBJfVDPCVxy7jxO4AtT/ZjzFaGc0itLxDw7Y8gUUWLKfyEE9AVx9HSjp/m3wLGz7QRU1Tlm9efQdPNFbTdaKKQ48sxLd7AGPwxRUZ5rxaWFjDwvecoLV9gramSb74xNVkx32okza//rZnqJ5n8NCtKwls7UafvLCTk2iRrHdPsScZoFQIs9Z7rluzwV26PcPX1tyLskhFLagoioLts1D8FhH93NDIM+NtfOX4BvLfTlF39MKW6vwyOKUSpCy8Tx12xY1PhEcqqY2qo/D337+Sqk6bmutHeXe0iwUvuDaCDy7Zxy2tXRiXlkMJoqPoaYRnoG2ImvALroMDtT/toW5rOR9hunEGhykmNQY+rvL1pjV8t8YgudBPocmiFCtbnyKEsyg2znujPQTU8gJgSbvAs5Ot/Hf3Zqp3qBgTFmoiR6ozT2zsBFayVF6cqEI5VIjybLKewlDe7Q1TaZWres6mbncO/6Wi6E4TgUsW3tTP5gUW27+8EKt4RtlkmhVyi+Gvr36SFXWTbsMuYZKealFUKfiCBS554z4a2ycpODr3phvpPFzA//jhaU/cPO9iwE5BbhcMX+GnpdlgY3iExUtbGCBA/pEw+mgBPWWCbYuCXjfjtlCnUFOr0NqWom5DmmCVSWbSz9E9Ecb2KEQqYN3pUwiXoHfCdkVBxZLJkR4IcvS5KrxrYiwoKrQvGmPh6gz+Zh0nk2WkpJEOBdxBvdWfou7kUqvptiCZhQWsJQqWR4MJnSM7w6QnwwTyHqpMm0LQodgWwb/rwgdtxAOhTqboPRzA0WqxW4MsDE8RMcoTiE9MlLpJddUwVJV/R8QTTy93ehKR2HYoVcOevlr274jSdLQfz9BL1F1XCsJfa1poEycn2ILN4XSMeaEUNUaWRn8SggYEA5DLXZDV7/43jh0IoKNT3a6xdtEUetRhnid9zj4d4YT7ouHl/sq5+2ds3Y3dW30FvAMz1A0qV8DOK+SSHrrrvFixIMlxP3khBqrLYiBRXSRXneNgNEBAsU6KAY39kyH2doep3qbinbAw4h7swWE8Z4ugGcI2FArzAkSrs8wz0mflNZenyPFsiN3DTRQnCyiZ8lowlYRatPH0p+gbCnFgvI6W6lFqmlKs8GukNzdglUToVvShcci2qGQXO2xsGKbJLwzJme4s9mLskA+9DlbPG6I2nMG0VPYONjEyBPrYi4X1hea8j+baZIbQfft56I219M0L8Ns1B3hHxw5W1lTxyY+9h+qfjhB9ZhxbDGA1MeioZ/B9Xi5ZupvfWvIEe4sNbO9cxD89fiu1391LZKQyPAKzjlQG51AXB/6qjkPrm7jv04v5k+bnedf8PqyP7OEPrriWe0cXuw2cPrx4K+9rOer2Kj9S6mV/IcQ/dV7Pc51LMPZ5qfrxHmKBAKzqQBOGjRhUqhRebi2X8+06t4ZGeO6LzTy6JMyJDzTy1Uvv5qqG8sJDr5SSo/J7e28m/pjDvG904RQKM1Pf/RrJWTqf7Lqat2vH+O2Wnbxl3l72L27l2WWrcQ4dm7YcjrPx7O7GGogwmF3ON96psmLpMJ9v3HWyK9xr41ghwt+PrmKyKJ77GbSihSu6UHBDn2o/RPed3HzyY3Fkh1H4LPNf9KstZ612WElNbsxqDwOfXsKvrXqG91UfPrm1PEmKBb6G+up58rHVNPTsRp+qnMTB0+QLcPA4//rIMkKF1fzZm7/LQk+SDQ1J3vaHPeXFiYRX0LFPvxfhBNGHQPzXXbGxgiROcWUrsQ06vxJ7xu1GOZkJ8ujPN8CRYUKcFU6bJi6YaTf+wwhaV4BHfqOW+Z4k64IZ/vHS+9hXH6bnzX7yJY2OSA8dsf3EOlQGFT//1n8Vme/4mDquEzt+BD1egTek5aBNZfhRzwK2Hqnlc4ufwadamI7CzkKIwz2t7Nk3n6ldEyiiheQMI0oE7Z0q1mcdvr5pLYF2jeTyEptjw7y+TpToOOxL1vNbe25EGzWgM4/VmSfQP4I3OY6SVFGzYv1vBfXYEE/Hq0g4EcJ9lrsi27SdRyqNcbRA839mueO5ZTw8bxkTa3TeuHA3q0XdsVauYj2bvK3x5eH1ZI4GsXf6MLZPER0olmO2s0gIuJjgu99HKacz2miy3jtFbI2D9gmbrXcuYCrtI18N0YdO4Bmdvox8NZHF++xRsn059nd4+M133syHl+1lc92ry+8Rnpsv/+RSjhzxMbU3RamrkqbRMyivacvMk97UTHCtwV+vvZ/V0cnT5d+nFpb6wrEr6H3KR82dx1DTld1ILLx1ELXPx38XbubmzQe4ZHEPLbpyet0OSymvWih+EqJtwtLoKvl44u71nHjWED4cKoG6zWO03mC5jVLdzrUWBAdMSqmZufcvmBjIHtDcJWT33xBFr9eYF85xXVMPRlUjeqHaXWZzvneS+d5RwpkI/cMhdnU1YDypQH8O/+QkFYlQnakMx7v89FT76LECBLQSJRQO5ULsOhLjue0NRIeOoKVmXgw4opphFJxR2F9opjAYIlkosWVRL4urR1wx8EhvM08MNaAPeIgcSBM5mEQfn0AXoZzTlFAmknR2+sk7XryDWddtN23nUSqhTpUIJgp0plso9gYZMQ3a8134GyYp6WeigafeZW2NbX01pPZE4Gk/2sFevMK6OOe8ZgkWGAdMUo0Gx1fGqAvreIMWCzYPcqivg0zCx5Q/QOQZMQlPnxgQYRxtaApzCCb7/Bxc0MClVpi6loS7gqnlVVAN2w0VqC/wGAivVG86glnUUPKwbVs9fXsVPHvK5YqS80exOUh4kcG19b34tTMtrgUlW+GJoTbMniw1nQPlhj0VjHcw4+ZfHGpexrzwCNFAgnyNgi6WYVeg1hfHdHQKto90SWUkY9A56WPbcw3ED4heRJUhBppb4ixYmHfDNW4qpOPgSdnY02hkTYsYsMcnSCR0HvvIIr775npim03uuPm7XBEYdl+qotBdMjicjfD7d7wd55kUkUdOYIs+85VstZkW1uAwke8A34VPKWd635cvaIFqZ++raroyXXh3dOPdAZEfwQ+UMD9khbvdpkSrc+hM08iXOnbTxEqZ7Ppd0ZrUxOPsm/5zFP+eZaF0nsBzDNqegCeVEE+x6mV/xWYSzZk8U4s0SxFlU9quo+wcr+P5PbczfJmHjcs6+dAlj/KG27ZypLeNu39+GeoMruVljOTcFsTfU/x8L7Sa/PWrSSxRCczPcf8bvk/IONfiLNoqH3nyDYx3RYgedjAePYARn/lqiYsR2wO2DzyKhvbCLG5HQe/zYk/kp79t+mtESxeJ3LmXrYfbeHLJeobeZrvn6NeKfGndT+g3q3kmuYidfW3o+3Xq77ew93dhzFCTp5fizZFerqkRwtfBdGxKjul27jy53MK0c0EzwBzTwoynCD5mohyw+fKD805bB8J6S9kqcVPH090HoyWcShcCZ+GehlMuU3vRZ1QmpydvsSz5ySVwTn/2Sv+IWAykAs7x1Pf/wvO4qBEnKhIK95+galQl/niGe37SSsb2kUg7VPUfQ3slpaMXCPeesE9ek4yJ9nwPoWMKetjk3x5pwlDPHeUsscRv/zDh1ARaApRMsSJF9MWAEIlKEUzHQncc1xg7hVKyid3fQ+rkip+zBXGvOEOTqJkssalyDxRdsXmwpom07WWqlCecGUSdUrH7HRBe0gq6v37QtYJn91ncuuR5dnbP5+CRZkoHB1HGZ0YQ6xc8CSeXx38kD0fgkZep7/RQoSEBiaTCUDJ59xUcLAcD9lBebVLgF/GgCkEsFaufGD89wDxC7CX3U4kTmNYjm5toaRNlCgZyIRr8eSJ6uWwtbRmM53349k24TZdmG0oig5bIEO47s+0A0ZPvSgR59WuWTBc7eurZt8dgoe8QT+9rYufeduoG909r+PVsZt/Vl0gkEsmrIvJYD8XDXt619N384Yqt3N560N3+04nFfLdnKao1WO4sKpk2an/aC3fDt9QOLDtJvb3/ZRe9mg6kGJBIJJKLHBGLFgm4oe8Ns7PWQy4sloaG/VkvdiKJmq0g//kcuiZYIgOrXN4502JMigGJRCKZA2hZi9j9oxzG4DCNp7dXSna9ZGZRnNnUeUUikUgkEsl5p/J6NEokEolEIplWpBiQSCQSiWSOI8WARCKRSCRzHCkGJBKJRCKZ40gxIJFIJBLJHEeKAYlEIpFI5jhSDEgkEolEMseRYkAikUgkkjmOFAMSiUQikcxxpBiQSCQSiWSOI8WARCKRSCRzHCkGJBKJRCKZ40gxIJFIJBLJHEeKAYlEIpFI5jhSDEgkEolEMseRYkAikUgkkjmOFAMSiUQikcxxpBiQSCQSiWSOI8WARCKRSCRzHCkGJBKJRCKZ40gxIJFIJBLJHEeKAYlEIpFI5jhSDEgkEolEMseRYkAikUgkkjmOFAMSiUQikcxxpBiQSCQSiWSOI8WARCKRSCRzHCkGJBKJRCKZ40gxIJFIJBLJHEeKAYlEIpFI5jhSDEgkEolEMseRYkAikUgkkjmOFAMSiUQikcxxpBiQSCQSiWSOI8WARCKRSCRzHCkGJBKJRCKZ40gxIJFIJBLJHEeKAYlEIpFI5jhSDEgkEolEMseRYkAikUgkkjmOFAMSiUQikcxxpBiQSCQSiWSOI8WARCKRSCRzHCkGJBKJRCKZ40gxIJFIJBLJHEeKAYlEIpFI5jhSDEgkEolEMseRYkAikUgkkjmOFAMSiUQikcxxpBiQSCQSiWSOI8WARCKRSCRzHCkGJBKJRCKZ40gxIJFIJBLJHEeKAYlEIpFI5jj6K93xJvWdzBYetH940Z/HxXQuF8t5XEzncrGcx8V0LhfLeVxM53LTRXIeAukZkEgkEolkjiPFgEQikUgkc5xXHCaQSCQSyUm8Hgh6ya3V6IhMsTwwxtZUG9lJH06/B214CqVozvRRSiSzTww4StlPoSkOOGDZKorjzPRhzTnc66CI/3vhdy+2AbZz6p1EMmdRw0GU1ii5j3hYsmCCD7d0c6B7Kel9dZgPVKE+npFiQDKrqBgxkLq8msxb6vjPtQ9xrLuWf73vUoKPHkVL5Gb60C56FK8XxeNB8XlJLasitTRMvtHGUc8IAsVRUExo/K9OPCPymkjmKIqCGgqy6LZJlt7ezXVtU9R5ckQ1nS/Ne5b71QX8tXkpzTvBm5jpg5VIZpkYUAJ+Io1QvSJF6+IcZjDJsvgIYwc9lEpAtgInH78PT1hj44YhupNVdE1E8R+Po5izxJuh6yi6huPzEl5cItySpyOYIt+RI9ORolgjxMCZ3W3boWQ6jFdBcVKH0vRbPY5Hx4mFWbV4mOqqM/dEwdY4VKgimQqQTxkE+gtQKM7IMUp+AX4vasjD/DVDxB0fw/koDBrYBv9/e+8BH8d53+k/07b3xaIXEuxgr6K6rG7Jsi33xE6cnvjSL8k/5ySXOMnlUi7J5RKffWlOHMexZceSi5olWbI6xU6wAUQhelsA2+u0/2cGlERKlCzbIrAg5/EHMrA7AGd25n3f7/urqGETUTZQJB2fouKXKphZk8wB21RFrWDKAqXVEWKrs3R1pNBFg5HpEL3jDbRumkFwqWhBLhg7Dg7LgaUXA6KImIjT0Zpi48oB3EqZjvYiH3n/HP/xwjuYqQQwhsepOeqiBNf6+P8+vZ9/PhXn4ItraPrbI0iapV5qH9HnRQj4MZvjNH9siHW3Jvl4+Cwuwbjo8SVTJ6Wp/M+GmxiZ9mNkFn/bYwa8aNtX8SO/3seezWOvvD6jefgf03HSPY3M98YI3DcFyfTCOZrCKw6P2llS3hrmD3zO5sIvnrv2mrnueATXmih3/q/DdBstDE42In0zSikgkN6i4w1UCHnLJCIpVvoqGCc0jv24Qi1huCWSdzRT3zXJXk+WT8+vpf/5Zsbub+EDf/YdBnC/3sPmsDhYLuYLX1ie90Iwl+T8l1QMmE1xxBVRtn2in+2dSa4OThMQNHyCyQ53hc/e7GXGJ1M3TO0xPYshGqh6levWnCAQGObrf99AuSBRy+gRD7mbVrPhmlFWdY7ynth+/IkynmAV5Q2EgIVbEIlLbtI3NZHyKYS/vXQ2UBMT47x4krBY4dcSJykF+ihtlJi9toiq6hSqMn998jbKsz5cSYnQkIo0lUEcmqRmscRxMMj8dREKnV5avzYH6QJmsfjWfl+SiK2rcu2npnn82HbGekPEv9b3+nlysa+psZ4N706y6b0n2RUtsk0Y4t2hJObPSBgS6D4TUTSQRBOPrPNkMcEAXmoJIRYh0ubiV+7+KhvqCxhlkb5PNTDTa6BPDPKl3m3oBT/Nz5i4sstxFVo+GD4XhRvXYKyvYLZU7df2Rge5OnqWDYrBiXKcp3JNdB/pRBwSiJ4sYYxNQnXh2FqjtLmZ6roo5u48NyTOcFXkLA+mtpJ90U/5ETf69Axo+mUoBgQQPB6iKzUSe9J0bZplRSRNVCwxrIbxCTr1cgmaTYz6Gh1UVRWzqNqLUoM/x6ZEkQfFhDUbU6tocR9ym5v1e1J0bU+xum2edcFZxNfIT+unvAEZ3UNa89PpnscjGiiCRLXRRSWxRI+NZiBmigz2hZEljXJMwuVR8bpU1nnnERUwQjBRp6KaAkXdxTZflvKcimtWQmqokh1XmQj4cZ+tIpR0y/9BTeEGaZtMy84y0kqVzL4A6CrCWxEDooCwxoVvu86mHQX2GQKmppyzErC0pvVVQSJrJ1m/OklE0XEJGi1KGcP/6omppkHZlEnpftKZAPNpH1CmVjACHoSEh831SQy3m9OZCJmTApVxDbNUZP5oHLlgEuzPIVRq47kyZREjEsCI6rj9Khv9cxgYVDWR3tFmzAqImkk1LGK4LFfc6x8UqWDgmVQxLZ9tjQR1ix6T+PYq4a4CgdYFl+HmSJ6uSJF1ioFRdjOXzyEqGYQGCIZKnOhxU5534U4aUCpbUepLeg1azIsQlIjW52jaWkRZJzGy2cQVlTBCHtQ5L+6CSGi2TPJZN1pOtdedS8XSzOqihNjcyFV3neD2j52gU5ZR0Zk3dP51fi2r3Tk+FukjmMjjj7uodaKigUsWkGvGHntxCns7aN1d4Q8++gXikoJPuLhwsYZIvybwYraZ76bX8Sdtj9PiquJBohrTUSNL4xAVCmVcJ0b5/Ge6qLT6GbvVR2LlPKsbZvhM5xO4BSshRaBVOmdalg0+u/UJ+1trCutRdZ6cXcVnB2+g+Y+mcA0WMd7qjnuREGICrt8X+WjjUbaJM/zkoZ/C1FV80+k3/0VRRFAU+ESQ8B6BG70FHu9IMZqVltxPoHtExt4VIbRRZLf7jT/vnKkyWPXyaH4lTx3dRKZbpJET1AqVOg/llSHqFA+PFNr42tQKssk0Zn4hw6bu70apNUy/h+o16ym/o0iga54/2vgSFbPCZM7Nf/mbmzAmBNwZlfmrfFQadNSG18fZBHrKtP3LPPrcPGaN7Kx9gQrXfvAQN4eTbL1IpOYWz7z9xc2n0DAoGhofPfBB5rtjtNxfwRgYgcLSjv3ijlbkXQHWf+ggd4ePscGV5W/mNjBRCfLl5G4m8yE27Zjg2ncM8PAv7iJ3RsOYmrl8xIC2sh5lVYDbfvwYO1fPUC8pPFJooX+wgd6TzWS7ZVxd45z4sEzZznNz+KFRZKRQiO03DLF2T5o6yYXLTh9cYH+pjhOlGN1zzZSLlgKVCXynRHZSQ51PMfFXMp4mhVbZ5P3rDnJ60k83nsW/DkNfWLxHJpCnJRqHJFy+KvMh+P2r7qLSIGHWGWzcfpYNvknWumdokFx2ZS3rattlkXti02zxfIe/+ultJA/XEX1gHqNUAv3SmuDeKi5RY31wikZ3FZ/pphwXkKwN8veg2uQj844OfrPrIDvqp+3s0NxMgMx4iHpz6WJuSl2NuDb5+fPrHmRj3cUFzajq42A5xqFH1jDXJ5I9Oo8rPUw0W1vj3785Q+T2IhWpyvyAn8kXW/BUcwjUbqCqFNUIvX+Kj67qZ3tiChHddvk1+XT+5KMPLVgGVBM1ImO4TXTX63fLw21BvtW0EuP/mJj9LDnqmiaUTSLvDD5Bk7IgTsqmyoFiM4eKTeeOMpEEg72BQRrlMk2ywB+u38eJeB0PNK5C+4yC0efBtCwEi4kkYTS7KP1slB/r6qGzscAD1Ua+8o3NuLpFhoYLlKsaFTOHsLOJ9j0CN12X5Uvv9pB6XiX6AJePGFATPsQ1AdbvStLiL+IxJU5ONHC6t4G+lxJET+SYVVz0lsOULWeiww+NIEkIfh9NTZO0Ns/iEUQmKgFyZRdKEk7nYnTnYhycilHKu9GyMq3PlxDnC0gljUrl1enOJ6h4BctUtQRiwNreW4t2voiYB9/cwstVj4sjRpxCo4Rh7W6EObR4GT1iMO9zU+cv0RDI4xcE/O4Sbe4Jvrl1FWIlROWpAKjVmhADRsCNFDVZ7ZnAK2oUq4r9+Yvqm5szjaAHd4uL9t1l1iTmSEgFTiUT5AZlOKsvvotAEGzztBrzEOsSad5Z4dqmUSRFIKm6yY940DwSekhCSpkM590cnw/S/VIdpV4d95E0EoXacbhZysrtIt5UoHVFhRI6pSmT6gkTt7rkhpc3RXTrBDtzdNUn2e6fwbAWSQR8ismu1a9aMkTbWbjwv9cS9tXxtLuJ+aDlYKgB2tzI60TalSIeETRD4sxUHcdnohxJhl45zBIDoViIdMRLvkGgKziHz13luDtMf2eAQkZCHl5EMSAIqA1elDUKq66rUh8oEzAr5E5LTB4MUN2vwGjJdseYsoHWKeOrCjQrKtJqHc5e2oG86GIg3wrVzbDXVyAgVymUXbz4la0UDmSJHuq2A53OtnkYm9lDSvNbe4vFPsXLDlMSMQM+Ep4CDfKCSe3TI9t5om8lzZ/VYS4HuQKR9Ajhcz5Bu/ZTQxyhq5Oo5yABQcU0XXx5/3UMHZNpoIdaQShXcT13CuXcrDzyGRhct4n71zeTWSXzkZ3d/NaNz756PCZ/se5Z9pfb+L0NdyIV8ohWKuISU97WRminwgfD++jRvTyfCVP/ZJbKcOUN13PLeFbZvZpt18zwO/f+GxO6m4emWvjzR95H/Kt9NPT2L7oYsNwVRtxP8mPr+Nmbn+cjW05gInKwEuGluRhP/9eNTK0PkLzdS8v9AkrfPMKpIWAQd224pF9vWets5/rW/Vyd6CFpSFRPzBD61smaFgIWLlGn3ZfCJ/3gz3dILrE1NMYByUeqBhLQXFdn8Nxp4pIkRAzSRQ+/8c8fRH9pGs/R86xgAnwtvpnC1X7mPh7ky5sfZr0/xV+0vciPv/c9nG0O0vyZ+cU5aUFAkGVmPtjKyr0VPrfuG3zy8Vt4bH8rDZ87AdXCBfFLhiSR2maQbzcQBJM1TTN4YiKFS3iKi3Zn9YBC6s4O7rhlhGt3HMIjaRwp1XFwLgEvjaMMV16JeLbWI+vbzZFx1BiMR8IY+fwlj6a8bKlU7UjaQ5+NMlDn53nFZHTOIJSeQD+rI1RU0LTXVReMbcrQ8tECjVErm0BgXq8SemKc8JElsAq8Bc6PmJeSOSRzmnA5irfl9WZcSxDYnpJamM0VGTFRx90397D5ulkroouDQyt5vGcFldFZzOzFdy9WYJjZnuDDH9xP1/o5IpKbzx3eQ/eJGNGv9eEayy1JkFR6bx3+LW7++M4n2No4Y/tse1QXR59NcOzJNspDadyzWRL9MpK1Oc1Vz5W+rE1MUaAa8xL0ugnj50+HdpNMGQjUfhpxNakw8vcJnninycgGP3cERhjT3IxrXk4WWtCtYmILsadkZgPMjofRojo74pP8aMer8Rq1dHdSOT/yvIi5cuGsTEGgFBERraAh88L7NnNjHat2z/OxlQdJuPMUTBjXBEgKeJLC4o7xaIRPbDhJw5oyn0luYPz5EuHvDkP1NYHMVmErSSAcK+DzVzANgfH+epITOr5LKAcWRwzIEkLIhftqD1u65rmlZQDrERyZ93NgIAHDGaTUhZOWdZvW+OYpBRUm/HGEUgnTEQM/GJqOmc0x9oKHcclv+63MSgWvlrEfwtdtxkQB0+Mi2FFkxVVJQl5rXyeQNQxcfRm8Q2pNpu+aigyKhOYT8Aer+D2zeGWTOvH1AyhnKOQ1CUHVlzTtznQriCE3nnVuNm+aZue6UUaKEc4MxTlzIk5Tagaxalw8XS/mQtwUYOf2MVobc0wW4pzoW0H/CS/xEycX/2Ks58btwr9BpHFvhTvW9qGIBmVdpHcqzGB3mImngxipcZRZA8UyBiwDTElADbtweWTcpsJLc50oxXkiTFPr6HmR9DNeTrdEyCsmmxsyDJQ9DFR8vDifQDNEe7K1nARzExEmB+sodol2AagfraHgzfNR5xXK0xJlw45FR5AMog1ZKjEdw+/GUARExUD0GYR3GHSuz3BDfICCEWWq6GFwTkAb1XHPVBbtnAVRQvD62Fs/TSRa5ksDV1Puz+LpT70uQ8PwyBBx0RlJk/AuBDnmJv2U5qq8hfCh2hYDQnMD0fUKP3r742wJpBEFkTm9QvIRmZl/CaOnp16X4mWJgXcGJpkPB3guvhUznV6oKufwA2MJgO+17lmmLDxuKnvX0LDhFNf6DuEVRDKGzITmoarqmJYVoRZpb0BbmWD0dhc3rT/FbatOcoc/g0+68Nmy7B8PFRo4nYrgHs+iV5bueowNHXi2yHT98ilaYyrlaj0ffub9BL80TvPTpxD0i98xKRLGt9Mk9kuDNDUI9KdW8PP776DxmSrx/jxLgenzYG5dyy/c9ATXXjOALCp2YNdM1sMX/uKdlI/PIQyPLrtCMIYskFkhUw0tmJLUooKoLpMSg+UKRu8g03/uYsYf5fDa9yJkS5Atok5OX7AQCWaORnmcsT9bh+iq3Y1X4kWVeEbn1E0GKz0QcZf5rXfdz5PSdp6WtpHtkPCvzlG3cZa/6nyQuJKliMl/G7qasWNx/J8X0QbG8eUX0QUtLKR5Wrv+Ss5N39Mricz04zVffw6ljQ2I19Tzdxu/Ql2ggK6JRM6YVMaWc8yAtXvx+ei8LUnH1UX2+NMEpRLjFZF/6r2BsUEXetLanb7BhGd5GmvFlHuZ9CCwAqHMWHDhBcNEzJcxC0VbKFjodUHkjjA/9aFDrF47ZUfhzhomZwbq+M7T68jOWybr2pnN9ZY6gq0C77trP65YEjHsp9Qu0xqdpy1QwCvrF9RROJ5NcP/kGua+I5I7KWJMJ1moeb3IeFzQUMfK22dp25bn9ug4LUqJourCHSkjtgQxO2QYmkR0ue2+EUYma0/elvlz7po4bTvm+UjdAIJYoJAJEegWkQbmYDK3NAYon8D8Hhk5IRGSLFsSnFUjnKpGSMfcVDfEEVoWXEzeRAlfS4HpmQjKiEa4O4+Rv9BvWiuYikm5VUcLmHberZyWEMvLRAxYmCZGWQVLWPZPLTRQsvLVqxepKSDqvK/lCDsTKdt6a5GdD7L/pQ3kMknL8cCSk0yT61f40rPX856uM1zVNsYaN3i3D7GtrkA1KKBEVNyxCt/JdVAeVSgfcZM5qSOMzaKNCJjFKsIbrDuXgmpIprAnRDUs49OrKBkBoXqR2K7OZrZcNc/GW3oJuMt24z7dNHEPJFEmlrMYsApexAO07Zlm040zrHYXmagKDBR8PNi9Ac/ILNHC1CU9BYdzWKoqakWrewh2+Gx/uamZqLMmpXkVNatDRcBs8qN0+bnzhl6iwRKGKXKw4ObUYJxDj65CyPYj1FAhGL0phLJF4tYfn8avTKMg4TqvfoLlBCkaCkVNwfIWnJyMc9/pNbQ+msE1lMfMLFIA0WtFckDBvTbAqr0jbNg0w1XeOfueTOgybdEMwgov8ryPStFE9gtIAZnKlKVbBKqCgrxLpnlDlZv8U5ysmMylRYK9VaTJDKSXyDLgNals1JEiIu5z9yCle5k0Q/haKrjqrdcWor2DKyHaVSIz4IcjBvqUgFApW40mqDWsPgPVOh3Da4kBASUlImoyhk9ZKNJjZ7kIiCV9YYGpkcI8F2CJLMuyOj33hlLeUEQkv8gNDSdZFS7bYiCjuZiaC9H3UhPhXAalFsRANk8RN8/t28TaUIrO+iTtHoOmzml2d07b11c2FLKqhwdGdjN1LIL6oB/hxABKsbwkWxnDI1Jo96B7F0SyZFgxJ6+eialI4FcIdPno2trHLVuPIyluO6bDOkyazyNlre3xMhUD1aib5Ic6WL+mj3d4U3a+97+P7OE/T60m8ae9SPkaNTdfbshWnYEg2U+EWLG7wKfXPGBHqOYMmQdz9TyfXMmp6VXIT4Zo2J5kxe4BJN/CvbHSdv70iXuZfVYndMAyW9fWZJ1aLyLulojIXlzChaZNa+DMGWW+OdPJ54auovHTFczhIu3TPYhl3W6+tBRIdTHqd1W4/n8+wy3htF3Q6eWyD42uIl9c9zjaKp3Cj4ncn2kmLBWIykWOF1s4nmzl6GQbX7ruG6wK5GyXx9+M3MTAcTeu73YvaVW1oKfMhk09NIazdoyJxdXeFHs8aX7mJwYvmMgE0bS/jFaRhzpX8d87rqP1L9O4x2owKM+qmHousMTKyU8c1UjF60l9tB79uhx6VkEf9dD85bNIycJC3YplSGljgvLNrTREjxEXNTRT5r+PXMPAAQ+xLx5BqKGYLSlXoe7zR/n3UidfK27imzd9Db+8IFQKZoX/nFzNp/uupv7/FhCH84hTI0s6NtzJKm33z+DZW8Xo0inflMdz3IAB0RZqpW2teHYG+c1P/ActwTIRCQbUKlFRok50U9rRQUWo4jp8dhmKAUFA8pj41hRwBVU0RPaXoswdlfA+XUIqaMunw98yxmyMYzS5qOySuG3rKJta5/H5qgvq1NTZLadIuAWujcwhKh4CjXki8RxuUae3FGV/phH1uSKuk9WaEwIWUllALly83q71Ss6QKM640I75KA6lEJNFxKK2pLu37F4PDXt0dluLplTmbFXh2fS6C47Z4h+l3TPPXjmNR6jiFVVi/nnWeTWujaZo85UoIXGq7KP4rTIc1O1yzUuF1lZHdLXOneFTNLnyr9wNCcM2SlWVMofn2uhOt7ChcYIVrjyr5Jy9646EijS1pVGU2twcWELA7VWZRqKn6kUo6OzYNMr6PZPIK3TMioTapvDo6VZKZyRcvctMDFhiJxikdWWJhqt6iHitctELYm42EyCdcRFVLRdB7WBpM0E1cLlVPOHKK2Ktqkt88ehujh0PoxyYxTxbwcxUlrz0MJZrZjbNN3pWkvBpfGJFN9oH0uh7SpiGiau9gL/FTVOwTO94Mz3jbZhr0mwLZqjzZKjEJKpBiUtZj/eSigHZo1PfOY8nUKWkSzybaSRzVCD2VAbdEQKLM8jbowib3YjvL3D7ihF2BqepmiKKNcEJBlvcWfuLqFXM48Jft8TAl2fWIr6UwjdWraFIgVdRMgbKnEnJEO3sNMvHJp93piVTRkpLxAbAzOXRKxXEJRQCtkDZ5UHfWWWtUmGwDKfzXj5/di3meel1P9KQJxSbZ627bHkVbNfCSs88gvVltcCwyitX/LyQj6E+lMfVs7T533prHN/KKjcFxnBZWQUI6JjohoxqSsyWdJ4abeKrZ7fwLtPg2tgMTdEyblHF7ymzomGWeUWvyVp+VvGaiL9AWhQYVN14xDK7V57l3muPIWhBZNHAXGnw1NHVZKsirl6WF9bCHw/T2jnJtdv7Cbk1rAx+a4ouzbspp2urc6SFbT33KkSiJerryueJAZn7ju6i/EyG6BMLu+iamLc0DSOd4Vsn19AR0Pi3ux9k7j1FClbirQlRYRa/KVFWJbrPtPOVgztojo0RVM5yjSeD6hfQL3FG9yWbQQRJZoW/xB+uepyAUmUmG+DRB/YiHx/HnU7WyB26jLEKcrhdhH9slg3bs/xC23GCUpVZ3c39uTZu8U2x2vXmvuVM1sfQcD3tZhFZ0pcm0O57EHx6CGFY4G/f00VHYJ5WJcMN3rwtCKw1dJ0i0rG3l/dsGeInN72HzD6VugdHMPWlW3aEYQ8TcYnPtq1k32MbSXa78Xxj4IJjHvM3893wCrI3rURfX8W7NsffrP0WjYqOT1iYnPvT9Xzu9DXUlQdQljiOwz1XgWSFPlWgXbEqVZoMaToPzGxm38Rq3J/3Uu5L0TJyklMy9L1rBV/56Y38RcezNLoy3Bw9zbelOlKXdO/zgxF3Ffjk2m/S7K0QEA3a/uwbtPusrJoAP/nF93HV6j4+ds3z7LqrmyHTzci3WVZYsQJT99Rz9a45bvbN48YgZ5pMWi6RbxWoHnVTa2hRN+O/tJH/eu0BPtjeg1d81YVhyFYwHrWHYVD/9WHyh/3cqX2Eqzf30N4wjSwYvDS4hv6hZloeq1DpSxGZ7Ua881w1RRMiAyqlieXatVCw4gdNorKl2sDUBbSUshCF+xZ2ZtYRg6qPbMVtV5irxSjjmqZJwrjOy541k8SDVR6aWkXgdJVSRua03IzS5WG8KcX1wXF70bwY64JzfKTjFN13eCkNBNDPhBCn5heikWsEsaqjz8DQl2TysQQT4TijXSar66ZZHZ0mYcXluHRccpF7t/Vx0ghzLNmA//gsYt4qdrO4qtTa+/uOW22JDc6MWBHOJdRRFSV14UAv50VKWYPigQzmjATjEsk2Bb9s4jEF+lQv45MeXC9YDZxYelIZ8gPw3EMbCIdkFBGyUzmGkj7yyRLVkwYky0hZDQ2BYsZFuhSw89znyx4OT6ygaIvN2tsliIJpV+ELSiYhUaAzWqZsejhV8JPfn+N02ccDq7pYFZhFrIsw0tgOs6llUSRNbQhgtAe4cXsPXW3jtrWwYuqMTEd55uQKCgMq4mxtWQWtCpe+kMg1mwdYVT9ni5cvDWwkGswR8heJt6coxGurAdnLCEUVYbKI+p0ME71QDXvtrLlUUkNNZsmc0hDnyojnjwUT5KkM0vxyziY4D8uKIxcXAnDe/MAFq5U1ALsrYUpFBSFfQrhYcRyHN0ToFJF+zsM72iZJlz38XO+dNH8pj2/UQG8PMSEFafPOcm1gwv6srSfO+u/L6UQWe+KTbI9N82uhGxk/FsF8KGxH8pp2oZ7auRt6Gib/GqYaGzBaogzfG+aezYe41zNLyGfiFk1cosknthzkYf9anjdvxjtu9V0wMLXFt3YE90/Cfqv4rrXDn7loIRG7Xayq4ttfQDgTxnvaR/IDbmJ+q4iSyYFCmOFBN/XfLqFna0AoJ1Nk8h4e+dxuyg0edAmCR6cw0mnchdnXHV7VJEolD4YpMJaL8WDfVtorJ/FQg5O4lURgisiCgM/6kq3mXj5ezERwPz9Gr5rg8Jar+fz2+3DHFZ5a2YyQK4BW47EDgoC2MgLXJfjwnsfpCJZs+3tWg9ND9XztgevwDpy024bXEoLXQzBmcu+mg6zyVcirbv7y8FWs7Rhn66phWtdNMX/EYNZq6b1EQcJviK4jzZeIf2uIlKWhrZ7lNhninNd90X9ePr1pIk3MIc6d7wB9+1n6QtPnY/l8mxppbc6yLX6Kp2bWkZkQEaana6KRzHJihz/JJ1Ye43+N7KK/N077V3TmNoWQ7szyib3fYE20QoNXtX3sFdPqkqVTMU18ooxfePWxsExYn2rZR7EOcnsM/vJv38nkCZfdSriW+ptbmMk5hFSKliGRvriXv2+5kdV/NMfehiS3+CfsY25qH+Lr7/syf7bvGkasZXh44fVaptwaQNtZx3Y/1MsGhibx3Kd3MnbURB8aWSglXQuUK5in+nH1LiRN6ZYF6U0CtxZEv7BgmarhWiJF08UT+S5ul6eJe1LkjArd+1t59MltCMVevH0SvgdcRNd5mLLSwXwK0kJhv9pFEhHbWth+4wxdH+hmc9DEJ8mousDvPPo+Jp8H7+PdCKUaSCV8DaXVdYS2yVznfZEZE3rnTZr+5hSld7vpa2jgd5sO8/y6Ffzb1duQD51ZsCw7LJ0Y0OoDqI3KWx8Rdni7RH6NH3dHgY2eDI+oLrJlifAyMLfVGh5Rp8VVZINvnnBCxb9thtRGhWBzgfWNaeKStZBLPDa/An0CjDkDPV+GBg9Ko4sbmgbwSrodSJRQSqiyQcmrc93V4/TVB+ltdFMdDGCmTeRsGdPKYV5qwWb9+7qOUoFKUWQuryB/J0h4i0rd1jIb3Bl8SpX2kIqw1USriEjD1DzexjKBzVl8LoO8oTBe9ZAdNKmOaQhqjQgBC0sYVhYyVb7XsI+4SvhDSRTL12sIiFVhSctCvxlWUNrJuWZ2evKYnhR5E4oTKpXuEm7NtIvHSFkB0boOq2NjrasbO69doLpHoW5NmU2xObySwYzqp7cYInlIoNSjEigsXrne7wepQ0PZYOCXDM6mE7wwHUOYrVKc9WPM+4k2V/EFTKr1HiTpfFvn8qNgiEzoEtoi1K+4ZGKgtK6B0iZLkV087et1WCHTbpnZ64MoW+fZ484gVUXKVZnwpTrJy5iF5iMCP9vcjdls2fwXXn/5TsxqGv1lN384uBf3Ex68BwWUkSSz14Yov8PLV245S5NXxSsuZIwrgogiifzMuw9wpJjgzyZ3UflqI0aPgNybRJudx7CKrtTIhG6WSmjlMlN/F0N9Xx1TKz381/hJYlYgpADl2zQqLhHf49Q0VtR0bHWKtptTyC6dAdXLd/MxKsPziMmLNZytfaxrag2k2dwwgUuqYmogFax8a2qSkqrQO9LJ7aFpjMgYKUNEHUnh2T+4cMDL6meZrDq2U9AHxQ9B/coyuzxpLPvMsVKUL06tQ3pihuCoVrPPlndzlsC1hu17fnF6FV/uW8MK7QSZtJfZ0Tr0LhHVJ1JMyHhqMZDw+2BGFzlWUShbqR2XOG7uBxcDkmQHcoheD6aqLXQVPI9Ci0muY+HBe9MxIgqIrc3oVwkIt2v83+3fJqso/MH4VRifM0icqIXoqDfH6rUgSQrm5tWYZ0oIYzNLfUr25268RknO6SozupsXi52cfmIFk8eChJ4eRcyAUFow6wYfm8e/X+aPjr2Xlp1zrL5mjA+EhgmJr/rW13tS/Hnr8+g/KZMrSZzIyjw6sZ7h8WZ8j3mQzkwgzqSpCXSdkiYzV/WjL2zZlg16QGbil1dz494efix+Gp+oMTDYzDee34EyO4hwroT0ckFwuSDkYfoX67h6+xDvDw7waL6FU90+2j43gDRXm+ZcwVp3sjKCZb2wWugKBorPquYZRU+nKa/SKb+nimF1XF8GlLe24Nnq50/XPcTKQNGuUjygCQzv95P5WgItOQRLmG3zvdgUGKcrUlwI7DwjkHhexKo3Fj2cx5sxkG6oUVX5A9CTb6J3voF8KXvJe8L8wGJAsPozSxJYA/wiKHkDl1Xi9mV9KYDmAUm+UBpYddbLXR4athRZtXmOzroZTicT9J9sQO8vI09WanXD8LpduBHyYXr1mtggJMtenppux+8r2TuuYsZLIVMllRfpmfIxtM9NpldCGSxfsJuX0xpmTmX8iETVDKArCZ5fbbIykmFdNGkHGHpFjRVWbYIGyBsiuupmJj5PQwN4Cx5GMmXmL6EeMsJ+uzuhWNGhVHlzn7kiE3OV2OBO25P4skIGaY1IJKHTIJU5Wqinf8RP4bBBqKQjLXW8RtBnd4o0ZQEhXXjDLBO7619TCF+Dib/FoHVHivWtWWJild7pBkbH3bjG8xhqbboDrYXGN2Ei5K068QYBQcDfJuK5SiB3IkyiVaOucxhJrqBbZWbtoLUa3Fdbc7bHTdOqIk07CqzzpwkoBropc2iijcHBENopy8dm1OTpn1/3QT6XSihWBOT8uZK9ORX3ZPkNm3stR1Qku1aHPdRr1k1gRWparYldCuZFzBex41XqlArCRxd+NiQoJwQk74XHmS6Y/aDMreun+a22/UzqVeYGmsh8sQHp7CmEXI1H5J6LwLfEke6R7JzdWth/Hp5r5KEX19C1ZgzKIoMH2omdUvGM5JEOnAam3jCj26o06HlpgHxviNNPt/HoB7Zxx9Y+/vTqx+xqcud74ay8653uEjtbjkELsB3+z3AHjx+vu2TXpq9pgUgAeaaIMTIB6ewbXIgAoQA7YjP8bOI4yw1JNGhOpAkHKmimiz+d2EHmYJXoN3qoCTqaMWMB1KCCsv8MgtV07CKYbpncbeuIXj3Fql2j/FbimG1pUnWFl46uY/5MlbpSH7WKZIXS7FORu6pUNqs0SW4abjaIb6mS+n9r2bP9OD+x5incgkhVb1xoOV2L65EkISXquPn6o1z/3n4i1vwtiORVhX944VbUAxli/bXfV3peCzCtWpH103Y9AatlsY2qYZZrVIj9gASVCkG5zKgsoFqu9EvoKviBxYDlGtD1AkK5bJdTfB3j04wEFD7Reys/3XyCLn+SP7r7KzwY3MiBro24UxBdnaZx/Ry/veu7NPlTzKo6f/ONuxh7yYV0qBeKtW0GtarbfS61gWvlJF28wYK0RHj7c7T8zWk0b8UO0ErksigFc2E3/RYx8wXMkSqJL8xw9mmF//b0u5nvkmlpneXuTYfZoJQJiYu/205tsvyGZW7qOMHJR9qYOdlkF70xZ+bsDozGxpVUYgp6Hey66ywNq1KvuTCQng4gvlRbyTTnI8SjBFe4+MW2F2gLZslbbZa/GoSDteM2W/OjZ4lsMIi4i7zYsIaZsy2vFH0x/CbV7SUsZRx0VfmDq+6nMVYlGtQJiDovFRp5eLYD+eszRPpq0z3wMoIVHHu8j2/2NnFkfZw/aDvINf4Z1rmylH6hj1Agh2K6+KPv3MHY8y6E7n4Ey2JVQ4iBAEqLQuNvz9PWVaFZFm2t/ODsar412onnC2fxjNb2fXiZ/iNtFPwi5r19uLfkCMhJzCdMyu1R9G1RTOUwlwslTaGkyuhW/nTNWgasE9N1zDeKIC+VKc0LHD/dQjrQjyeosaNpnOGNzeS8CVsMxNYWaFibZ1t0imxB5vh4I72Ho+R6VHzppfe7fy9UQ+RItoGVwRJdnmxNBRBZTaB8fVYjmwU8/ACTk5XFoel4hssU8yGOlxLM52VS0yqdZhy1QaMuUKU1PIfHcgMZMj2FGHPV15h/3masVCFB16muMGnaUiHqL1GaVslOCFTyCs07CphxGRICXdtmaQy+Gs9SNiXSmoIxKiDVcMNMqUXCu1FkU2CeedXN0fkY2kkN4RJXIXvLCCaB9iINa0uscc+T3VFPPGFNJyaGLNhioLKzZDcjCskVtq+csJuvuAWFs5UQpycinDwZRhgsoiTV2t7LGSZCpsD0QJRsd5Aj/gaa/FnqLVfZiiypgodTE82csueuKqHM0nSNfDO0uBelQ2H9tkkawhXciMzqLs5OBOnpjhEaHELM1ciz9T0ojchkeySSmkI4nmPNymnORH1oiQBmg992S10u+KUqEYqcFU2qdvW+SzdSLt3WyCqUkDSIfl7CVScgtQo0SG5+YWs3P7+1+5V10243aao8c2IDn3nwRkL3H8OXq50WuW+G1RRj/+gK9gQy4Jm0H0IrBuJyRJjLIs9lqT8IqtfD1xt2MfaeOlo3z/N7N3+N1QrMloN87Mi7iM2NEuLStQZueDCJetrHA53b+PObnuP6943weCnE86lVjJej/Er7N2iWISq+XFP91QE0pnl4shCnMlbGPS3UZjyKKOK/vUDsQwZ+j8K/92zl/x7eRvuhY8i52ikJbRUMCos6t3hT3Hrv0/Zr5+c3WN9ZuSjWiDBYCOsuGiJ/NrWDuQclqn9fwSgU7cV2OeB6qIy6X+Qn/ts7uXtdN3d3HLNff+7Uer753b3Ev3CUULo25670tgjyNQo/X9eDS9IpmRJPFOuZ/K5C/PN59MLyuAcWgaPTuLImT3wizJbACNe2D/GLe38cNeDB8Gt2tsrlwk7/KO8JnOD3fdcwI7swq5fOenNp7aQVFQbG+MYXVvDM4Q7Sd8L7Gvq4NjJuv/3g1CoeHe2k/pspJgdd+AfPINZgkYvvhY6BpleRjwwgjS6fQfWDYj2Q+kySyIMZ9OcNvvroNvySSEVXaJyeRj5zaeM8jFIZccQg8f9cuD9RxbvXYK+nwJq6Acq6Qrss4bEDNxbuRcnUyRgG/zx+NTNHFArfKJE/msbI1N69MmMi/HKMG64a44b4jB30GLCudb/2vat3LiamwIlUE2LKD6GhC8T9y7z8morA0YqP7r4VHO/pYP67Ompf2b6Py0UIWJhVq/+CTsPfDzAW0PmGb6Gz11wGwrO9SIXam7v0oJvS7hVcc8sQm7ZPI4rWbGVSqio8/MIu8r1ljFR6WZV7t+afSgkGyvWs8aqs9Kd578ef4aTaxLgQRVKWz7V8L7yCTMwKtFvZhFE0EUamlqkYsFwI6Sx9R+vR5/zMNcHa1mna4nP2292jEZ4daKDtSQVxtoAr/xrf7jKhaEjMqTLCdAYxW3sdvt52dAOzWMIzUMKUJXoG4gt1IkzwVXIYlgi8pP++jpCt4jtcYnbMy1AmiuzR6FAKBD0v17awdqMCo+UguaJJOmvw4qk6cgcEAk+amFqlpqonWhh+F1KjTPNNKusbs6x3zTOcipIfl/EPlGouSjo1EWByTKQvFsOjaHhknYR8YUxDsuxjtuzl9LSHQycSHDrYQPyFMQSr5m2tVE58q+iGXZEvcKyK5Qjo51wjGdsNV5tzl+mRKG8M02m1w20ds0eFpSkLqsjomXrEqWkCldeXi65pDAOtJDAzEGB+lY9yQ4Etm0cRCwb+soosLQRwXg4GAkkQ8IgyWocfbU5DGbt0QYSXPoLK6ikwPoU4AXX74UHBy0N02W9ZU1uredpOfVvOWu5kNcLX8x22P/qKQ9PRrZ3FYmOamOUyf/3SHipagMTmGX6puZt3RwdfFWm6ws/33kLxYAD/U1ZB+R58xUqtaYBXKO5uJ7FL4a9WfhFRqjJa9PKx/7wX8ckkge7+pa/w+BoiXzUZPpbgPe/+EKtXTLElPsUft+y7YBL+f33bue9UFyv/PQfjs8Rmuu2BX6O34LLDCBgUryuxoqnEVlfF/twnNIGzRah7qURlbJkJsnNoMwKjn3Dz5K/XMfYBhY+HhtntHsJkyM7mstKlrZTQywIJ0vdU7aDJ2GnfOdfa279iLl44tXVz7ElgoW75yyxn9SaWdOr+dZR8oMhJxYtm5bs6LA6mgVGt4Ns3ibtfQXiszFM+H4PujlcOUa0iQ6k5XMksTFplB9WaqZB4MayYk5IkcbQaRhUEUgUfnn4DZq2SvbUnl42ZOeRylnhaQQ8WGXbr/B9/xwVjun++Siw9jDGkQvHCmhYOl5imOhKrTN7f+TRrAzP2zGt9/Ef2r+G5g61oZ6YQUrWfuv2G4z9fYuJBF/nhBGc/2MJtzSNcG53g78a20dvtx//sAGJpeYodUxeYO5KgIGYQ4wb3Nh+hpz7CabluIWX6ElC7uVXLALFqEnwuZcfpT9od6BwWDWtWszIdBjNW+z+bXlz0Er/gMJH8G9ZTqDWEioaWkzg1EqSCTC7rxT1SxsjUVkOoV8gVkHIQXggBsg3lT/Da+hI6QZaZGfoywYyFCDZWuD3RS1ySKBsyc0UfPSeb6X62DXmqx64psiyxNpaqSuaoi9SIh+fXthAvFFjZPMejp1upnC4RPJNkuWLqAqVTLtJBF7P1Cp3qFPmqzunXja+3D0cMODjUCP4XhmAfPPS5hP2ztf6bam9NtYt2WD5UQwpm1KRVUpg2DPqyPn7nWx/F98QUvkMDmMtVCLwmmFCYrtLy33N8V4DnhDUo5ihKjcXXfL+Iqk70ayd48esGB8X1thvd0AV0LXXJfGyOGHBwqBHsMraGVRb+fDPg8p7UHJYOaThJpqLz+T9ppWCazFfcuPsmkM7mFtqPXy6YC1ZaK2DY6pl5oSN6+SLoBoYOlUWqaeuIAQcHB4fLEHlinvwEfP1w4yuvBZhc0nNyqF0E03RskA4ODg4ODlcytdBTx8HBwcHBwWEJccSAg4ODg4PDFY4jBhwcHBwcHK5wHDHg4ODg4OBwheOIAQcHBwcHhyscRww4ODg4ODhc4ThiwMHBwcHB4QrHEQMODg4ODg5XOI4YcHBwcHBwuMJxxICDg4ODg8MVjiMGHBwcHBwcrnAcMeDg4ODg4HCF44gBBwcHBweHKxxHDDg4ODg4OFzhOGLAwcHBwcHhCscRAw4ODg4ODlc4jhhwcHBwcHC4wnHEgIODg4ODwxWOIwYcHBwcHByucBwx4ODg4ODgcIXjiAEHBwcHB4crHEcMODg4ODg4XOE4YsDBwcHBweEKxxEDDg4ODg4OVziOGHBwcHBwcLjCccSAg4ODg4PDFY4jBhwcHBwcHK5wHDHg4ODg4OBwheOIAQcHBwcHhyscRww4ODg4ODhc4ThiwMHBwcHB4QrHEQMODg4ODg5XOI4YcHBwcHBwuMJxxICDg4ODg8MVjiMGHBwcHBwcrnAcMeDg4ODg4HCF44gBBwcHBweHKxxHDDg4ODg4OFzhOGLAwcHBwcHhCscRAw4ODg4ODlc4jhhwcHBwcHC4wnHEgIODg4ODwxWOIwYcHBwcHByucBwx4ODg4ODgcIXjiAEHBwcHB4crHEcMODg4ODg4XOE4YsDBwcHBweEKxxEDDg4ODg4OVziOGHBwcHBwcLjCccSAg4ODg4PDFY4jBhwcHBwcHK5wHDHg4ODg4OBwheOIAQcHBwcHhysc+a0eeJv4QZYLjxtfveyv43K6lsvlOi6na7lcruNyupbL5Toup2u57TK5DgvHMuDg4ODwWgTAA6YiYErONOlw+eM85Q4ODg6vQVgp4/m3OrRPrKV00wZQFBAsheDgcIW7CRyuHExJoLShDiWi4YuWuDowSbLiY3+6Cf/hPGJOA91Y6tN0cLgkCH4f3pjAmhVJ/NUCWsLNU8U6lMEMruniUp+eg8MlwRED3wemtTEQFyyImIBxGV6fAKZbJH9rO8GuIr71ST7ePsXBdCOP9zbj/dQ4UqmIqVeW+nQdHC4JQjRMsF7jmsgxdt5QQtvl5wHXRwg+YDpiwOGyxRED3wez723H3Onj6nV9zN8fJPu0B31yGkxLGSxvTJeMtmstxs4q7u15/nH7Nwj7Skgujahc5ProCF/d9nX+av31jFX8MDi21Kfs4HBJ0MNevHGVmzwFvJLBlKITXJvGFXUEsMPly5KKATXhxWxwc13rKC5JtzfbFtPpAKdG61GmMghVnVpBiygozQrb16c4vUuhUvZQetKHWayCqrKskCWQZYywD6FBxVWvs3XXBPIGFfe6EvHoPDnVy2i2Dl9kFL9UYaVPRfFLmB7XgnVkCTFcEmpLGMNlCRkTJWR9/iYu0eCq4DSycHGzzUQpwLF0At/pLFKpdp4th9rBChgUJYGAaCBg2vOShuDsnBwua5bs+bYGWGFzHeZt9fzqB/cT9xYwzsmBbx9dx6H71iI9eBxpvkAtERSrfCTSz/13mMxtdKP21aGNZzBTaZYTotcDoQDVzW0ot2UI7J7jlzoeISKayILMiarIwzPtfHVkF/+44z9Y5a0QFkR7ETYUEWmRz/dloWiLEMHECHvI376OcgzMqE5w47z9plcu8/9tOIhfurg4+/r4ar59ZBXt/+MU0sjla/I931a11MLth77f5722FNdiGCLZrBexKuPj8uON7JrCEn7mS4FZa9cqmG/iz71MxIDuk0l+ZC13X9PHPTufxu8uoL8iBWDbmkH+9Oen+TvPjcx0J/DvG6IWEDTB/jJMk1uDI6xryfDrt9+D+GyVwIHlJQY2/swMq24cZW/sAFJER/WZPF1cwZm+NgZONeH5xhmyWYV6fYD/9Xs3ceOaSX6hpZu5TTLpvET82OKdq/VczL9nFcZKF972HL/ecoyN4UG0WD+mDKZsInmtXb4lZEw8ovaGf+uGxBj/efU3+Tt/ggkULjfEYJBqk4+ZW+qR60vIpoF7vw/X8XGU8dSinYfud5G/ZS2u9QXk1tKrb2gC2uMRqLx5IlOuXUCIa0Q6FsaVUZLJHY8SeHEMz8DiXYeFXISWh02E2piGfmgEWQZJQnC7Se8Kk9kRukARSOEqsqLjUjT0ngDKQInQMyOgXz6WNDEUtD8HU9UwSiXUkMTMj6wm3JTHaxQQ/3gWYRGNvUIoiBjwo0cD6LuruHaV+P3W/QSlygVizbJ3/tHQ1YxMxJCOe/G9eBZ5rrA8xYBa58Fs9rBhd5pNa2bZmJhBEmTM8zRZxFdiY1se13oRI63APmoC92QJ91mBsmEQlksoftiyaYbkmQK1Zb/43nhbdCIbyqzzz6Bhkq7IDB5u4czpIIOnvESP+BDL1oRQYjDZzurGhQldVE0EbfFiJKQouFcL1O8p4F5ZwtOap6u5wAZfzloy7GPKmsyxmQakEohlg+PpwAVxHFrYjV7nYWtimpirTCRSwStF4HISA4KAoMi41kv4Vxs070kjJ8pIpoEklJmdqpAfX8TzcQmo6z20b0vTsiLzysumKqBnBai8+e4m1yYgxDQi7Qu/m897eKHUivfk4u9fBN3EPQt6aRnHBimyHRdUbnLhDRr4fTqrglly2yG7zbDFgGp9YaIHdTwujaBShZBG2WMy1x2ETB60NxbaNYEd5C2hBxW0iAtfpIwk6cjihW7DUKSCLKtUVZOZpAfN56JhRwVBlxBmvQh2Gumlv9+mLFJtClDXBpGGIpG4hrFDxbW9xPqWPAGpet422ZrWBDbHs0SaRQxfmakplz2U5NkffgVa9JGVvboJ5R1B/vSOfybuMvAKr5+QFUEiLMoo2600Nhe1QvSJceL9JaY+rtMkCYTdZf72jm/yxeMt3Eczy4mC7iZzTunP6TCSEjn2O/WUkkXixZ5XDzShOutBy7jt7+uOVsieXLw0Cu92gda/kPm1uudY6cpf9Jjpgp+fefRdeAfAP1xBeaYbQXt1F5Pd20zhnjYefP99tIdeXZguJwRFQYpGCP9CiQ075vjN+qP265bFLb+tyj/0rObpow2Ldj6mx0Dfk+fdq07xwfreC94TV4u2J37BG//W6MvFeGq0E82/jBfkJbYYac0hJn6ikZWrpmlpmeJ3m47gE3UEBAwMUrrJtGEwq/sJihptUoHQGhdHmjv4w+N3IhztQ7AEQa1iLeCiiOjz2S7o9I2NrL3hLKFggZBSvuDQrf5hfGKVeT3AN4/spFL089Grv8uRf1jL0ON1mIukefSAi/kPdrHr+sNcu7GX23xzKOfcAyKiLQQuGCeCyX/r2Eel3SC9W+W/8xF697cQ/taJ5SMGtKDC/LtX8a6bznDDtmHqFAGX8Krn2bpgzRT4WmYVCSt63T/G+xsOcXJFA8+s64KRSShdeEOXgrzu5pvpbdwmTrDdO7fwYiKKuX4FQv8onLcI1TLD/6ww850YJ27oIhcWKAgiqa4oUu808uB5Zt3zx9kSeNTyqoeedIzD/iFOlDx8Z3YDeo8faVIg2JfHmJunlFWJTfcgFUEqGedqIAi26s7fuIprb5jh3tseIeErcDBXzxem1jJeKdZ8bqjhkZh/12p0rwimTt3XBl4X9GhNE/kbV+JdA027knxg6wBrIguCZ1bXGS77+czA7WRS1vUuUjR8Yx2xNRI/u+pxtr1N4ksQTSS3bv//pWZ+J8zurTUH8g+G4XejblnBLXceZ92mbmIdLny+Kh53hWldY7oaYqIc5YVnN6GPgzxeRp1M492iEfuISZOrwGwqiDw+j1FVa3acpN+5Gk97mVhrjl9rOY4nIqDFXHadFFnWXxdQHJRKSIKJahYZaElyfMjgxd9qJXOmgjk5DsYiPGd3NeLfJfMnt36dtniWOk8JybLSGjCjSXxxci/ZqhvDEHhX83GmMxGeG1jPx7Y+y4pAhgZJQNlUQigp8C2WhxjQIm5odtNxlcrW9bPsbhrDLSi2Ij1fDKgGnO4JUAgZXLXeYJ1vllKdlyfWBlHmZxEqVSuah6VE1WR6JhLscGXQPLNIgoCvySS+xSA95cLIq7VvSrMWkDMi2XGFAVecSkxE90DQNC9akjISKBLwLI0Q07JQPiXSP+dFM0UOzETQjwcQx0TCp2XMpI5ZKOLhwkVHCysYCTcdu3S2bMxwTesoeUNkdM7NwSNRvKUKUi2LAUlC9LuI7jRxBXUUQ6PymBtTszJXFp4vwy1jhNx4t8jUbaywdm+GDZE0DXKRMdXPSEqmby7EkcMJwnPT+BdJDJhBH+6YwPbQGHUua4wviP6coZDXFUx7ylvY73jFKoYpUDXlcxP2wiQckSooGIiC9R5UdBAr8AZJIm8rer2B1lTDz8ZbRRSRAjLKVpmW3Vk61yWRszGEkoCRV+grxBkv+BnJhji4P444KhAYr1I13CSaS/iMAn0VF5mCCyGdr8mNjh50Yda5ad1dJbiqTF17iRVNOURVJFv0QVlAUiWkkoAaElHcGmFfkYio2s9bxTQJqlXkeYPpFzz2XMIlFj3WJkWLeanbAs1XFblqxTBe0cQ0Zc6kEqQzOskMHB6Kkq56MHSBlbNBJlMhDvTG2dSYQBCxrZx1DVliTQKa3wvlyg9VDO7SiwFRIntTB/GrZf7lPV/AK5lIwutN/6ppkK/qjP6GirRDY/5/a7TLEhPtMPGj0Dzlw1MyMfJLa6aqFlz0P7CaqXtGyMdVwrjouuksxrZZvjZ2PYXBIuZUkuWAWKgSfPAEwZc1mfn6zZAoGty0+SRbO+cv8F0tFvKxEv5fHeVJovbPfkZfOdc3e+wzNyao3lvHfTf+Jwl3yQ4hOFRRGHyxiv83R98wULdWEMMh/O1u3nvnd9gSydNW1fgvX30Xqf4S5uSMfUy1PULhltXc9b4X2d48xT2BKUwMhqoBPj23iZ7HVpE57Kbp/u5F2em8jOaX0SISPhHk8x6ow6U6nik22dY1SwBYbPaPUdTdDFfixJUCsrCw4NwTGKBFKeBBZko3GSsJuPo9iNlLn8cSCJQJBpd/TQHLXO5fIbDu57qpC6SYy/v55Fc+BHkJpWAQ7isjDE/B6BQBTi8EFnrdjP9eJ5s3D/PJhgP85siNDKY8mLPnrKA1RmFXC+5rg/zVhz5HndtAEVx8s1DHc2fW8p0jW+1j/OMq8WMVxu7w0Lhmjnu2H+bewCQxqciwViXznSCV58Io6bHFCBOwhcDsR7fwP259jDvWn8EURCqmznRJ5ie//T48z+SIvjCHkEoSObf5XZj/BJqFE/yT/2r27pzl+t2PcFfDcTo6G/j6xhugbxhS2RoVA7KEFI3yI7t62HJdCo8E4nmTw7DqZUjzcbZUx2zZy1TGS0qtUsgG+MuBdv6/jqOsD8zzFxue5r5tm5gw6nAfWloxIJaq+J7pp3t9AGNdFx+JDtIsV1E8RR7wKXaQznLilWqKFyMSRGwOsyf8PKvcacqmhmkpz0VcWOxztP+5hQfne1luda9E8sNt3Hz1DLdvOU1YqTBriAyXJR7/nw2MHfXWtBAwPAqVbe2s3TvNmi0T7Allicsqgvqqk8YUBPQNbay+Kss1736YLY0p6j0VOyf+yUIzp8fjjH2zDXN/Gu9I2b5fi2nxzreJuNeK+Ky4n/Merqn+OrqPrMG1bxTO1Q8pJzrQNJN8VmfWG0I8N0GM72hGaQaps4J60kVhQCb8cD/K+ELQ6CXB7UJoqOODbX1sapxb/l4CWaKgeXnxdD1TI434xisEnxq0ogQRrSDgnA6Fsp2qZl2rXheENQl+e8t+NrYla/r6TbdCdXM7N7xjmA3XTnFCr2PkdDMj/U3kXswwOwWxqT5EjwezQyF/r5uf334QIaBzNNvC0LFOXDMm0pk8E/s0pNHFEQL6ikYSG0x+9q6H2NQyg2ilSQOPntrEM6faCNw3jDxRRc8u7PJfvQfn3Q3FRJAXTna1UqYQqzB9tZvwnIjnh0i0uXQrlx3IIeNZJbNjzTTXrpiwX56v+MhrCnXeHGN5P0dTdfTM1TNVDjOZDxNV+0mX/PROtPNfmntYH5zjrsQgjzRtZbjOj5ulRVANXGfnmJpzI5YbMMwhQqJuR6YaQRndvfg5+JcKI+iB9igd3hx+ocJQOUi1pCFUrWWnhrDGiawghXXkBoH4jTLb12V4Z9NZ++2KJpHWRIZPB5lLutGbz90hQ8BQRaRMFVFbQrOwFZAhy+hBK51OoWGXwqrrCnRtTdLmtlKLTLKGtOCzNQwERSLYJbNiS4EbNp6hWZKxnrqU7uL4dB09/QmyL3gQziRxpXKLfzn1KlKrgUuUkATdtgLkTZn5CT/JQ2ECj4/YWR/WfSsmgpiahpnJkvO64FyHwN5SC9UVLijkcO/3oPRpeE71Xlohp8gIkTBbo3PsCFrzVe0EL/9AWMG/JZGJkwEqJ7z4hsp4Tg1cvGKq9QzGFdjg5R0tIzSEs1RNAT0nYxSkmhMGpkuksjZG5/rjXNV5locmuzja08Lp/c3UPWZgVlXcUp5IIo/ud1PcHmBNLEm+rDA51MGJIwnMYYnY0RzG5Ayi5R5YBIz2AP4NZW5d14tflNBMhYmql6P9jex7qZXY0ZOI6sVtsKYiYXoVEoE8Me/Cpthyd4TcKuWETNB2ydWgGBAjYfxdIuv+eoJQ7NWAtM/27eChiU7+8Jr7efGF1Tz9+BYCL42ih92EG72IeREhIyOe9sEmEYIsC0wJ5rs8lFNlQn1cFhSaZApXewn5XJzINvJH/dcSPTWMbO02awlLCKxso+7H0rTeMc8nG7+BT3o1bqNF0qkPmjz/dxJGPsRgNm6/rmZcFAfC1P/rKTzDP7h57YfGpSC1tTD3QTex3SU+t+OruGUdQViI4TirKfSWZSqDo1AI4q6P8JH/8iTrO/KslBeqQQ6qfu7PtXL482tIH5IRj51edAuOhSDJbF41RueWMuI5B3/FFHkw38RIn0H0hQn06jnhZYI+86pLzTzPBRh+ehieETCKhVf6gFxyi46w0K7YJ7rwX8SVudwwMhmkbIb23skFF6D1+b1B6XRrB61squL5kQnEgE7OhDHVJPV4nPJBBS8LrqlaQfPCzHUG7haJeDXIo5+/Dm3/DPFjCy6x/O5Gcu9s5+53PkEkVLC1zqe/fAvpQ+B/rJeQMbvgarQ+j0UcJpWbS6jXVQiJLjvebKLi56d67kB8dJ74gycQ3mDMCrKC1hqjsqud39j9VTa2zdrZBrOGTjqrUX/QwPVDlrq5ZGKg/A4Xkd0ad0VHaXYVma96+OzgDk487sV9YpaHslcxd0BBOTyOMZeDXBkplYeqtlBh6RJVWXq7SFe8CAX5lflWlEyat04xP2LlhV4eBOMFohumSckahV6NwH8UEa08xBoyC+S3JwiscfFzNxwn0FUiECvbQsCKFL5g443JXdFxrg7MkY0txB0YVQm12cV0OMvEuIenezYSODi9OM1oXIotmFObPYTbK3xo+wGktW6CjTpuWbXNh1VT4qVyHadP1dN7rI5qKU95jUL+HUFWxDWaXQslmCd1gf4pP0efW0v+WBFzpPKGk8qlxPC7qGxqZVvrKXYHJs8LRrUKQVVR29yk9sQJ7VMRckXMN8sOKi347BezpoUVgGWOTfLVkXZOtkT4eEOP7Xo5P9D5DRFEBI8bPC7we5nb6kKqmEROVjDnUpc8KO0NT8v6+PQ3/wytiqKpW5vYtWuGu+rPEJIr9E41cF/3BvL7ciiDNZhFYK3hhnVnBNtkbm4tUpYDqI1tXL/tJKH2DN6Vg0wIYUZOxjGfl9BemkeZ0Gw3yVLZNkt5N8Xcwr9uWfystUI1RRTrWt5ozAqgdTawZmeRd7z3WTbV5YhJCyG4Dx/cwfHuGMqxIYRMsQbFgCRi7BDx7jXY7Zu1h9JIKcRXT68jtH/SrtZ3yL8Rd+80nrNTmFaQRKGEeM7focgaIW8RSbSyX8GeDyy/Zw2tsoW8G2HOg7nynC9bNKlbkaJaB0u4x/z+sPyzkrSwW7AexHPBKvbH7VeIJio0rZhi2pRJjQkEnsyj54yaabOsB2TkrX5iV4vcfdcggmigmgLzJS9WHJr9ZU3C58bYSm+JFVLZikLEFK0+BjqRlgqHVrnoTiZ46ZkQ0vAcTF/aczc8MnLIhb/TQ/G6ALEujXfsPUNcUvAKEhoGWd1FqurmwESU08daGHymGU+gB2m9gHizSVNQJ3pufAyV3fTPhBg+2Ip3+CSyJa4XG0FA9Cu4twdY21Bkm3f2gscsLFbwt5q4d0r4plyoSY1ySkO0jIa6VcjqNSmTS5GRY2VpJOd5brSNyfYoH2/otYWkVYBGdwsYkvCqC9Du8CnYGR2mYhV8EqiLiYgBGTPmpXiTFz0vYGQtv3x+4TmsVWSB4p4QTRvGuTU0TErz0D+V4Ol9XURPW89T7ZXttkSOWLYqwoIom8TXpZE8IYS2ABvvylHvzRIwK/xD33XMdQcwvqFgTkziWuL7oKdk1FmDqmladbkQBIOoUqLqNjG8CqJlNTPPxWVJom2psrrI+td5WbNjlvded9z+O1VDYqbqYV/3Knr2+wmfPa82TK2IAcHtQozHWV+fZm301WgGJa3T+elZqpMlKFUJffPYgmK9iBrqah/h5vcfpD6QI2sInFFFtFkdz1ztpOz5npcJpd0IVsDq8ooZtLFKkQo+D8bqVsRsCSFXQp+csoWB6ZZI/thmbriuh19sfIlf7H8HM6OgpOZqpkNjtdnD6G+t5892Ps/drWeRRZ0eVeFkPsBfP/pulCGJwHAV5flTC7sySST9vs1U6z1UIlYlTI3rmsb4zI4n2OZS2dw8yQc/8CX+/LFW9p8IXdJzz926lqYdJe593zPsCeSpV6p4ZBcSIla7rrRR4bMjO3i4fx2J35/ESGXxiCn8fx/m5s5RPtjwJGHJfKUi2V8fv4PBMwnCeQNxibK/xHCYhnVVPvzLX2G9/8I6FS7B4EZvmmu376O05SAPv7uB/ck2nh1fS/19MopVXrjvXJZIDeB/zEVgTsDYviB8qx6YvMZFcFYieO40BbeVi+sjfedKqp0GrC7xd1sfptXy5VoiQRZ4dqaVX4nfRutnPXhLKmYtNjOTJGSPwppN4zS0pFBNiV8+cyvJF13U39+HmanNrAqxKhA5LeFaLRBeXeGf1z4BqwUMwyQt5vnWzAY+N3gViT+ZRRzNYli75iWwlr2Wxu8WiaernLxZotNlUK/k+czar/NPN9/IY66txF6chkwO09ocJ2IUO0MUtwT43Y//B6viVvr0wubzULaR3+6/icBDScIn3h4Xztu+jBkuGaMxzI7IEJv9C1usEc3DQNkL8wWEslXwciEQ743wSSqtngyKaJJUfRzIN5EfyyNO1NCDmcxS6q/yD4PbuL55gi2R2YVdBDWOFSQVj1LcpODtMPjwxm5mSwLTOYkXjzajnC3jnS0TXp8n2FjEjUZpMEhlQkcxX93tLTViRSB0ViK4UaeqS/zL8a1kTuVID1btwDkzLVBJ66gZy0Ig2V4n48AsgmVN8FhpVwaTbfD/srt597YemiM5XK4q8iUsamMEveitMd55Qz+rNqbYEs6RkCv47FKpAmeqQSayfs48neBsXxBpKIU2p1FuDmCs9fCRjj42RZOEJN3Ov7d+x4oreF/LGWY8o+gRg/3FBlL9HqSRxU1vtUqq0lZltb9EUNJfH98pmMiyhiLpbI+kqXOZrA8UOHp7K9l9KoVBqWZq39s9SM6t25YpVpAt33QRMaBiuEXSt9XTkCjTUpfnnq3HEGM6xFUawiW88qsblg2ROX5l8yGeuL6BuYib4IuT1Br6FjfCHg/3tAzS7M1yqhxAe6iKeESDfOmHylu/1DFalZiJ7l4w+ftE1bbIzqkSTz24hZ6hKK6zKcyxImZeqwkhYCHMZkiPizwwsZUP1w+xOTBPRDa4qWuI1mAJ76Y8J+ZD9GYD9j0R6ySqDS5WREuE3FYtAoGXSg0cHUsgf0dGmNagotagGLBc/R4ZtTXAlkiGXbap0GSw7Od4PoReKNmNId78bwi4RZ24VETGKpnr5WB+BfnxPMJU7VgGhFSOolHin4a24vXD9sgcHkFDliU76hPVKvNZewhuGXllDPMd4N2W40NbjtKj+jhZiPJC02Z4roB8PE3T6jl8dUXymoze58YYr61djVQyiRzXMXYJTIW8/MNL2/A/PELw0AwBFjJXXmtW9J6YevUFUSC7Isy/ubdyVecYTeGc3ab2Uk59ZtiDsbWJd177PF0d07gF2a66aQXYWRH3J4oRjk7Vs/+hbQj944SmkgiiiLnOh3hLlHfVP0nUU+T80lDWM/bhttMUWw0mNmoMDr2XNGEYtQKkFm8CrDQGMFpVWmVLqJxXS/3cf6zP9uUz3uTKsdGVRQ2P8Tc+gYGql/y33QiFc0VTasT69DKCZOJtLCJGdPSoTOXdcYIrpljdmOHjsQH8gnrRGhwd/gy/tPYg+69/D0PuUE2KAWmLjO9DLu5qGGJKV3gpG0N+tIB7SMCs1NDm6zxMt4AZFJCbqoj+BQFpxQ7MaRI9eQ+PPLCFQl+B8OjCeK+lp8nM5EhPeXhoYBO7XDlWufJW/DPXrBnhurUj9jHy/BpmMiHe295PyG5UtHAFhimiIvDifCN9g3WEntQxLGv521SI720VA2IohL7GR/6jebQmq/2NFQSl8+S+zTx9oJ1A5dSbBzZZLuymBuS4inJul52vuumdqyeoWhXJakcMnI8VkSoKVX4svo/HN2/mK3dfg//xkwuTW43Vr/evMNjwZ6e5vW6C9f60XYVrvavAaqXEbe/6Eo/tbeP5dBOfXHOY/fkmfv7MbahfGyE4VltiQMiXUJ45zj8e9CFLXhKVYwhWmbq3iNjSxKpdeW756a/REs6RMUUOVlzM6m/eTe+HIdCZo+7newk3qK+U4n66VMdgNchAqZ4z+9qZHo1S3KEQvTdCqF7ALWl8qL6Hu+tHiLjf+HnyCSKrFDe3f/QIrV1hnj/SgpEvLJppOt8mkFsh2CWrzy9bXTE1Cia8WKpHR7QtBHd4Myh2QIfJL8ZP8NC1q/jU715P+1czuEZyGNO1VbTLanLTFMgy9pEY1Xti/Ns191Hv1glIAr63EMjktxrOtNbi1gDeF+/n3tUzdne8x2c7+MeBnUSyw0g1KgQs8j9VT907RL6w4z4irvJCEKEg8PjQZv7x2CbiRwcQM1VqFWW0TNOvnuEzd2/lP3bv4Bfufph17hLN8sJYvTtylltDIwRf04Y9acj0VLz0/XmC6W4XxlD/22pNe1vFQLU5RGylye0remj0FuyQgLxhoPXm4VDme0a1WsVUshsDVFb48Qky3eU6esbDuL+jIaZrSd+9Fivi2CQh6/g9oIUUzEu3pvzADTyKu0L4tmnsbRyiIggcy9cx3RdDDYASrPLeljNsis/iCugklBJGTmZqMEYom0Ks1JYQszefVY1ideGDlqwQ4bf8yxC8rkzjVUU2hfP4Zd2uzvb0sbXMWYXBLxGibKD4Vaso5ysLZotsiVyBKAIrO3QK4Wk0j4k/UcQTKaOIOlt988Q9Zft3Xh4FRVNjTvMyUo0xdzyGPqsjTWfoqXqYHhcxq9WFwNxFwj9UQI/BtzMd9uOm6yLzqQDivIGRNhgaqNr+XFEWSN7Ywsr4HOui48RFlfWxOX5k62lSeYnsCZh+xLuwK12i0uOCqqOrOnOGTkgU8Isqd4WHmXf5LBMH7d4yfsnqeSnZ7pqF7DSBAdVL0ZApGy42edL4rOg2O9PIQJCW4Fpk2Q4ELq6JoHtM27R+fmXR+niG8CYISRoPpzo5froO+RkVwQr5sG5iLVloLDdfWKZ8XZBdu1Ks78wiuXIcmGlhNBenpXme3kqErOkmqmmINeresLB0sJTRqHQXyGZlDszGGOzwEEx47AyUtrYZ2luTRGxhbTUbs/qMwNk+DweejJE5qaJN6297eei3VQyUV0RIrMnw662H7J+tus9pw0Q+MUPwxbfwYImQ2uOnst5HSFR4ptDCmYEo4a+U0GeXohjuW8dSp2FRwqNIaJ4aCx6wIr1dLoq3hpGuLnKLf4Ivp9awb7KFIw93kW8zCbYXeGfTAJu9c/aXlcOqpVyUzoQIVAXEWpoYfggskWZHft+ToX1Llo0uFVWXmJkP8cgTuwnOnMFF6tL82+clqr2crtblytlf+JKIO4YWJj0rmviiCAumQhNSVZWespcnsu2cfnA95eMqysHzC1wsbgR46HgWo+ri39613r6GSlXm9JlWQj0CwQEV95NWJ0nDrhD5Jc8mbt50ho95pwl4VTaGZ+yvBzraOP1CjJkXWzEt8+fL9QgWGbGio5c0xiqwwm31SbCsfud3XlwQoK9m7VnuJZPDpRBTmp+U7qddKeA7L35gKRC8CmZjgPyd7agRA827MIatJ8+laKzpquKNWa4qhX+f2UjhKDR8M01VEzHcEqaqL2R6sPSYkoTR6Kb8swlu6zzDDZEhBsvwtb5Onh7ZwN7rTzOsB5E9rzqkah3fiSScgOceTJC/bgXlrkasxqy33HyI2xtTNFkuN6ulNAajmszxo36e+ZP6Sza2ayYO3qqLLfld7NwyyNrOaVuU9qbrOTvrRpmeAaM2gouWI1afeyEW5Q82vMSW9Uk76Gz88RYGX2gn9EQ34ViIaIeIeK15wRNh7SQMq+TjchldbwHNEps/G+fjW19kQ2ihSscnn3kHxw6Fidx3DLF46cyL6bKXielG8pHTGK6LiauFvvIXk73WsjiulXlspIuvnNxD05cnMOZUimWrcM8AslXRbwkxMlmMowLajy80uLACndqrKUQrp1u1UgcXzk8s67T95WlGVob5u83v5+d/6SFW1uVolk1u90/i2yhz32800fjZAp6BpXFNmaMTjGlefudffpzfvv05blwzdNHjrKX+6VKQsFiiUchz/8PXMOXyIawu8qOBERrl13f/XEyqvxAgvkfnp1b9p90ryjwXyxESKwRFlZU+07YKWCm2/7vzOxg/Cfr7Bf5wdC/Dk02k+iMkvtyDklza67CsmrPvX0nLbo1/3vQQWaHMS1MRPv/Xt5M+lSWW7eGlpg72tp3lfVsP8rCSoLzM6sD6u2fwTBSYfn8nnliVFjmHaYdvm1QNkb86fTszIyaeS1j8qWbEQKXRjb4hxJ0Ng6zzz9srUGnWR2lOQamRKOPliu6WqbYFiYeqBJUqLxUamTkLek8BVwVc9VUC6+0x98qe1fpvPJxnfecoxagb3aoPUwMtpH9QrGyCwtYIjTsNtnRN0u4v4hI1xjWJ5DGN7OEKkeylvT5hHsSXBJ6jg4mGEA3xNB5RtYMHk9UgxbwXdIGOxDSNcpnIeT5DXRPZf3gVp3vjpI6pyD0iUk5ciAlQVYTFtNzIkp1aV7bGrFdEd4FvMIeUVTEnXhUlim3gvBBrPVLmqnYFuUzMR1mzgqIW9GZA1IgFyjSvzqBY3bOsxjlLVG9Az6gUjwoMbvfT1BFijSv3BoVqrAJXBm5RZFfjNBnFhRQqE3iNv3cpENIutBGDmRFrYNs5XPbr8x0R/M0CO4PDFE2ZGdVjVeam4pXJKR4Ko0HK7gDVBjeGvLT+Tt2vUF0ZZcf2FGs3ZnEreYZON9DTE2f2uA7zom2NXRGYw++qUhRctvVtuWH4XUgJF7dt6GNj/RxBu0eHyXA5RHeujvl9Lqo9VTyX8BxqRgwUN/jJfayBu1ueod2XRbeirIf8qGM1c4oXZxlYz63iPJktMbvd57zm43OzG0n3lFBOjyHGo/hu0wi/u4hVgVUwF0zZoiDS2TzDXcEyj67YRa7iQZ+4+GJpZ7nVrotuodqXIjL34Tb2bhvlDzoO2JanGV3iSMmF9N0JIvt9l/w8XCM63n+v8A+5XYS6ity0+wQJJUvVkHkhtYqRs/VQEfnotc9xk3/6FTFgnX+5qnDffTeR7c5Rb9WXX8KP3KolIjfEKNxUT6nRRTlq0vyvA/h60285TsEqrqJkVTug+PypO+ItsrfjLP0hP3m3G2OJ2oFLeZXIi+Mcu8VLcX0jndH8uQ6M51W2xCQu5YmJAglZ5jdvev6if8u+wiWoqCq94KZwQOaRoy0XvD77/hW4bg3ynuZ/Z1j30l0OWc2imVMDjJeijHfXkzddaHEDLmGq7VtBq/OSvnsVH7/hW2xpG+VoReSpb6/n5FPNCCd7ERoTeNvi3LliP6NClIPJlahmdnlMzOdRWRXFvyfIf7/pC/jdlnVStjdkL2Wa+d+DO4nfN0tkrHRJx/zSr7SigNjUyDs3THLXnudo8BQYUv28UIwjP50h2l27Ks8qZtfydYOQdYorqVmsXVhuhYnqNanMK4x9cSXi0DBGsMTEj7bxs7sP8c66Xvo1eHJwMy+OruWDu/ex0pvljtA0uU/2cOpUE0ef2XJBsKexuYAYUfEqKsrfzyMdq03LQW5PjPzdDXzm1u+yLpayhcDRqotT+/08/KkGMsOLU4feLJfRJiap+48kksfgpM+Krg/aJvWSPk9gZYj4Gol7bjtLnWLtqk000+DLw5v5ak8X2nPDSLNLH+W9dWeSn/6j41QDEsfK9Xxq4DpMsfJ9VQ0s18nkNvtZ4YOmcwWULFIVH89NduLJTCOWltA8revo2Rz9j7QwNrWKyQ9EuCc2zFW+V8tTWoboDYqI9KatP6E44acwEVj8NisDY+eU+jlcCqzt4N17e9l8zQhnDJF9Xwyy7z/i5+IfJFSzSjBzGp8ldWRQZpZuTIsBP+0ryvzIvd+iJTHN5GyIv/3SHZSenEfoPWsHOJaavIjr/dwRnOUrE/UcH2ijXuvBquO5HDA9LrQdq3nf+7u5/sYBPMrCBsAqUfxUKcGZY1HC33TDVBGjfGnvxdubWlg1MasGGQP8b2UN97gQA25a9xZYvT7NhuA8LkEmM+Oh50Q92nAF2YpGpjYRdBPXYBZ5vjYXwZcRFQNPfRnRpaNXRQoFD8FON4FVJVq3TdCcSCPqKicPxDnTH2RoTKE70IzR7MLflKSzI4UiyriqgVf/qCAw3CmSVyTUmSCmWGNFmF0KgkfBu6FM7KoKoe2zbIrPEXKXbIvAqf0+ep/3M3v6UhreXoOVVltVUZILA764UI7n3JsaZsK0XTV1UhWvaO38rW5/kBwWGNsnUT9fQiovvcvMF1BZuSaDNTKreZMtuVFSa0WKWgipcOHAl4uGfc6WBUDzSegBiUhjlujaPP6NIwQ9ml2W1VqMCoZALi9TGvCjFATEJcomeAVdpzqkURVN+tfVM7NxmmKbYadxWlg7N59oZRMs1Jh/LWnNw+lSlNKQjGt08a9FKF8Y/2IJ0IQ113amWO1Pc6C7jeETftI9ynlHmUiUasLjXur0I6/Ns7U+yWgyyujZKDOHJFyTGnK5iuD1EGsvUrehQsZ0UZ6WEU6fC+ZYBhhhH0Kdl8BOjdbVOVYl5hHFhflA1wV6epuYOu1D6s1B2epYai4fMeDO6GgZgz4V1irg/l6CoC6KvCbGOz75GF3xLB5h4aFMnQhy7M/XoA8O281DahZNR+gdhtkL20XVmi3D7VVp7kzi9lXt56nQJBK728OKDpNPNn6dYc3PSzNhvvY726jMlIlXB3lqdjsD1ybpv6Wfn4mc5tZ1kwjrj78yCVppbv+YWsexs810P9GFkqytrmZCKIjcFqH5f05wc/Mo7w8vFPSwYgQs18Ajf9hI8tQiCoG3QKlBJN8u4RZlJAw002TM6tu1b46mf+ynFmn1zfOr657gL3/knZwaayN4Rrqgu2BwsIQyUbYXpmpngNJ6D10fOM7W6Fmu8p8lcp5PekwXSE5JBB71Is3XRm6u3D+JOe4mVd7A9MddTDWrrJDddj7BQrOZN8YSAr81eB3BZ0wSx3MXiaBYXLyRCtf9yhE2B1MkCi6++ekbKPXOIVzqZhw/ANbnOvOuGM17ZHa7S/zy87dx4KUYUSsrxXrTCjivT7Dzun7W3DPJQ+Vmxg+7aflGBr1Sq9vHC9FWNyFvCbDyY6eoi+XxnRMC1uVpqsRzX95O7mgO5dRCK/ZLzdtrGegdZsyj8Bdn7uB3VhxiczBJQhIp3tHGdKiV+i+fRdB1DL9E6tdaWNWao6vxFHtjWaJyBWvT83sv3cbYAQVjdGLJOn39MGxee5afDWR57MEIhWwt6GvwyxVWhiYJyBVaIyX+/t6v4QmoqAr8e2obE0/WMbsvSGV6HsOqJ2AY+J7uJX9U5fiXFX73tjsJrqlSt3nO/nulpJfZQwnyxyuUJzR8PaeQfsiOWW8XVuOY3M1rePc1Z7lnz3E87RUi7qptdvvjkd1M7zNRv5QmPVQ7LWpNvxt180p+7s4errpqApcIBVNjpqDw2f94F7OHrc+2xiwv5/ALJhsUjU91PU9xlQdp94VSuFBUKZc1TMPE51PwB2TCiYL9TAZF0Z6AKqbVmMngXx+/g6GDfpQXexCytfE82VSqiMf6efZvInTfv5fMe9tJtGVI1KftJkzJXICJ+QgNdRmqZYX52QWHgDohEfiuhHBsBn2Jm/3krm5F2iGyy3ecZ1KrOTLSSvX0OMJcbVo1rYynD6/Zz+ZVC22tvXM6vhndFgLzd7QgbvVw+7YzXL06SRtV/u3JLVSP5ZCszLMarjFgoYc8FK/t5CfuOcnuLZN4EwXq7MwTgbRR5TuHN/Hk/o1Unh5FWkSr89sqBqxmN+UpganuJobCYeo8RerlLG2rK3TqZYonXJiagRSAjj0F1jbMsz48Y/duny37mMgH6T4SpnBGI1qcXbKiI98vZVMgY4j4rQ5UwRzrWss87Y9RUOSFTmhLTUlAPCswIoQxwhKN9SlyJR9z6QC9ffXMHQlROKq8YoqyjdezebRZyJwVGQsH8aZU6s6ZHS0xkDwQIngqhTxXQU5d+oWq2uADr4DoPbe/0gXMzIWPrydUwRc1aNlTYtOONJs3L1Symyn7OJxp4NiRIOkDBp79tVWdzCpfrTaHWNGcZ3P9jJ1aOFoIcXo2xMChMOa4zvmG3KUmo7k5lksQchdxi6pdhnttcB4l9Pp9csYwKBiWQ0GwC/iERKuChWjHQlid23qLcYppg+JYhcEDQZKnZILzS9B18U2wq6Zm8sz2upiZ9DPVHCQxaVDfuJAHnswGGU8FaawzqFhiILkgBrzTOvXHSxjzJVtQLAV217uQj/oNGo1bVLua4tRUkJ7eOgLpEaixYmLn0+xP0+gv2N93xOYodlQRd2XJ7AnDFoPw+iJFzcP4pJf0URlxzMBXy5ZkUUSNe5BaXHTuzrNxY5LNK6dsd5s15q3MmpMTDRw/VcepAwH8M5OLWuztbQ8gdI9oNP11mofDrfR5A/xm4hi/uukgH1x7ml/a/A47ZzIgVfjd9kdoVqzgO5nnyj4eH97A/d27aP+HbqI1ECT1/TChyRyturnKreMVNOpkFaW+DjEtYCSXvrlPYdDDyd9v5cDPd+DfUuLaukG+e7KL0TN1rPhyCnMyCWmrI9bFJ8LQ42fgcXj1iBzhc/muiyHXrCVm/t2d6F0eAmsXXDKWENCeiV5gp41fM8K6zeP8Xv238Fhlvs7x8GQnf9x9Fe2fOoFnpvaeLV0RKDRJqF5emRjuG93Ol4+vZcUTJxCXuIbAazmYbeTxU7u4rbWHFf45VriS7HQXqJNeP3GFRdEWABbn2wxKpsGMbvDJwetJPy4Q/tsZTG2CYA0Xt7KqOgqzVZr+8ZT9s5UAbSFQpIXpVyJAXs5LqYUkGzPgQb16Az9y9yNs29nP4UozuWeDBB+SMUt6bVUZfA05w0Peri9T4uMfeobqB3U77z4o9lA0vPxzejX/cmgjg4frqf/SUYQaFjZIEqLHQ/qulbTsKvJH93yBuKzYfUassv1FQ2Oq4OFTX/gQvDhN8IgV/LC4vO1iwNRUjFSas8k45rxENl61SwvH5DK/3XzQnrutevjtisCoFub5fIRn/2kV44MKDWf77Vzl5UbvvnbS8mqUnzxALJDDLVTRGgJoSQOxFsqsV6oYYxMo/2yihwxOuF2QnaYun1poQFTLavpcCtdv7N5HyzYNOXju+VAFjMYL98v+WIlAqIQiGEzoEiMlN098ZTf9p2UaTw4gp2vz2bLK28vlhTKl1hkOayB0azQ+odr92msN15ki0T8bZcRrMOeL0lvXyjPNbsSgRDUo4lqdR469fiesjXjRhz0oZTCsKn9FDeNIDu+YupCJUMML0/dCqNWYobCOcE8Gf6uOnA/w5Of3MPecijk0Dm9Y6bIGMAX2P7KJyWQJ486TeMUikqDaj8iRTCcj03VM/7uPytk84cms3RiuplFkhEScn9t7hK5ds8QkGRcCBUPmqWI9fUdaGD4eQ3nsLEJyaVxKb39qoWHadcULIwKz9W76Q1E6IxWiXo2rAgtdpKxObcOFKAOpMCcmIww8F6c6UiCQXPBJLzfmh332Xrn3qgCNYYOYKaOqVrvJGhlsuo6ZKyAeX9hIzyHZuxnfIpes/YERoCGcZ12sQKvvPBNyHZQNwQ7M8okmFas2hS4ylIkwnDI5OyPzwvP1VM/kCQzWXse4l7H8oHYmjtXHwzQpWx0W1SrxSpGyLGMab19nsrcDaV7Duz+HdSdybjdSQ4RSawg1rFCJiHhyWeSG1/s61TN+9D4fctG0KybKBQ3hxADK29SC1eFC9ISCsEIivC5NUXMzNRRn5MUoxtlpxOyCL76WmT4RRDXdJDqCRCQR1zllfHguxtBYDN9zJsZsBk9hwZVQy5huiUqLny3tM+xstrqqimR1NzNVH91jDZzubmLspQi+oZ5XqnUuNpeszkD0n8YpP+Dn1+/+cX7/w09w65ZX66anqx4+8uK9eJ4ziL9QQjsz9LY3XVhUpuZQp+e4/0eaEGiyXzKMEc5Ln3b4IbDy8H/j2E1sd2X4wlWPXPBejyqTNkRu8FQYUGVO5AL8+UPvw/PYFKGnxzCNvtq/D4aJUjCpqCpVs8IGxc3QPUnSO0VO/UIT2lQeI1dbfvRXqFTQR8ZRRsftuAbbRL5QwvJ1KK8Jv/8e6fkOPyTZX2gkdLXJ7Y1H+fbnNjL+bAzh6CDiInWy/GFRDg+QPwxPfjFilaN65XWTCi5zvNbjBC+gnJCZeF+YUsOr1syHc+0cGG/m9N+tRzw+hm/41AWZOJeNGLBMn0K2gue5szw+4eFkrOOV98q6THBqDGnKRJ/Raj768y1hdS6zgtqW+jwuQ6xFI/zgJJmXVP76vrYL3pszRKqmwCFJt0VBSlUIjA4jjZXs+7EcsPohuA8N8pS/g1NHWjBDbqbnFeanJLRkFuM1+eK1yAWTmDMIlhTT54bWBjteIFsQePrpbRSOVjEGJhEsIVArFsu3+ExdbBwvj5H9KmIVfJMCUgWyusK+cj2nH4wxc9CNeHQEYT6/pELgklcgtCIhXf1JTva7OGnZdM/Ddy78xpk3HN4Kge4MVmTDt4lf9P3u8773svRBm98PQlVDHk7S80Irp/ojlBIKnmQFZa6EmbWadDmjxOH7wHItRYIIBY3qqEDfiQaCg4N4XlMPxWHxECsmnrEK2TGJMcHLwVyUsRe85J8TEOdqY75a+nLEDg4ONsLgOOLZcfznesnb8XTLOKjOYWkQsgWEg6eIHHk5pUG4PKyvyxh5JkfkP4/y9w/EEIUoVqNlUy1gGrUT7+CIAQeHWsE0z5kKHQHg8ENi1Qt5Zf13nqelRrDLChpo2hsE1NQAtVHz08HBwcHBwWHJEEyry4aDg4ODg4PDFYtjGXBwcHBwcLjCccSAg4ODg4PDFY4jBhwcHBwcHK5wHDHg4ODg4OBwheOIAQcHBwcHhyscRww4ODg4ODhc4ThiwMHBwcHB4QrHEQMODg4ODg5XOI4YcHBwcHBw4Mrm/web0OMCWpnLEAAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 640x480 with 40 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["labels = [\"0\", \"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\"]\n", "rows = 4\n", "\n", "print(labels)\n", "\n", "for y, label in enumerate(labels):\n", "    ##\n", "    idxs = np.nonzero([i == y for i in iis_ys])\n", "    idxs = np.random.choice(idxs[0], rows)\n", "\n", "    ##\n", "    for i , idx in enumerate(idxs):\n", "        plt_idx = i * len(labels) + y + 1\n", "        plt.subplot(rows, len(labels), plt_idx)\n", "        plt.imshow(iis_Xs[idx].reshape((28, 28)))\n", "        plt.axis(\"off\")\n", "        if i == 0:\n", "            plt.title(label)\n", "\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 13, "id": "79c22465", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(33600, 784) (8400, 784) (33600,) (8400,)\n"]}], "source": ["from sklearn.model_selection import train_test_split\n", "\n", "X_train, X_valid, y_train, y_valid = train_test_split(iis_Xs, iis_ys, test_size=0.2, random_state=42)\n", "print(X_train.shape, X_valid.shape, y_train.shape, y_valid.shape)"]}, {"cell_type": "code", "execution_count": 14, "id": "c8a88faf", "metadata": {}, "outputs": [], "source": ["from sklearn.neighbors import KNeighborsClassifier\n", "from sklearn.metrics import accuracy_score, classification_report, confusion_matrix"]}, {"cell_type": "code", "execution_count": null, "id": "d23577f2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["              precision    recall  f1-score   support\n", "\n", "           0       0.98      1.00      0.99       816\n", "           1       0.95      1.00      0.97       909\n", "           2       0.98      0.95      0.96       846\n", "           3       0.97      0.96      0.96       937\n", "           4       0.98      0.97      0.98       839\n", "           5       0.95      0.97      0.96       702\n", "           6       0.97      0.99      0.98       785\n", "           7       0.96      0.97      0.96       893\n", "           8       0.98      0.93      0.95       835\n", "           9       0.95      0.95      0.95       838\n", "\n", "    accuracy                           0.97      8400\n", "   macro avg       0.97      0.97      0.97      8400\n", "weighted avg       0.97      0.97      0.97      8400\n", "\n", "[[812   0   0   0   0   1   3   0   0   0]\n", " [  0 907   0   0   0   0   2   0   0   0]\n", " [  7  13 800   4   3   2   1  14   1   1]\n", " [  1   5   5 896   0   9   0   8   9   4]\n", " [  1   3   0   0 816   0   3   1   0  15]\n", " [  0   0   0   7   1 678   8   0   3   5]\n", " [  6   0   0   0   0   3 776   0   0   0]\n", " [  0  14   4   0   1   0   0 863   0  11]\n", " [  3  11   6  11   2  16   3   2 774   7]\n", " [  2   2   2   9   9   2   0  14   0 798]]\n"]}], "source": ["model = KNeighborsClassifier(n_neighbors=3)\n", "model.fit(X_train, y_train)\n", "y_pred = model.predict(X_valid)\n", "accuracy = accuracy_score(y_valid, y_pred)\n", "\n", "print(classification_report(y_valid, y_pred))\n", "print(confusion_matrix(y_valid, y_pred))"]}, {"cell_type": "code", "execution_count": 45, "id": "d3de171e", "metadata": {}, "outputs": [], "source": ["class KNNClassifier:\n", "    ##\n", "    def __init__(self, k: int = 3):\n", "        ##\n", "        self.k = k\n", "\n", "    def fit(self, X: np.n<PERSON>ray, y: np.n<PERSON>ray):\n", "        ##\n", "        self.X_train = X\n", "        self.y_train = y\n", "\n", "    def predict(self, X: np.ndarray) -> np.ndarray:\n", "        ##\n", "        iis_samples_n = self.X_train.shape[0]\n", "        diffs = np.tile(X, (iis_samples_n, 1)) - self.X_train\n", "        dists = np.sqrt(np.sum(np.square(diffs), axis=1))\n", "        dists = dists.argsort()\n", "\n", "        ##\n", "        votes = dict()\n", "        for i in range(self.k):\n", "            label = self.y_train[dists[i]]\n", "            votes[label] = votes.get(label, 0) + 1\n", "\n", "        ##\n", "        return max(votes, key=votes.get)"]}, {"cell_type": "code", "execution_count": 27, "id": "501a6f95", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 8400/8400 [07:49<00:00, 17.88it/s]\n"]}], "source": ["from sklearn.metrics import accuracy_score\n", "import tqdm\n", "\n", "classifier = KNNClassifier(k=3)\n", "classifier.fit(X_train, y_train)\n", "\n", "predictions = np.zeros(len(y_valid))\n", "for i in tqdm.tqdm(range(X_valid.shape[0])):\n", "    output = classifier.predict(X_valid[i])\n", "    predictions[i] = output\n", "\n", "accuracy = accuracy_score(y_valid, predictions)"]}, {"cell_type": "code", "execution_count": 28, "id": "6eb8a1b8", "metadata": {}, "outputs": [{"data": {"text/plain": ["0.9683333333333334"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["accuracy"]}, {"cell_type": "code", "execution_count": 29, "id": "b4316a8a", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.imshow(oos_Xs[34].reshape((28, 28)))\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 46, "id": "ea5241df", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['7', '5', '4', '2', '6', '2', '5', '5', '1', '6']\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 10 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["classifier = KNNClassifier(k=3)\n", "classifier.fit(iis_Xs, iis_ys)\n", "labels = [str(classifier.predict(X)) for X in oos_Xs[30:40]]\n", "\n", "print(labels)\n", "\n", "##\n", "for i in range(len(labels)):\n", "    plt_idx = i + 1\n", "    plt.subplot(1, len(labels), plt_idx)\n", "    plt.imshow(oos_Xs[i + 30].reshape((28, 28)))\n", "    plt.axis(\"off\")\n", "\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}