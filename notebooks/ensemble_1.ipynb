%matplotlib inline
import numpy as np
import matplotlib.pyplot as plt

from sklearn.tree import DecisionTreeClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.utils import shuffle

def plot_decision_boundary(X, model):
    h = .02

    x_min, x_max = X[:, 0].min() - 1, X[:, 0].max() + 1
    y_min, y_max = X[:, 1].min() - 1, X[:, 1].max() + 1

    xx, yy = np.meshgrid(np.arange(x_min, x_max, h), np.arange(y_min, y_max, h))

    Z = model.predict(np.c_[xx.ravel(), yy.ravel()])

    Z = Z.reshape(xx.shape)
    plt.contour(xx, yy, Z, cmap=plt.cm.Paired)

class BaggedTreeClassifier:
    ##
    def __init__(self, M):
        ## M is the number of trees
        self.M = M

    ##
    def fit(self, Xs, ys):
        ## N is the number of samples
        N = len(Xs)
        self.models = list()

        for _ in range(self.M):
            ##
            idx = np.random.choice(N, size=N, replace=True)
            Xb = Xs[idx]
            yb = ys[idx]

            model = DecisionTreeClassifier(max_depth=5)
            model.fit(Xb, yb)
            self.models.append(model)

    ##
    def predict(self, Xs):
        ##
        predictions = np.zeros(len(Xs))
        for model in self.models:
            ##
            predictions += model.predict(Xs)
        ##
        return np.round(predictions / self.M)

    ##
    def score(self, Xs, ys):
        ##
        return np.mean(self.predict(Xs) == ys)

np.random.seed(10)

N = 500
D = 2
X = np.random.randn(N, D)

delta = 1.75
X[: 125] += np.array([delta, delta])
X[125: 250] += np.array([delta, -delta])
X[250: 375] += np.array([-delta, delta])
X[375:] += np.array([-delta, -delta])

Y = np.array([0] * 125 + [1] * 125 + [1] * 125 + [0] * 125)

plt.scatter(X[:, 0], X[:, 1], s=100, c=Y, alpha=0.5)
plt.show()

model = DecisionTreeClassifier()
model.fit(X, Y)
print("Score for basic tree:", model.score(X, Y))

## plot data with boundary
plt.scatter(X[:, 0], X[:, 1], s=100, c=Y, alpha=0.5)
plot_decision_boundary(X, model)
plt.show()

model_d3 = DecisionTreeClassifier(criterion="entropy", max_depth=3)
model_d3.fit(X, Y)
print("Score for d3 tree:", model_d3.score(X, Y))

## plot data with boundary
plt.scatter(X[:, 0], X[:, 1], s=100, c=Y, alpha=0.5)
plot_decision_boundary(X, model_d3)
plt.show()

model_d5 = DecisionTreeClassifier(criterion="entropy", max_depth=5)
model_d5.fit(X, Y)
print("Score for d5 tree:", model_d5.score(X, Y))

## plot data with boundary
plt.scatter(X[:, 0], X[:, 1], s=100, c=Y, alpha=0.5)
plot_decision_boundary(X, model_d5)
plt.show()

model_logistic = LogisticRegression()
model_logistic.fit(X, Y)
print("Score for logistic regression:", model_logistic.score(X, Y))

## plot data with boundary
plt.scatter(X[:, 0], X[:, 1], s=100, c=Y, alpha=0.5)
plot_decision_boundary(X, model_logistic)
plt.show()

model_bag = BaggedTreeClassifier(200)
model_bag.fit(X, Y)
print("Score for bagged tree:", model_bag.score(X, Y))

## plot data with boundary
plt.scatter(X[:, 0], X[:, 1], s=100, c=Y, alpha=0.5)
plot_decision_boundary(X, model_bag)
plt.show()

np.random.seed(10)

N = 500
D = 2
X = np.random.randn(N, D)

R_smaller = 5
R_larger = 10

R1 = np.random.randn(N // 2) + R_smaller
theta = 2 * np.pi * np.random.random(N // 2)
X[: N // 2] = np.array([R1 * np.cos(theta), R1 * np.sin(theta)]).T

R2 = np.random.randn(N // 2) + R_larger
theta = 2 * np.pi * np.random.random(N // 2)
X[N // 2 :] = np.array([R2 * np.cos(theta), R2 * np.sin(theta)]).T

Y = np.array([0] * (N // 2) + [1] * (N // 2))

plt.scatter(X[:, 0], X[:, 1], s=100, c=Y, alpha=0.5)
plt.show()

model = DecisionTreeClassifier()
model.fit(X, Y)
print("Score for basic tree:", model.score(X, Y))

## plot data with boundary
plt.scatter(X[:, 0], X[:, 1], s=100, c=Y, alpha=0.5)
plot_decision_boundary(X, model)
plt.show()

model_d3 = DecisionTreeClassifier(criterion="entropy", max_depth=3)
model_d3.fit(X, Y)
print("Score for d3 tree:", model_d3.score(X, Y))

## plot data with boundary
plt.scatter(X[:, 0], X[:, 1], s=100, c=Y, alpha=0.5)
plot_decision_boundary(X, model_d3)
plt.show()

model_d5 = DecisionTreeClassifier(criterion="entropy", max_depth=5)
model_d5.fit(X, Y)
print("Score for d5 tree:", model_d5.score(X, Y))

## plot data with boundary
plt.scatter(X[:, 0], X[:, 1], s=100, c=Y, alpha=0.5)
plot_decision_boundary(X, model_d5)
plt.show()

model_logistic = LogisticRegression()
model_logistic.fit(X, Y)
print("Score for logistic regression:", model_logistic.score(X, Y))

## plot data with boundary
plt.scatter(X[:, 0], X[:, 1], s=100, c=Y, alpha=0.5)
plot_decision_boundary(X, model_logistic)
plt.show()

model_bag = BaggedTreeClassifier(200)
model_bag.fit(X, Y)
print("Score for bagged tree:", model_bag.score(X, Y))

## plot data with boundary
plt.scatter(X[:, 0], X[:, 1], s=100, c=Y, alpha=0.5)
plot_decision_boundary(X, model_bag)
plt.show()