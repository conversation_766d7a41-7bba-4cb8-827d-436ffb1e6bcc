from sklearn.datasets import load_digits
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, confusion_matrix, classification_report

import xgboost as xgb
import matplotlib.pyplot as plt

def load_data():
    digits = load_digits()
    samples = zip(digits.images, digits.target)

    for idx, (image, label) in enumerate(list(samples)[:4]):
        plt.subplot(1, 4, idx + 1)
        plt.axis("off")
        plt.imshow(image)
        plt.title(f"Label: {label}")
    plt.show()
    return train_test_split(digits.data, digits.target, test_size=0.2, random_state=42)

X_train, X_test, y_train, y_test = load_data()
X_train.shape, X_test.shape, y_train.shape, y_test.shape

dtrian = xgb.DMatrix(X_train, label=y_train)

param = {
    "max_depth": 6,
    "eta": 0.3,
    "objective": "multi:softmax",
    "num_class": 10,
}

max_round = 20
model = xgb.train(params=param, dtrain=dtrian, num_boost_round=max_round)
y_pred = model.predict(xgb.DMatrix(X_test, label=None))

print(f"accuracy_score: {accuracy_score(y_test, y_pred)}\n")
print(confusion_matrix(y_test, y_pred))
print(classification_report(y_test, y_pred))