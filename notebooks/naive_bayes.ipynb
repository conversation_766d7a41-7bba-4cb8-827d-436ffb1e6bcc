{"cells": [{"cell_type": "code", "execution_count": 70, "id": "76de3294", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 71, "id": "a96ded2b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>v1</th>\n", "      <th>v2</th>\n", "      <th>Unnamed: 2</th>\n", "      <th>Unnamed: 3</th>\n", "      <th>Unnamed: 4</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>ham</td>\n", "      <td>Go until jurong point, crazy.. Available only ...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>ham</td>\n", "      <td>Ok lar... Joking wif u oni...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>spam</td>\n", "      <td>Free entry in 2 a wkly comp to win FA Cup fina...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>ham</td>\n", "      <td>U dun say so early hor... U c already then say...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>ham</td>\n", "      <td>Nah I don't think he goes to usf, he lives aro...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     v1                                                 v2 Unnamed: 2  \\\n", "0   ham  Go until jurong point, crazy.. Available only ...        NaN   \n", "1   ham                      Ok lar... Joking wif u oni...        NaN   \n", "2  spam  Free entry in 2 a wkly comp to win FA Cup fina...        NaN   \n", "3   ham  U dun say so early hor... U c already then say...        NaN   \n", "4   ham  Nah I don't think he goes to usf, he lives aro...        NaN   \n", "\n", "  Unnamed: 3 Unnamed: 4  \n", "0        NaN        NaN  \n", "1        NaN        NaN  \n", "2        NaN        NaN  \n", "3        NaN        NaN  \n", "4        NaN        NaN  "]}, "execution_count": 71, "metadata": {}, "output_type": "execute_result"}], "source": ["inputs_df = pd.read_csv(\"../.inputs/email/spam.csv\", encoding=\"latin-1\")\n", "inputs_df.head()"]}, {"cell_type": "code", "execution_count": 72, "id": "e205e43a", "metadata": {}, "outputs": [{"data": {"text/plain": ["0    Go until jurong point, crazy.. Available only ...\n", "1                        Ok lar... Joking wif u oni...\n", "2    Free entry in 2 a wkly comp to win FA Cup fina...\n", "3    U dun say so early hor... U c already then say...\n", "4    Nah I don't think he goes to usf, he lives aro...\n", "Name: v2, dtype: object"]}, "execution_count": 72, "metadata": {}, "output_type": "execute_result"}], "source": ["inputs_df.v2.head()"]}, {"cell_type": "code", "execution_count": 73, "id": "8e3a6d79", "metadata": {}, "outputs": [{"data": {"text/plain": ["0     ham\n", "1     ham\n", "2    spam\n", "3     ham\n", "4     ham\n", "Name: v1, dtype: object"]}, "execution_count": 73, "metadata": {}, "output_type": "execute_result"}], "source": ["inputs_df.v1.head()"]}, {"cell_type": "code", "execution_count": 74, "id": "06a1ef9f", "metadata": {}, "outputs": [{"data": {"text/plain": ["(5572, 5)"]}, "execution_count": 74, "metadata": {}, "output_type": "execute_result"}], "source": ["inputs_df.shape"]}, {"cell_type": "code", "execution_count": 75, "id": "ebfe45b6", "metadata": {}, "outputs": [{"data": {"text/plain": ["((4457,), (1115,), (4457,), (1115,))"]}, "execution_count": 75, "metadata": {}, "output_type": "execute_result"}], "source": ["from sklearn.model_selection import train_test_split\n", "\n", "X_train, X_test, y_train, y_test = train_test_split(inputs_df.v2, inputs_df.v1, test_size=0.2, random_state=42)\n", "X_train.shape, X_test.shape, y_train.shape, y_test.shape"]}, {"cell_type": "code", "execution_count": 76, "id": "ade72531", "metadata": {}, "outputs": [], "source": ["from sklearn.feature_extraction.text import CountVectorizer"]}, {"cell_type": "code", "execution_count": 77, "id": "5da08574", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['are' 'good' 'student' 'students' 'we' 'you']\n"]}, {"data": {"text/plain": ["array([[1, 1, 0, 1, 1, 0],\n", "       [1, 1, 1, 0, 0, 1]])"]}, "execution_count": 77, "metadata": {}, "output_type": "execute_result"}], "source": ["X_train_demo = [\"We are good students\", \"You are a good student\"]\n", "vectorizer_demo = CountVectorizer()\n", "X_train_demo_count = vectorizer_demo.fit_transform(X_train_demo)\n", "## Test set no need for fit again; fit only on training data to build vocabulary\n", "## while test will use the same vocabulary.\n", "X_train_demo_transformed = vectorizer_demo.transform(X_train_demo)\n", "print(vectorizer_demo.get_feature_names_out())\n", "X_train_demo_count.toarray()"]}, {"cell_type": "code", "execution_count": 78, "id": "c8db40f0", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[1, 1, 0, 1, 1, 0],\n", "       [1, 1, 1, 0, 0, 1]])"]}, "execution_count": 78, "metadata": {}, "output_type": "execute_result"}], "source": ["X_train_demo_transformed.toarray()"]}, {"cell_type": "code", "execution_count": 79, "id": "fa44a626", "metadata": {}, "outputs": [{"data": {"text/plain": ["((4457, 7735), (1115, 7735))"]}, "execution_count": 79, "metadata": {}, "output_type": "execute_result"}], "source": ["vectorizer = CountVectorizer()\n", "X_train_count = vectorizer.fit_transform(X_train)\n", "X_test_count = vectorizer.transform(X_test)\n", "X_train_count.shape, X_test_count.shape"]}, {"cell_type": "code", "execution_count": 80, "id": "36ead3df", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["occurrences = X_train_count.toarray().sum(axis=0)\n", "plt.plot(occurrences)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 81, "id": "7ef690c3", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>term</th>\n", "      <th>occurrences</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>6906</th>\n", "      <td>to</td>\n", "      <td>1823</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7679</th>\n", "      <td>you</td>\n", "      <td>1746</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6786</th>\n", "      <td>the</td>\n", "      <td>1052</td>\n", "    </tr>\n", "    <tr>\n", "      <th>955</th>\n", "      <td>and</td>\n", "      <td>761</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3637</th>\n", "      <td>in</td>\n", "      <td>722</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3739</th>\n", "      <td>is</td>\n", "      <td>710</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4390</th>\n", "      <td>me</td>\n", "      <td>649</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4646</th>\n", "      <td>my</td>\n", "      <td>597</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3750</th>\n", "      <td>it</td>\n", "      <td>579</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2954</th>\n", "      <td>for</td>\n", "      <td>570</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     term  occurrences\n", "6906   to         1823\n", "7679  you         1746\n", "6786  the         1052\n", "955   and          761\n", "3637   in          722\n", "3739   is          710\n", "4390   me          649\n", "4646   my          597\n", "3750   it          579\n", "2954  for          570"]}, "execution_count": 81, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame({\"term\" : vectorizer.get_feature_names_out(), \"occurrences\" : occurrences}).sort_values(by=\"occurrences\", ascending=False).head(10)"]}, {"cell_type": "code", "execution_count": 82, "id": "c2674e14", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['ham', 'ham', 'spam', ..., 'ham', 'ham', 'spam'], dtype='<U4')"]}, "execution_count": 82, "metadata": {}, "output_type": "execute_result"}], "source": ["from sklearn.naive_bayes import MultinomialNB\n", "\n", "model = MultinomialNB()\n", "model.fit(X_train_count, y_train)\n", "y_pred = model.predict(X_test_count)\n", "y_pred"]}, {"cell_type": "code", "execution_count": 83, "id": "85017fdf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.9838565022421525\n", "[[963   2]\n", " [ 16 134]]\n", "              precision    recall  f1-score   support\n", "\n", "         ham       0.98      1.00      0.99       965\n", "        spam       0.99      0.89      0.94       150\n", "\n", "    accuracy                           0.98      1115\n", "   macro avg       0.98      0.95      0.96      1115\n", "weighted avg       0.98      0.98      0.98      1115\n", "\n"]}], "source": ["from sklearn.metrics import accuracy_score, confusion_matrix, classification_report\n", "\n", "print(accuracy_score(y_test, y_pred))\n", "\n", "print(confusion_matrix(y_test, y_pred))\n", "\n", "print(classification_report(y_test, y_pred))"]}, {"cell_type": "code", "execution_count": 84, "id": "fed80b7e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.9813349320543564\n"]}], "source": ["from sklearn.model_selection import cross_val_score\n", "\n", "data_content = inputs_df.v2\n", "data_labels = inputs_df.v1\n", "\n", "vect = CountVectorizer()\n", "data_count = vect.fit_transform(data_content)\n", "cross_val = cross_val_score(model, data_count, data_labels, cv=20, scoring=\"accuracy\")\n", "print(cross_val.mean())"]}, {"cell_type": "code", "execution_count": 85, "id": "f4da85c4", "metadata": {}, "outputs": [], "source": ["def getVocabulary(archive):\n", "    vocab_dict = dict()\n", "    word_id = 0\n", "\n", "    for document in archive:\n", "        for word in document.split():\n", "            if word.lower() not in vocab_dict:\n", "                vocab_dict[word.lower()] = word_id\n", "                word_id += 1\n", "\n", "    return vocab_dict"]}, {"cell_type": "code", "execution_count": 86, "id": "d170101b", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'no': 0,\n", " \"i'm\": 1,\n", " 'in': 2,\n", " 'the': 3,\n", " 'same': 4,\n", " 'boat.': 5,\n", " 'still': 6,\n", " 'here': 7,\n", " 'at': 8,\n", " 'my': 9,\n", " 'moms.': 10,\n", " 'check': 11,\n", " 'me': 12,\n", " 'out': 13,\n", " 'on': 14,\n", " 'yo.': 15,\n", " 'half': 16,\n", " 'naked.': 17,\n", " '(bank': 18,\n", " 'of': 19,\n", " 'granite': 20,\n", " 'issues': 21,\n", " 'strong-buy)': 22,\n", " 'explosive': 23,\n", " 'pick': 24,\n", " 'for': 25,\n", " 'our': 26,\n", " 'members': 27,\n", " '*****up': 28,\n", " 'over': 29,\n", " '300%': 30,\n", " '***********': 31,\n", " 'nasdaq': 32,\n", " 'symbol': 33,\n", " 'cdgt': 34,\n", " 'that': 35,\n", " 'is': 36,\n", " 'a': 37,\n", " '$5.00': 38,\n", " 'per..': 39,\n", " 'they': 40,\n", " 'r': 41,\n", " 'giving': 42,\n", " 'second': 43,\n", " 'chance': 44,\n", " 'to': 45,\n", " 'rahul': 46,\n", " 'dengra.': 47,\n", " 'o': 48,\n", " 'i': 49,\n", " 'played': 50,\n", " 'smash': 51,\n", " 'bros': 52,\n", " '&lt;#&gt;': 53,\n", " 'religiously.': 54,\n", " 'private!': 55,\n", " 'your': 56,\n", " '2003': 57,\n", " 'account': 58,\n", " 'statement': 59,\n", " '***********': 60,\n", " 'shows': 61,\n", " '800': 62,\n", " 'un-redeemed': 63,\n", " 's.': 64,\n", " 'i.': 65,\n", " 'm.': 66,\n", " 'points.': 67,\n", " 'call': 68,\n", " '***********': 69,\n", " 'identifier': 70,\n", " 'code:': 71,\n", " '40533': 72,\n", " 'expires': 73,\n", " '31/10/04': 74,\n", " 'g': 75,\n", " 'says': 76,\n", " 'you': 77,\n", " 'never': 78,\n", " 'answer': 79,\n", " 'texts,': 80,\n", " 'confirm/deny': 81,\n", " '88066': 82,\n", " 'from': 83,\n", " 'lost': 84,\n", " '3pound': 85,\n", " 'help': 86,\n", " 'okey': 87,\n", " 'dokey,': 88,\n", " 'i\\x89û÷ll': 89,\n", " 'be': 90,\n", " 'bit': 91,\n", " 'just': 92,\n", " 'sorting': 93,\n", " 'some': 94,\n", " 'stuff': 95,\n", " 'out.': 96,\n", " 'why': 97,\n", " 'come': 98,\n", " 'between': 99,\n", " 'people': 100,\n", " 'wah': 101,\n", " 'lucky': 102,\n", " 'man...': 103,\n", " 'then': 104,\n", " 'can': 105,\n", " 'save': 106,\n", " 'money...': 107,\n", " 'hee...': 108,\n", " 'much': 109,\n", " 'better': 110,\n", " 'now': 111,\n", " 'thanks': 112,\n", " 'lol': 113,\n", " 'madam,regret': 114,\n", " 'disturbance.might': 115,\n", " 'receive': 116,\n", " 'reference': 117,\n", " 'dlf': 118,\n", " 'premarica.kindly': 119,\n", " 'informed.rgds,rakhesh,kerala.': 120,\n", " 'coming': 121,\n", " 'home': 122,\n", " '4': 123,\n", " 'dinner.': 124,\n", " 'ok...': 125,\n", " 'ì_': 126,\n", " 'all': 127,\n", " 'decide': 128,\n", " 'faster': 129,\n", " 'cos': 130,\n", " 'sis': 131,\n", " 'going': 132,\n", " 'liao..': 133,\n", " 'hi': 134,\n", " 'mate': 135,\n", " 'its': 136,\n", " 'rv': 137,\n", " 'did': 138,\n", " 'u': 139,\n", " 'hav': 140,\n", " 'nice': 141,\n", " 'hol': 142,\n", " 'message': 143,\n", " '3': 144,\n", " 'say': 145,\n", " 'hello': 146,\n", " 'coz': 147,\n", " 'havenå<PERSON><PERSON>': 148,\n", " 'sent': 149,\n", " '1': 150,\n", " 'ages': 151,\n", " 'started': 152,\n", " 'driving': 153,\n", " 'so': 154,\n", " 'stay': 155,\n", " 'off': 156,\n", " 'roads!rvx': 157,\n", " 'amazing': 158,\n", " ':': 159,\n", " 'if': 160,\n", " 'rearrange': 161,\n", " 'these': 162,\n", " 'letters': 163,\n", " 'it': 164,\n", " 'gives': 165,\n", " 'meaning...': 166,\n", " 'dormitory': 167,\n", " '=': 168,\n", " 'dirty': 169,\n", " 'room': 170,\n", " 'astronomer': 171,\n", " 'moon': 172,\n", " 'starer': 173,\n", " 'eyes': 174,\n", " 'see': 175,\n", " 'election': 176,\n", " 'results': 177,\n", " 'lies': 178,\n", " 'lets': 179,\n", " 'recount': 180,\n", " 'mother-in-law': 181,\n", " 'woman': 182,\n", " 'hitler': 183,\n", " 'eleven': 184,\n", " 'plus': 185,\n", " 'two': 186,\n", " '=twelve': 187,\n", " 'one': 188,\n", " 'amazing...': 189,\n", " '!:-)': 190,\n", " 'good': 191,\n", " 'morning': 192,\n", " 'plz': 193,\n", " 'sir': 194,\n", " 'k': 195,\n", " 'actually': 196,\n", " 'guys': 197,\n", " 'meet': 198,\n", " 'sunoco': 199,\n", " 'howard?': 200,\n", " 'should': 201,\n", " 'right': 202,\n", " 'way': 203,\n", " 'new': 204,\n", " 'theory:': 205,\n", " 'argument': 206,\n", " 'wins': 207,\n", " 'd': 208,\n", " 'situation,': 209,\n", " 'but': 210,\n", " 'loses': 211,\n", " 'person.': 212,\n", " 'dont': 213,\n", " 'argue': 214,\n", " 'with': 215,\n", " 'ur': 216,\n", " 'friends': 217,\n", " 'just..': 218,\n", " '.': 219,\n", " 'kick': 220,\n", " 'them': 221,\n", " '&amp;': 222,\n", " 'say,': 223,\n", " 'always': 224,\n", " 'correct.!': 225,\n", " 'oh': 226,\n", " 'yeah!': 227,\n", " 'and': 228,\n", " 'diet': 229,\n", " 'flew': 230,\n", " 'window': 231,\n", " 'watching': 232,\n", " 'cartoon,': 233,\n", " 'listening': 234,\n", " 'music': 235,\n", " 'eve': 236,\n", " 'had': 237,\n", " 'go': 238,\n", " 'temple': 239,\n", " 'church..': 240,\n", " 'what': 241,\n", " 'about': 242,\n", " 'u?': 243,\n", " 'india': 244,\n", " 'win': 245,\n", " 'or': 246,\n", " 'level': 247,\n", " 'series': 248,\n", " 'means': 249,\n", " 'this': 250,\n", " 'record:)': 251,\n", " 'armand': 252,\n", " 'get': 253,\n", " 'ass': 254,\n", " 'epsilon': 255,\n", " 'tell': 256,\n", " 'her': 257,\n", " 'said': 258,\n", " 'eat': 259,\n", " 'shit.': 260,\n", " 'oh,': 261,\n", " 'phone': 262,\n", " 'phoned': 263,\n", " 'disconnected': 264,\n", " 'please': 265,\n", " 'customer': 266,\n", " 'service': 267,\n", " 'representative': 268,\n", " 'freephone': 269,\n", " '0808': 270,\n", " '145': 271,\n", " '4742': 272,\n", " '9am-11pm': 273,\n", " 'as': 274,\n", " 'have': 275,\n", " 'won': 276,\n", " 'guaranteed': 277,\n", " 'å£1000': 278,\n", " 'cash': 279,\n", " 'å£5000': 280,\n", " 'prize!': 281,\n", " 'hey': 282,\n", " 'sweet,': 283,\n", " 'was': 284,\n", " 'wondering': 285,\n", " 'when': 286,\n", " 'moment': 287,\n", " 'might': 288,\n", " '?': 289,\n", " 'want': 290,\n", " 'send': 291,\n", " 'file': 292,\n", " 'someone': 293,\n", " \"won't\": 294,\n", " 'yahoo': 295,\n", " 'because': 296,\n", " 'their': 297,\n", " 'connection': 298,\n", " 'sucks,': 299,\n", " 'remember': 300,\n", " 'set': 301,\n", " 'up': 302,\n", " 'page': 303,\n", " 'download': 304,\n", " 'format': 305,\n", " 'disc': 306,\n", " 'could': 307,\n", " 'how': 308,\n", " 'do': 309,\n", " 'know': 310,\n", " 'other': 311,\n", " 'big': 312,\n", " 'files': 313,\n", " 'directly': 314,\n", " 'internet.': 315,\n", " 'any': 316,\n", " 'would': 317,\n", " 'great,': 318,\n", " 'prey': 319,\n", " '...': 320,\n", " '*teasing': 321,\n", " 'kiss*': 322,\n", " 'purity': 323,\n", " 'friendship': 324,\n", " 'not': 325,\n", " 'smiling': 326,\n", " 'after': 327,\n", " 'reading': 328,\n", " 'forwarded': 329,\n", " 'message..its': 330,\n", " 'by': 331,\n", " 'seeing': 332,\n", " 'name.': 333,\n", " 'gud': 334,\n", " 'evng': 335,\n", " 'heard': 336,\n", " 'u4': 337,\n", " 'while.': 338,\n", " 'am': 339,\n", " 'night': 340,\n", " 'knickers': 341,\n", " 'on.': 342,\n", " 'make': 343,\n", " 'beg': 344,\n", " 'like': 345,\n", " 'last': 346,\n", " 'time': 347,\n", " '01223585236': 348,\n", " 'xx': 349,\n", " 'luv': 350,\n", " 'nikiyu4.net': 351,\n", " 'will': 352,\n", " 'place': 353,\n", " 'man': 354,\n", " 'welp': 355,\n", " 'apparently': 356,\n", " 'he': 357,\n", " 'retired': 358,\n", " 'haha': 359,\n", " 'money': 360,\n", " 'leh...': 361,\n", " 'later': 362,\n", " 'got': 363,\n", " 'tuition...': 364,\n", " 'looking': 365,\n", " 'empty': 366,\n", " 'slots': 367,\n", " 'lessons': 368,\n", " 'aiyar': 369,\n", " 'hard': 370,\n", " '2': 371,\n", " 'type.': 372,\n", " 'free': 373,\n", " 'n': 374,\n", " 'scold': 375,\n", " 'u.': 376,\n", " \"i'll\": 377,\n", " 'close': 378,\n", " 'wat': 379,\n", " 'late': 380,\n", " 'early': 381,\n", " 'mah.': 382,\n", " 'we': 383,\n", " 'juz': 384,\n", " 'dinner': 385,\n", " 'lor.': 386,\n", " 'aiya': 387,\n", " 'dunno...': 388,\n", " 'yup': 389,\n", " \"it's\": 390,\n", " 'paragon...': 391,\n", " 'havent': 392,\n", " 'decided': 393,\n", " 'whether': 394,\n", " 'cut': 395,\n", " 'yet...': 396,\n", " 'tv': 397,\n", " 'now.': 398,\n", " 'job': 399,\n", " ':)': 400,\n", " '2004': 401,\n", " '07742676969': 402,\n", " '786': 403,\n", " 'unredeemed': 404,\n", " 'bonus': 405,\n", " 'claim': 406,\n", " '08719180248': 407,\n", " '45239': 408,\n", " 'great!': 409,\n", " 'run': 410,\n", " 'ttyl!': 411,\n", " 'hello,': 412,\n", " 'love!': 413,\n", " 'goes': 414,\n", " 'day': 415,\n", " 'wish': 416,\n", " 'well': 417,\n", " 'fine': 418,\n", " 'babe': 419,\n", " 'hope': 420,\n", " 'find': 421,\n", " 'prospects.': 422,\n", " 'miss': 423,\n", " 'you,': 424,\n", " 'boytoy': 425,\n", " '*a': 426,\n", " 'teasing': 427,\n", " 'zoe': 428,\n", " 'hit': 429,\n", " 'im': 430,\n", " 'fucking': 431,\n", " 'shitin': 432,\n", " 'myself': 433,\n", " 'il': 434,\n", " 'defo': 435,\n", " 'try': 436,\n", " 'hardest': 437,\n", " 'cum': 438,\n", " '2morow': 439,\n", " 'millions': 440,\n", " 'lekdog': 441,\n", " 'text': 442,\n", " 'cup': 443,\n", " 'stop': 444,\n", " 'work.': 445,\n", " 'bus': 446,\n", " 'congratulations': 447,\n", " 'ore': 448,\n", " 'mo': 449,\n", " 'owo': 450,\n", " 're': 451,\n", " 'wa.': 452,\n", " 'enjoy': 453,\n", " 'many': 454,\n", " 'happy': 455,\n", " 'moments': 456,\n", " 'fro': 457,\n", " 'wherever': 458,\n", " 'morning,': 459,\n", " 'suffering': 460,\n", " 'fever': 461,\n", " 'dysentry': 462,\n", " '..will': 463,\n", " 'able': 464,\n", " 'office': 465,\n", " 'today.': 466,\n", " 'pa.': 467,\n", " 'pain': 468,\n", " 'de.': 469,\n", " 'it.': 470,\n", " 'seventeen': 471,\n", " 'pounds': 472,\n", " 'seven': 473,\n", " 'hundred': 474,\n", " 'ml': 475,\n", " '\\x89ûò': 476,\n", " 'ok.': 477,\n", " 'complain': 478,\n", " 'num': 479,\n", " 'only..bettr': 480,\n", " 'bsnl': 481,\n", " 'offc': 482,\n", " 'nd': 483,\n", " 'apply': 484,\n", " 'it..': 485,\n", " 'kate': 486,\n", " 'jackson': 487,\n", " 'rec': 488,\n", " 'center': 489,\n", " 'before': 490,\n", " '7ish,': 491,\n", " 'right?': 492,\n", " 'k..k.:)congratulation': 493,\n", " '..': 494,\n", " 'whatsup': 495,\n", " 'there.': 496,\n", " 'sleep': 497,\n", " 'å£12': 498,\n", " 'japanese': 499,\n", " 'proverb:': 500,\n", " 'it,': 501,\n", " 'too': 502,\n", " 'none': 503,\n", " 'it,u': 504,\n", " 'must': 505,\n", " 'indian': 506,\n", " 'version:': 507,\n", " 'let': 508,\n", " 'him': 509,\n", " 'it,leave': 510,\n", " 'it!!': 511,\n", " 'finally': 512,\n", " 'kerala': 513,\n", " 'doing': 514,\n", " 'strike': 515,\n", " 'against': 516,\n", " 'search': 517,\n", " 'happiness': 518,\n", " 'main': 519,\n", " 'sources': 520,\n", " 'unhappiness!': 521,\n", " 'accept': 522,\n", " 'life': 523,\n", " 'comes!': 524,\n", " 'every': 525,\n", " 'live.': 526,\n", " 'faith': 527,\n", " 'makes': 528,\n", " 'things': 529,\n", " 'possible,hope': 530,\n", " 'work,love': 531,\n", " 'beautiful,may': 532,\n", " 'three': 533,\n", " 'christmas!merry': 534,\n", " 'christmas!': 535,\n", " 'c': 536,\n", " 'ya...': 537,\n", " 'class.': 538,\n", " 'holla': 539,\n", " 'buy': 540,\n", " 'space': 541,\n", " 'invaders': 542,\n", " 'orig': 543,\n", " 'arcade': 544,\n", " 'game': 545,\n", " 'console.': 546,\n", " 'press': 547,\n", " '0': 548,\n", " 'games': 549,\n", " '(std': 550,\n", " 'wap': 551,\n", " 'charge)': 552,\n", " 'o2.co.uk/games': 553,\n", " 'terms': 554,\n", " '+': 555,\n", " 'settings.': 556,\n", " 'purchase': 557,\n", " 'house': 558,\n", " 'water': 559,\n", " 'dock,': 560,\n", " 'boat': 561,\n", " 'rolled': 562,\n", " 'newscaster': 563,\n", " 'who': 564,\n", " 'dabbles': 565,\n", " 'jazz': 566,\n", " 'flute': 567,\n", " 'behind': 568,\n", " 'wheel': 569,\n", " 'baby': 570,\n", " 'house.': 571,\n", " 'pictures': 572,\n", " 'facebook': 573,\n", " 'thx.': 574,\n", " 'few': 575,\n", " 'months': 576,\n", " 'mrng': 577,\n", " 'dear': 578,\n", " 'yar': 579,\n", " 'wanted': 580,\n", " 'yest': 581,\n", " 'already...': 582,\n", " 'where': 583,\n", " 'zhong': 584,\n", " 'se': 585,\n", " 'qing': 586,\n", " 'you?': 587,\n", " 'ask': 588,\n", " 'b4': 589,\n", " 'w': 590,\n", " 'act': 591,\n", " 'real.': 592,\n", " 'slowly.?': 593,\n", " 'god,i': 594,\n", " 'love': 595,\n", " 'need': 596,\n", " 'you,clean': 597,\n", " 'heart': 598,\n", " 'blood.send': 599,\n", " 'ten': 600,\n", " 'special': 601,\n", " 'miracle': 602,\n", " 'tomorrow,': 603,\n", " 'it,pls,pls': 604,\n", " 'it...': 605,\n", " 'yes': 606,\n", " 'princess!': 607,\n", " 'catch': 608,\n", " 'strong': 609,\n", " 'hands...': 610,\n", " 'wait,': 611,\n", " 'wesleys': 612,\n", " 'town?': 613,\n", " 'bet': 614,\n", " 'she': 615,\n", " 'does': 616,\n", " 'hella': 617,\n", " 'drugs!': 618,\n", " 'problem': 619,\n", " 'renewal.': 620,\n", " 'i.ll': 621,\n", " 'away': 622,\n", " 'his': 623,\n", " 'details.': 624,\n", " 'part': 625,\n", " \"\\\\don't\": 626,\n", " 'initiate\\\\\"': 627,\n", " \"don't\": 628,\n", " 'understand\"': 629,\n", " 'has': 630,\n", " 'color': 631,\n", " 'dreams,': 632,\n", " 'stars': 633,\n", " 'musical': 634,\n", " 'sms': 635,\n", " 'give': 636,\n", " 'warm': 637,\n", " 'peaceful': 638,\n", " 'sleep.': 639,\n", " 'are': 640,\n", " 'stand': 641,\n", " \"doesn't\": 642,\n", " 'ache': 643,\n", " 'without': 644,\n", " 'wonder': 645,\n", " 'crave': 646,\n", " 'urgent,': 647,\n", " 'important': 648,\n", " 'information': 649,\n", " 'o2': 650,\n", " 'user.': 651,\n", " 'today': 652,\n", " 'day!': 653,\n", " 'log': 654,\n", " 'onto': 655,\n", " 'http://www.urawinner.com': 656,\n", " 'there': 657,\n", " 'fantastic': 658,\n", " 'surprise': 659,\n", " 'awaiting': 660,\n", " 'oz': 661,\n", " 'guy': 662,\n", " 'being': 663,\n", " 'kinda': 664,\n", " 'flaky': 665,\n", " 'friend': 666,\n", " 'interested': 667,\n", " 'picking': 668,\n", " '$': 669,\n", " 'worth': 670,\n", " 'tonight': 671,\n", " 'possible': 672,\n", " 'waiting': 673,\n", " 'machan.': 674,\n", " 'once': 675,\n", " 'free.': 676,\n", " 'honeybee': 677,\n", " 'said:': 678,\n", " \"*i'm\": 679,\n", " 'sweetest': 680,\n", " 'world*': 681,\n", " 'god': 682,\n", " 'laughed': 683,\n", " '*wait,u': 684,\n", " 'havnt': 685,\n", " 'met': 686,\n", " 'person': 687,\n", " 'msg*': 688,\n", " 'moral:': 689,\n", " 'even': 690,\n", " 'crack': 691,\n", " 'jokes!': 692,\n", " 'gm+gn+ge+gn:)': 693,\n", " 'gas': 694,\n", " 'station.': 695,\n", " 'ammae....life': 696,\n", " 'takes': 697,\n", " 'lot': 698,\n", " 'turns': 699,\n", " 'only': 700,\n", " 'sit': 701,\n", " 'hold': 702,\n", " 'steering...': 703,\n", " 'hmm.': 704,\n", " 'shall': 705,\n", " 'bring': 706,\n", " 'bottle': 707,\n", " 'wine': 708,\n", " 'keep': 709,\n", " 'us': 710,\n", " 'amused?': 711,\n", " 'joking!': 712,\n", " 'bottle.': 713,\n", " 'red': 714,\n", " 'white?': 715,\n", " 'tomorrow': 716,\n", " 'sorry': 717,\n", " 'missed': 718,\n", " 'babe.': 719,\n", " 'slept': 720,\n", " 'in.': 721,\n", " 'lesson,': 722,\n", " 'boytoy.': 723,\n", " 'thanx...': 724,\n", " 'gd': 725,\n", " 'nite': 726,\n", " 'too...': 727,\n", " 'polyphonic': 728,\n", " 'tone': 729,\n", " 'mob': 730,\n", " 'week!': 731,\n", " 'txt': 732,\n", " 'pt2': 733,\n", " '87575.': 734,\n", " '1st': 735,\n", " '!': 736,\n", " 'txtin': 737,\n", " 'friends.': 738,\n", " '150p/tone.': 739,\n", " '16': 740,\n", " 'reply': 741,\n", " 'hl': 742,\n", " '4info': 743,\n", " 'nothing': 744,\n", " 'really,': 745,\n", " 'making': 746,\n", " 'sure': 747,\n", " \"everybody's\": 748,\n", " 'speed': 749,\n", " 'you.': 750,\n", " 'holding': 751,\n", " 'tightly.': 752,\n", " 'are.': 753,\n", " 'mean': 754,\n", " 'mom': 755,\n", " 'fit': 756,\n", " 'whole': 757,\n", " 'family': 758,\n", " 'crazy': 759,\n", " 'terrible': 760,\n", " 'good,': 761,\n", " \"we'll\": 762,\n", " 'iåõm': 763,\n", " 'cool': 764,\n", " 'ta': 765,\n", " 'v.tired': 766,\n", " 'cause': 767,\n", " 'been': 768,\n", " 'doin': 769,\n", " 'loads': 770,\n", " 'planning': 771,\n", " 'wk,': 772,\n", " 'social': 773,\n", " 'services': 774,\n", " 'inspection': 775,\n", " 'nursery!': 776,\n", " 'take': 777,\n", " 'care': 778,\n", " '&': 779,\n", " 'spk': 780,\n", " 'sn': 781,\n", " 'x.': 782,\n", " 'light': 783,\n", " 'turned': 784,\n", " 'green,': 785,\n", " 'meant': 786,\n", " 'another': 787,\n", " 'may': 788,\n", " 'around': 789,\n", " 'que': 790,\n", " 'pases': 791,\n", " 'un': 792,\n", " 'buen': 793,\n", " 'tiempo': 794,\n", " 'something': 795,\n", " 'telugu': 796,\n", " 'movie..wat': 797,\n", " 'abt': 798,\n", " 'mm': 799,\n", " 'enough': 800,\n", " ':-)': 801,\n", " 'winner!!': 802,\n", " 'valued': 803,\n", " 'network': 804,\n", " 'selected': 805,\n", " 'receivea': 806,\n", " 'å£900': 807,\n", " 'prize': 808,\n", " 'reward!': 809,\n", " '09061701461.': 810,\n", " 'code': 811,\n", " 'kl341.': 812,\n", " 'valid': 813,\n", " '12': 814,\n", " 'hours': 815,\n", " 'only.': 816,\n", " 'lor...': 817,\n", " 'raining': 818,\n", " 'non': 819,\n", " 'stop...': 820,\n", " 'wan': 821,\n", " 'elsewhere?': 822,\n", " 'otherwise': 823,\n", " 'na-tuition..': 824,\n", " 'words....': 825,\n", " 'words': 826,\n", " 'leave': 827,\n", " 'dismay': 828,\n", " 'times.': 829,\n", " 'oooh': 830,\n", " 'bed': 831,\n", " 'ridden': 832,\n", " 'ey?': 833,\n", " 'thinking': 834,\n", " 'of?': 835,\n", " 'oclock': 836,\n", " 'mine.': 837,\n", " 'bash': 838,\n", " 'flat': 839,\n", " 'plan.': 840,\n", " 'gsoh?': 841,\n", " 'spam': 842,\n", " 'ladies?u': 843,\n", " 'b': 844,\n", " 'male': 845,\n", " 'gigolo?': 846,\n", " 'join': 847,\n", " \"uk's\": 848,\n", " 'fastest': 849,\n", " 'growing': 850,\n", " 'mens': 851,\n", " 'club': 852,\n", " 'oncall.': 853,\n", " 'mjzgroup.': 854,\n", " '08714342399.2stop': 855,\n", " 'stop.': 856,\n", " 'msg@å£1.50rcvd': 857,\n", " 'gram': 858,\n", " 'usually': 859,\n", " 'runs': 860,\n", " ',': 861,\n", " 'eighth': 862,\n", " 'smarter': 863,\n", " 'though': 864,\n", " 'gets': 865,\n", " 'almost': 866,\n", " 'good!': 867,\n", " 'you...': 868,\n", " 'crab': 869,\n", " 'running': 870,\n", " 'sea': 871,\n", " 'shore..the': 872,\n", " 'waves': 873,\n", " 'came': 874,\n", " 'cleared': 875,\n", " 'footprints': 876,\n", " 'crab..': 877,\n", " 'asked:': 878,\n", " 'frnd': 879,\n", " 'y': 880,\n", " 'clearing': 881,\n", " 'beautiful': 882,\n", " 'footprints?': 883,\n", " 'replied:': 884,\n", " 'fox': 885,\n", " 'following': 886,\n", " 'you!': 887,\n", " 'thats': 888,\n", " 'off:)': 889,\n", " 'frndsship': 890,\n", " 'dwn': 891,\n", " 'nyt..': 892,\n", " 'dunno': 893,\n", " 'jus': 894,\n", " 'lido.': 895,\n", " '930.': 896,\n", " 'msg:': 897,\n", " 'gnarls': 898,\n", " 'barkleys': 899,\n", " '\\\\crazy\\\\\"': 900,\n", " 'ringtone': 901,\n", " 'totally': 902,\n", " 'now!\"': 903,\n", " 'saying': 904,\n", " 'welp?': 905,\n", " 'official': 906,\n", " 'england': 907,\n", " 'poly': 908,\n", " 'colour': 909,\n", " 'flag': 910,\n", " 'yer': 911,\n", " 'mobile': 912,\n", " 'tonights': 913,\n", " 'game!': 914,\n", " '84199.': 915,\n", " 'optout': 916,\n", " 'eng': 917,\n", " 'box39822': 918,\n", " 'w111wx': 919,\n", " 'å£1.50': 920,\n", " 'also': 921,\n", " 'thk': 922,\n", " 'fast...': 923,\n", " 'xy': 924,\n", " 'suggest': 925,\n", " 'me.': 926,\n", " 'dun': 927,\n", " 'rain': 928,\n", " 'leh': 929,\n", " 'gd.': 930,\n", " 'fran': 931,\n", " 'e': 932,\n", " 'completely': 933,\n", " 'broke': 934,\n", " 'an': 935,\n", " 'knackered': 936,\n", " 'bout': 937,\n", " '2mrw': 938,\n", " 'janx': 939,\n", " 'p.s': 940,\n", " 'dads': 941,\n", " 'fone,': 942,\n", " '-no': 943,\n", " 'credit': 944,\n", " 'welcome': 945,\n", " 'improved': 946,\n", " 'sex': 947,\n", " 'dogging': 948,\n", " 'club!': 949,\n", " 'unsubscribe': 950,\n", " 'msgs@150p': 951,\n", " '18': 952,\n", " 'no1': 953,\n", " 'nokia': 954,\n", " 'week': 955,\n", " '8077': 956,\n", " 'txting': 957,\n", " 'mates.': 958,\n", " 'www.getzed.co.uk': 959,\n", " 'pobox': 960,\n", " '36504': 961,\n", " 'w45wq': 962,\n", " '16+': 963,\n", " 'norm150p/tone': 964,\n", " 'cool.': 965,\n", " 'swimming?': 966,\n", " 'pool': 967,\n", " 'jacuzzi': 968,\n", " 'course.': 969,\n", " 'guess': 970,\n", " \"god's\": 971,\n", " 'getting': 972,\n", " 'u....': 973,\n", " 'whos': 974,\n", " 'class:-)': 975,\n", " 'loosu': 976,\n", " 'hospital.': 977,\n", " 'de': 978,\n", " 'careless.': 979,\n", " 'k,': 980,\n", " 'roommate': 981,\n", " 'wants': 982,\n", " 'dubsack': 983,\n", " 'plan': 984,\n", " 'bringing': 985,\n", " 'extra,': 986,\n", " 'oh...': 987,\n", " 'haha...': 988,\n", " 'den': 989,\n", " 'shld': 990,\n", " 'went': 991,\n", " 'gee,': 992,\n", " 'nvm': 993,\n", " 'la...': 994,\n", " 'kaiez,': 995,\n", " 'mind': 996,\n", " 'goin': 997,\n", " 'oso...': 998,\n", " 'scared': 999,\n", " ...}"]}, "execution_count": 86, "metadata": {}, "output_type": "execute_result"}], "source": ["vocab_dict = getVocabulary(X_train)\n", "vocab_dict"]}, {"cell_type": "code", "execution_count": 87, "id": "db1c11b6", "metadata": {}, "outputs": [], "source": ["def Document2Vector(vocab_dict, document):\n", "    word_vector = np.zeros(len(vocab_dict))\n", "    words = document.split()\n", "    out_of_vocab = 0\n", "\n", "    for word in words:\n", "        if word.lower() in vocab_dict:\n", "            word_vector[vocab_dict[word.lower()]] += 1\n", "            continue\n", "        out_of_vocab += 1\n", "\n", "    return word_vector, out_of_vocab"]}, {"cell_type": "code", "execution_count": 88, "id": "cb77934d", "metadata": {}, "outputs": [{"data": {"text/plain": ["(4457, 11782)"]}, "execution_count": 88, "metadata": {}, "output_type": "execute_result"}], "source": ["train_matrix = []\n", "for document in X_train:\n", "    word_vector, _ = Document2Vector(vocab_dict, document)\n", "    train_matrix.append(word_vector)\n", "\n", "train_matrix = np.array(train_matrix)\n", "train_matrix.shape"]}, {"cell_type": "code", "execution_count": 89, "id": "fea38238", "metadata": {}, "outputs": [], "source": ["def naiveBayes_train(train_matrix, train_labels):\n", "    num_documents = train_matrix.shape[0]\n", "    num_words = train_matrix.shape[1]\n", "\n", "    spam_word_counter = np.ones(num_words)\n", "    ham_word_counter = np.ones(num_words)\n", "\n", "    spam_total_words = 0\n", "    ham_total_words = 0\n", "\n", "    spam_count = 0\n", "    ham_count = 0\n", "\n", "    for i in range(num_documents):\n", "        if train_labels[i] == \"ham\":\n", "            ham_word_counter += train_matrix[i]\n", "            ham_total_words += np.sum(train_matrix[i])\n", "            ham_count += 1\n", "        else:\n", "            spam_word_counter += train_matrix[i]\n", "            spam_total_words += np.sum(train_matrix[i])\n", "            spam_count += 1\n", "\n", "    p_spam_vector = np.log(spam_word_counter / (spam_total_words + num_words))\n", "    p_ham_vector = np.log(ham_word_counter / (ham_total_words + num_words))\n", "\n", "    p_spam = np.log(spam_count / num_documents)\n", "    p_ham = np.log(ham_count / num_documents)\n", "\n", "    return p_spam_vector, p_ham_vector, p_spam, p_ham, spam_total_words, ham_total_words"]}, {"cell_type": "code", "execution_count": 90, "id": "e9140c24", "metadata": {}, "outputs": [{"data": {"text/plain": ["((11782,),\n", " (11782,),\n", " np.float64(-2.0103140595539535),\n", " np.float64(-0.14380871048767932),\n", " np.float64(14280.0),\n", " np.float64(54545.0))"]}, "execution_count": 90, "metadata": {}, "output_type": "execute_result"}], "source": ["p_spam_vector, p_ham_vector, p_spam, p_ham, spam_total_words, ham_total_words = naiveBayes_train(train_matrix, y_train.values)\n", "p_spam_vector.shape, p_ham_vector.shape, p_spam, p_ham, spam_total_words, ham_total_words"]}, {"cell_type": "code", "execution_count": 91, "id": "a9acfe80", "metadata": {}, "outputs": [], "source": ["def predict(test_word_vector, p_spam_vector, p_ham_vector, p_spam, p_ham, spam_smoothing, ham_smoothing):\n", "    ## Here we use test_word_vector as count of each word in the document\n", "    ## and if the word appears multiple times in the document then it will be\n", "    ## multiplied by the probability of the word multiple times.\n", "    p_spam_given_vector = np.sum(test_word_vector * p_spam_vector) + p_spam + spam_smoothing\n", "    p_ham_given_vector = np.sum(test_word_vector * p_ham_vector) + p_ham + ham_smoothing\n", "\n", "    if p_spam_given_vector > p_ham_given_vector:\n", "        return \"spam\"\n", "    else:\n", "        return \"ham\""]}, {"cell_type": "code", "execution_count": 92, "id": "26c9c7bf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.9811659192825112\n", "[[962   3]\n", " [ 18 132]]\n", "              precision    recall  f1-score   support\n", "\n", "         ham       0.98      1.00      0.99       965\n", "        spam       0.98      0.88      0.93       150\n", "\n", "    accuracy                           0.98      1115\n", "   macro avg       0.98      0.94      0.96      1115\n", "weighted avg       0.98      0.98      0.98      1115\n", "\n"]}], "source": ["num_words = len(vocab_dict)\n", "predictions = []\n", "\n", "for document in X_test:\n", "    test_word_vector, out_of_vocab = Document2Vector(vocab_dict, document)\n", "    if out_of_vocab > 0:\n", "        spam_smoothing = np.log(out_of_vocab / (spam_total_words + num_words))\n", "        ham_smoothing = np.log(out_of_vocab / (ham_total_words + num_words))\n", "    else:\n", "        spam_smoothing = 0\n", "        ham_smoothing = 0\n", "    predictions.append(predict(test_word_vector, p_spam_vector, p_ham_vector, p_spam, p_ham, spam_smoothing, ham_smoothing))\n", "\n", "print(accuracy_score(y_test, predictions))\n", "print(confusion_matrix(y_test, predictions))\n", "print(classification_report(y_test, predictions))\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}