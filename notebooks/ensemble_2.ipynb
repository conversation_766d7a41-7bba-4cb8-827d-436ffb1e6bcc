%matplotlib inline
import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.ensemble import AdaBoostClassifier
from sklearn.tree import DecisionTreeClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import train_test_split, cross_val_score

np.random.seed(19)

data = pd.read_csv("../.inputs/breast-cancer-wisconsin-data/data.csv")
data.head()

data["diagnosis"].value_counts()

data.info

data.drop("id", axis=1, inplace=True)
data.drop("Unnamed: 32", axis=1, inplace=True)
data["diagnosis"] = data["diagnosis"].apply(lambda x: 1 if x == "M" else -1)
data

data.describe()

data.info()

import seaborn as sns

sns.countplot(data, x="diagnosis", hue="diagnosis")
plt.show()

features = data.columns[1:7]
target = "diagnosis"
features

i = 0
for feature in features:
    ##
    bins = 25
    plt.hist(data[feature][data[target] == -1], bins=bins, color="lightblue", label="B-healthy", alpha=0.5)
    plt.hist(data[feature][data[target] == 1], bins=bins, color="k", label="M-cancer", alpha=0.5)

    ##
    plt.xlabel(feature)
    plt.ylabel("Amount of counts")

    plt.legend()
    plt.show()

train_data, test_data = train_test_split(data, test_size=0.3)
train_data.shape, test_data.shape

iis_Xs, iis_ys = train_data[data.columns[1:]], train_data[data.columns[0]]
oos_Xs, oos_ys = test_data[data.columns[1:]], test_data[data.columns[0]]
iis_Xs.shape, iis_ys.shape, oos_Xs.shape, oos_ys.shape

logistic_model = LogisticRegression(max_iter=10000)
print(f"Logistic Regression performance: {cross_val_score(logistic_model, iis_Xs, iis_ys, cv=8).mean()}")

tree_model = DecisionTreeClassifier()
print(f"Decision Tree performance: {cross_val_score(tree_model, iis_Xs, iis_ys, cv=8).mean()}")

ada_model = AdaBoostClassifier(n_estimators=200)
print(f"AdaBoost performance: {cross_val_score(ada_model, iis_Xs, iis_ys, cv=8).mean()}")

logistic_model = LogisticRegression(max_iter=10000)
tree_model = DecisionTreeClassifier()
ada_model = AdaBoostClassifier(n_estimators=200)

logistic_model.fit(iis_Xs, iis_ys)
tree_model.fit(iis_Xs, iis_ys)
ada_model.fit(iis_Xs, iis_ys)

print(f"Logistic Regression performance: {logistic_model.score(oos_Xs, oos_ys)}")
print(f"Decision Tree performance: {tree_model.score(oos_Xs, oos_ys)}")
print(f"AdaBoost performance: {ada_model.score(oos_Xs, oos_ys)}")

from sklearn.base import BaseEstimator
class Adaboost(BaseEstimator):
    ##
    def __init__(self, M):
        ## M is the number of stumps
        self.M = M

    ##
    def fit(self, Xs, ys):
        self.models = list()
        self.model_Ws = list()

        N, _ = Xs.shape
        sample_Ws = np.ones(N) / N

        for _ in range(self.M):
            ##
            tree = DecisionTreeClassifier(max_depth=2)
            tree.fit(Xs, ys, sample_weight=sample_Ws)
            predictions = tree.predict(Xs)

            ## Amount of say
            weighted_error = np.dot(sample_Ws, predictions != ys)
            model_amount_of_say = 0.5 * (np.log(1 - weighted_error) -  np.log(weighted_error))

            ##
            sample_Ws *= np.exp(-model_amount_of_say * ys * predictions)
            sample_Ws /= sample_Ws.sum()

            ##
            self.models.append(tree)
            self.model_Ws.append(model_amount_of_say)

    ##
    def predict(self, Xs):
        ##
        N, _ = Xs.shape
        predictions = np.zeros(N)
        for model_weight, model in zip(self.model_Ws, self.models):
            ##
            predictions += model_weight * model.predict(Xs)
        ##
        return np.sign(predictions)

    ##
    def score(self, Xs, ys):
        ##
        return np.mean(self.predict(Xs) == ys)

model = Adaboost(M=100)
print(f"AdaBoost iis performance: {cross_val_score(model, iis_Xs, iis_ys, cv=8).mean()}")
model.fit(iis_Xs, iis_ys)
print(f"AdaBoost OOS performance: {model.score(oos_Xs, oos_ys)}")