%matplotlib inline
import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.ensemble import RandomForestClassifier
from sklearn.tree import DecisionTreeClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import train_test_split, cross_val_score

np.random.seed(19)

data = pd.read_csv("../.inputs/mushroom-classification/mushrooms.csv", header=None)
data.head()

data.info()

data[0] = data.apply(lambda row: 0 if row[0] == "e" else 1, axis=1)
data.head()

cols = np.arange(1, 23)
for col in cols:
    ##
    if np.any(data[col].isnull()):
        data.loc[data[col].isnull(), col] = "missing"

labelEncoders = dict()

for col in cols:
    ##
    encoder = LabelEncoder()
    values = data[col].tolist()
    values.append("missing")
    encoder.fit(values)
    labelEncoders[col] = encoder

dimensionality = 0
for col, encoder in labelEncoders.items():
    ##
    dimensionality += len(encoder.classes_)

print("Dimensionality:", dimensionality)

def transform(df):
    ##
    N, _ = df.shape
    X = np.zeros((N, dimensionality))
    i = 0
    for col, encoder in labelEncoders.items():
        ##
        k = len(encoder.classes_)
        X[np.arange(N), encoder.transform(df[col]) + i] = 1
        i += k
    ##
    return X

Xs = transform(data.iloc[:, 1:])
ys = data[0].values
Xs.shape, ys.shape

Xs

ys

logistic_model = LogisticRegression(max_iter=10000)
print(f"Logistic Regression performance: {cross_val_score(logistic_model, Xs, ys, cv=8).mean()}")

tree_model = DecisionTreeClassifier()
print(f"Decision Tree performance: {cross_val_score(tree_model, Xs, ys, cv=8).mean()}")

rf_model = RandomForestClassifier(n_estimators=20)
print(f"Random Forest performance: {cross_val_score(rf_model, Xs, ys, cv=8).mean()}")

from sklearn.base import BaseEstimator

class FakeRandomForest(BaseEstimator):
    ##
    def __init__(self, n_estimators):
        ## n_estimators is the number of trees
        self.n_estimators = n_estimators

    ##
    def fit(self, Xs, ys, n_features=None):
        ## N is the number of samples; D is the number of features
        N, D = Xs.shape

        ##
        if n_features is None:
            n_features = int(np.sqrt(D))

        ##
        self.models = list()
        self.features = list()

        for _ in range(self.n_estimators):
            ## Tree without pruning
            tree = DecisionTreeClassifier()

            ## Bootstrap sampling (sample with replacement)
            idx = np.random.choice(N, size=N, replace=True)
            Xb = Xs[idx]
            yb = ys[idx]

            ## Note: in real random forest, each depth of the tree
            ## will require resampling of features
            features = np.random.choice(D, size=n_features, replace=False)
            tree.fit(Xb[:, features], yb)
            self.features.append(features)
            self.models.append(tree)

    ##
    def predict(self, Xs):
        ##
        predictions = np.zeros(len(Xs))
        for features, tree in zip(self.features, self.models):
            ##
            predictions += tree.predict(Xs[:, features])
        ##
        return np.round(predictions / self.n_estimators)

    ##
    def score(self, Xs, ys):
        ##
        return np.mean(self.predict(Xs) == ys)

frf_model = FakeRandomForest(n_estimators=20)
print(f"Fake Random Forest performance: {cross_val_score(frf_model, Xs, ys, cv=8).mean()}")

class BaggingTreeClassifier(BaseEstimator):
    ##
    def __init__(self, n_estimators):
        ## n_estimators is the number of trees
        self.n_estimators = n_estimators

    ##
    def fit(self, Xs, ys):
        ## N is the number of samples
        N = len(Xs)
        self.models = list()

        for _ in range(self.n_estimators):
            ##
            idx = np.random.choice(N, size=N, replace=True)
            Xb = Xs[idx]
            yb = ys[idx]

            model = DecisionTreeClassifier(max_depth=2)
            model.fit(Xb, yb)
            self.models.append(model)

    ##
    def predict(self, Xs):
        ##
        predictions = np.zeros(len(Xs))
        for model in self.models:
            ##
            predictions += model.predict(Xs)
        ##
        return np.round(predictions / self.n_estimators)

    ##
    def score(self, Xs, ys):
        ##
        return np.mean(self.predict(Xs) == ys)

bag_model = BaggingTreeClassifier(n_estimators=20)
print(f"Bagging performance: {cross_val_score(bag_model, Xs, ys, cv=8).mean()}")

from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import LinearRegression

house_data = pd.read_csv("../.inputs/kc_house_data/kc_house_data.csv")
house_data.head()

house_data.info()

NUMERICAL_COLS = ["price", "bedrooms", "bathrooms", "sqft_living", "sqft_lot", "sqft_above", "sqft_basement", "sqft_living15", "sqft_lot15"]

scalers = dict()
for col in NUMERICAL_COLS:
    scaler = StandardScaler()
    scaler.fit(house_data[col].values.astype(np.float64).reshape(-1, 1))
    scalers[col] = scaler

def transform_numerical(df):
    ##
    N, _ = df.shape
    D = len(NUMERICAL_COLS)
    result = np.zeros((N, D))
    i = 0
    for col, scaler in scalers.items():
        ##
        result[:, i] = scaler.transform(df[col].values.astype(np.float64).reshape(-1, 1))[:, 0]
        i += 1
    ##
    return result

from sklearn.model_selection import train_test_split

hdata = transform_numerical(house_data)

hdata

train_data, test_data = train_test_split(hdata, test_size=0.2)

iis_Xs, iis_ys = train_data[:, 1:], train_data[:, 0]
oos_Xs, oos_ys = test_data[:, 1:], test_data[:, 0]
iis_Xs.shape, iis_ys.shape, oos_Xs.shape, oos_ys.shape

rfregressor = RandomForestRegressor(n_estimators=100)
rfregressor.fit(iis_Xs, iis_ys)
predictions = rfregressor.predict(oos_Xs)

plt.scatter(oos_ys, predictions)
plt.xlabel("target")
plt.ylabel("prediction")
ymin = np.round(min(min(oos_ys), min(predictions)))
ymax = np.ceil(max(max(oos_ys), max(predictions)))
r = range(int(ymin), int(ymax) + 1)
plt.plot(r, r)
plt.show()

plt.plot(oos_ys, label='targets')
plt.plot(predictions, label='predictions')
plt.legend()
plt.show()

linear_model = LinearRegression()
print(f"Linear Regression performance: {cross_val_score(linear_model, iis_Xs, iis_ys, cv=8).mean()}")

print(f"Random Forest performance: {cross_val_score(rfregressor, iis_Xs, iis_ys, cv=8).mean()}")

linear_model.fit(iis_Xs, iis_ys)
print(f"Linear Regression OOS performance: {linear_model.score(oos_Xs, oos_ys)}")

rfregressor.fit(iis_Xs, iis_ys)
print(f"Random Forest OOS performance: {rfregressor.score(oos_Xs, oos_ys)}")

data = pd.read_csv("../.inputs/mushroom-classification/mushrooms.csv")
data.head()

data.info()

data["class"] = data.apply(lambda row: -1 if row[0] == "e" else 1, axis=1)
data.head()

def dummies(
        data,
        columns,
):
    ##
    for col in columns:
        data[col] = data[col].apply(lambda x: str(x))
        new_cols = [col + "_" + i for i in data[col].unique()]
        data = pd.concat([data, pd.get_dummies(data[col], prefix=col, dtype=int)[new_cols]], axis=1)
        del data[col]
    ##
    return data

target = "class"
cols = data.columns.drop(target)

cols

data_set = dummies(data, cols)
data_set.head()

train_data, test_data = train_test_split(data_set, test_size=0.3)
train_data.shape, test_data.shape

iis_Xs, iis_ys = train_data.drop(columns=[target]), pd.DataFrame(train_data[target])
oos_Xs, oos_ys = test_data.drop(columns=[target]), pd.DataFrame(test_data[target])
iis_Xs.shape, iis_ys.shape, oos_Xs.shape, oos_ys.shape

class TreeNode:
    ##
    def __init__(self, is_leaf, prediction, split_feature):
        ##
        self.is_leaf = is_leaf
        self.prediction = prediction
        self.split_feature = split_feature
        self.left = None
        self.right = None

def node_weighted_mistakes(
        targets_in_node,
        data_weights,
):
    ## If we predict the node as negative (-1): All the positive examples (+1) in the node would be misclassified.
    ## So the weighted mistakes would equal the total weight of positive examples (weight_positive).
    weight_positive = sum(data_weights[targets_in_node == +1])
    weighted_mistakes_negative = weight_positive

    ## If we predict the node as positive (+1): All the negative examples (-1) in the node would be misclassified.
    ## So the weighted mistakes would equal the total weight of negative examples (weight_negative).
    weight_negative = sum(data_weights[targets_in_node == -1])
    weighted_mistakes_positive = weight_negative

    if weighted_mistakes_negative < weighted_mistakes_positive:
        return weighted_mistakes_negative, -1
    else:
        return weighted_mistakes_positive, 1

example_targets = np.array([-1, -1, 1, 1, 1])
example_data_weights = np.array([1., 2., .5, 1., 1.])
node_weighted_mistakes(example_targets, example_data_weights)

def best_split_weighted(
        data,
        features,
        target,
        data_weights,
):
    ##
    best_feature = None
    best_error = np.inf
    num_samples = float(data.shape[0])

    for feature in features:
        ##
        left_split = data[data[feature] == 0]
        left_data_weights = data_weights[data[feature] == 0]

        ##
        right_split = data[data[feature] == 1]
        right_data_weights = data_weights[data[feature] == 1]

        ##
        left_weighted_mistakes, left_class = node_weighted_mistakes(left_split[target], left_data_weights)
        right_weighted_mistakes, right_class = node_weighted_mistakes(right_split[target], right_data_weights)

        ##
        error = (left_weighted_mistakes + right_weighted_mistakes) * 1.0 / sum(data_weights)
        if error < best_error:
            best_error = error
            best_feature = feature
    ##
    return best_feature

features = data_set.columns.drop(target)
example_data_weights = np.array(len(train_data) * [2.])
best_split_weighted(train_data, features, target, example_data_weights)

class TreeNode:
    ##
    def __init__(self, is_leaf, prediction, split_feature):
        ##
        self.is_leaf = is_leaf
        self.prediction = prediction
        self.split_feature = split_feature
        self.left = None
        self.right = None

##
def create_leaf(
        target_values,
        data_weights,
):
    ##
    leaf = TreeNode(True, None, None)
    weighted_error, best_class = node_weighted_mistakes(target_values, data_weights)
    leaf.prediction = best_class

    ##
    return leaf

def create_weighted_tree(
        data,
        data_weights,
        features,
        target,
        current_depth = 0,
        max_depth = 10,
        min_error = 1e-15,
):
    ##
    remaining_features = features[:]
    target_values = data[target]

    ## termination 1
    if node_weighted_mistakes(target_values, data_weights)[0] <= min_error:
        print("Termination 1 reached.")
        return create_leaf(target_values, data_weights)

    ## termination 2
    if len(remaining_features) == 0:
        print("Termination 2 reached.")
        return create_leaf(target_values, data_weights)

    ## termination 3
    if current_depth >= max_depth:
        print("Termination 3 reached.")
        return create_leaf(target_values, data_weights)

    ##
    split_feature = best_split_weighted(data, features, target, data_weights)
    remaining_features = remaining_features.drop(split_feature)

    ##
    left_split = data[data[split_feature] == 0]
    left_data_weights = data_weights[data[split_feature] == 0]
    right_split = data[data[split_feature] == 1]
    right_data_weights = data_weights[data[split_feature] == 1]

    ##
    print("Split on feature %s. (%s, %s)" % (split_feature, str(len(left_split)), str(len(right_split))))

    ##
    if len(left_split) == len(data):
        print("Perfect split!")
        return create_leaf(left_split[target], left_data_weights)
    if len(right_split) == len(data):
        print("Perfect split!")
        return create_leaf(right_split[target], right_data_weights)

    ##
    left_tree = create_weighted_tree(
        left_split,
        left_data_weights,
        remaining_features,
        target,
        current_depth + 1,
        max_depth,
        min_error,
    )
    right_tree = create_weighted_tree(
        right_split,
        right_data_weights,
        remaining_features,
        target,
        current_depth + 1,
        max_depth,
        min_error,
    )

    ##
    result_node = TreeNode(False, None, split_feature)
    result_node.left = left_tree
    result_node.right = right_tree

    ##
    return result_node

def count_leafs(tree):
    ##
    if tree.is_leaf:
        return 1
    return 1 + count_leafs(tree.left) + count_leafs(tree.right)

example_data_weights = np.array(len(train_data) * [1.])
small_data_decision_tree = create_weighted_tree(train_data, example_data_weights, features, target, max_depth=2)
count_leafs(small_data_decision_tree)

def predict_single_data(
        tree,
        x,
        annotate=False,
):
    ##
    if tree.is_leaf:
        if annotate:
            print("At leaf, predicting %s" % tree.prediction)
        return tree.prediction
    ##
    split_feature_value = x[tree.split_feature]
    ##
    if annotate:
        print("Split on %s = %s" % (tree.split_feature, split_feature_value))
    if split_feature_value == 0:
        return predict_single_data(tree.left, x, annotate)
    else:
        return predict_single_data(tree.right, x, annotate)


def evaluate_accuracy(
        tree,
        data,
):
    ##
    predictions = data.apply(lambda x: predict_single_data(tree, x), axis=1)
    accuracy = (predictions == data[target]).sum() / len(data)
    return accuracy

evaluate_accuracy(small_data_decision_tree, test_data)

class WeightedDecisionTree(BaseEstimator):
    ##
    def __init__(self, max_depth=3, min_error=1e-15):
        ## Hyperparameters
        self.max_depth = max_depth
        self.min_error = min_error

    ##
    def fit(self, Xs, ys, ws = None):
        ##
        data_set = pd.concat([Xs, ys], axis=1)
        features = Xs.columns
        target = ys.columns[0]
        self.root_node = create_weighted_tree(
            data_set,
            ws,
            features,
            target,
            max_depth=self.max_depth,
            min_error=self.min_error,
        )

    ##
    def predict(self, Xs):
        ##
        return Xs.apply(lambda x: predict_single_data(self.root_node, x), axis=1)

    ##
    def score(self, Xs, ys):
        ##
        from sklearn.metrics import accuracy_score
        target = ys.columns[0]
        y_pred = self.predict(Xs)
        return accuracy_score(ys[target], y_pred)

from sklearn.base import BaseEstimator
class adaboost(BaseEstimator):
    ##
    def __init__(self, M):
        ## M is the number of trees
        self.M = M

    ##
    def fit(self, Xs, ys):
        ##
        N, _ = Xs.shape
        sample_Ws = np.ones(N) / N

        ##
        self.models = list()
        self.model_ws = list()
        self.target = ys.columns[0]

        for _ in range(self.M):
            ##
            tree = WeightedDecisionTree(max_depth=2)
            tree.fit(Xs, ys, sample_Ws)
            predictions = tree.predict(Xs)

            ## Amount of say
            weighted_error = np.dot(sample_Ws, predictions != ys[self.target])
            model_weight = 0.5 * (np.log(1 - weighted_error) -  np.log(weighted_error))

            ##
            sample_Ws *= np.exp(-model_weight * ys[self.target] * predictions)
            sample_Ws /= sample_Ws.sum()

            ##
            self.models.append(tree)
            self.model_ws.append(model_weight)

    ##
    def predict(self, Xs):
        ##
        N, _ = Xs.shape
        predictions = np.zeros(N)
        for model_weight, model in zip(self.model_ws, self.models):
            ##
            predictions += model_weight * model.predict(Xs)
        ##
        return np.sign(predictions)

    ##
    def score(self, Xs, ys):
        ##
        from sklearn.metrics import accuracy_score
        result = self.predict(Xs)
        return accuracy_score(ys[self.target], result)

model = adaboost(M=20)
model.fit(iis_Xs, iis_ys)
print(f"AdaBoost OOS performance: {model.score(oos_Xs, oos_ys)}")