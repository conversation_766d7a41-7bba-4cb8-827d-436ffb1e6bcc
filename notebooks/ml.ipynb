{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using device: <tensorflow.python.eager.context._EagerDeviceContext object at 0x313ef2440>\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-29 00:16:13.340880: I metal_plugin/src/device/metal_device.cc:1154] Metal device set to: Apple M3 Max\n", "2025-04-29 00:16:13.340899: I metal_plugin/src/device/metal_device.cc:296] systemMemory: 128.00 GB\n", "2025-04-29 00:16:13.340904: I metal_plugin/src/device/metal_device.cc:313] maxCacheSize: 48.00 GB\n", "WARNING: All log messages before absl::InitializeLog() is called are written to STDERR\n", "I0000 00:00:1745900173.340918 4724751 pluggable_device_factory.cc:305] Could not identify NUMA node of platform GPU ID 0, defaulting to 0. Your kernel may not have been built with NUMA support.\n", "I0000 00:00:1745900173.340935 4724751 pluggable_device_factory.cc:271] Created TensorFlow device (/job:localhost/replica:0/task:0/device:GPU:0 with 0 MB memory) -> physical PluggableDevice (device: 0, name: METAL, pci bus id: <undefined>)\n", "/Users/<USER>/workspaces/mle/.venv/lib/python3.12/site-packages/keras/src/layers/core/dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n", "2025-04-29 00:16:13.592943: I tensorflow/core/grappler/optimizers/custom_graph_optimizer_registry.cc:117] Plugin optimizer for device_type GPU is enabled.\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m4/4\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 7ms/step \n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Learned weight: 2.99\n", "Learned bias: 4.02\n"]}], "source": ["import tensorflow as tf\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# Check if MPS (Metal Performance Shaders) is available\n", "device = tf.device('mps' if tf.config.list_physical_devices('GPU') else 'cpu')\n", "print(f\"Using device: {device}\")\n", "\n", "# Generate synthetic data\n", "np.random.seed(0)\n", "X = 2 * np.random.rand(100, 1)\n", "y = 4 + 3 * X + np.random.randn(100, 1) * 0.1\n", "\n", "# Create the model\n", "model = tf.keras.Sequential([\n", "    tf.keras.layers.Dense(1, input_shape=(1,))\n", "])\n", "\n", "# Compile the model\n", "model.compile(\n", "    optimizer=tf.keras.optimizers.<PERSON>(learning_rate=0.1),\n", "    loss='mse'\n", ")\n", "\n", "# Train the model\n", "history = model.fit(X, y, epochs=100, verbose=0)\n", "\n", "# Plot the training loss\n", "plt.figure(figsize=(10, 5))\n", "plt.plot(history.history['loss'])\n", "plt.title('Training Loss')\n", "plt.xlabel('Epoch')\n", "plt.ylabel('Loss')\n", "plt.show()\n", "\n", "# Plot the results\n", "plt.figure(figsize=(10, 5))\n", "plt.scatter(X, y, label='Data')\n", "plt.plot(X, model.predict(X), color='red', label='Prediction')\n", "plt.xlabel('X')\n", "plt.ylabel('y')\n", "plt.title('Linear Regression with TensorFlow')\n", "plt.legend()\n", "plt.show()\n", "\n", "# Print the learned parameters\n", "weights = model.layers[0].get_weights()\n", "print(f\"Learned weight: {weights[0][0][0]:.2f}\")\n", "print(f\"Learned bias: {weights[1][0]:.2f}\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using device: <tensorflow.python.eager.context._EagerDeviceContext object at 0x329664b00>\n", "Epoch 1/100\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/workspaces/mle/.venv/lib/python3.12/site-packages/keras/src/layers/core/dense.py:87: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 9ms/step - loss: 7.0411 - mae: 2.2877 - val_loss: 6.0897 - val_mae: 2.0748\n", "Epoch 2/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 6ms/step - loss: 5.3987 - mae: 2.0211 - val_loss: 4.2768 - val_mae: 1.7493\n", "Epoch 3/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 6ms/step - loss: 3.6041 - mae: 1.6441 - val_loss: 2.9218 - val_mae: 1.4514\n", "Epoch 4/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 2.3284 - mae: 1.3299 - val_loss: 1.9333 - val_mae: 1.1882\n", "Epoch 5/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 1.6771 - mae: 1.1489 - val_loss: 1.2248 - val_mae: 0.9536\n", "Epoch 6/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 1.0512 - mae: 0.8950 - val_loss: 0.7612 - val_mae: 0.7555\n", "Epoch 7/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.6505 - mae: 0.7181 - val_loss: 0.4588 - val_mae: 0.5888\n", "Epoch 8/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.4028 - mae: 0.5660 - val_loss: 0.2615 - val_mae: 0.4449\n", "Epoch 9/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 6ms/step - loss: 0.2358 - mae: 0.4337 - val_loss: 0.1470 - val_mae: 0.3321\n", "Epoch 10/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.1328 - mae: 0.3228 - val_loss: 0.0807 - val_mae: 0.2436\n", "Epoch 11/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0735 - mae: 0.2384 - val_loss: 0.0443 - val_mae: 0.1764\n", "Epoch 12/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0431 - mae: 0.1798 - val_loss: 0.0251 - val_mae: 0.1276\n", "Epoch 13/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0267 - mae: 0.1364 - val_loss: 0.0159 - val_mae: 0.1019\n", "Epoch 14/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0183 - mae: 0.1091 - val_loss: 0.0119 - val_mae: 0.0907\n", "Epoch 15/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0145 - mae: 0.0947 - val_loss: 0.0103 - val_mae: 0.0856\n", "Epoch 16/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0111 - mae: 0.0825 - val_loss: 0.0099 - val_mae: 0.0836\n", "Epoch 17/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0091 - mae: 0.0752 - val_loss: 0.0099 - val_mae: 0.0828\n", "Epoch 18/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0104 - mae: 0.0804 - val_loss: 0.0100 - val_mae: 0.0824\n", "Epoch 19/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0108 - mae: 0.0817 - val_loss: 0.0102 - val_mae: 0.0829\n", "Epoch 20/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0097 - mae: 0.0790 - val_loss: 0.0103 - val_mae: 0.0829\n", "Epoch 21/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0099 - mae: 0.0785 - val_loss: 0.0104 - val_mae: 0.0831\n", "Epoch 22/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0092 - mae: 0.0754 - val_loss: 0.0104 - val_mae: 0.0831\n", "Epoch 23/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0093 - mae: 0.0763 - val_loss: 0.0105 - val_mae: 0.0834\n", "Epoch 24/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0095 - mae: 0.0782 - val_loss: 0.0105 - val_mae: 0.0834\n", "Epoch 25/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0100 - mae: 0.0786 - val_loss: 0.0105 - val_mae: 0.0833\n", "Epoch 26/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0091 - mae: 0.0769 - val_loss: 0.0105 - val_mae: 0.0833\n", "Epoch 27/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 6ms/step - loss: 0.0094 - mae: 0.0774 - val_loss: 0.0105 - val_mae: 0.0835\n", "Epoch 28/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0093 - mae: 0.0759 - val_loss: 0.0105 - val_mae: 0.0833\n", "Epoch 29/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0091 - mae: 0.0741 - val_loss: 0.0106 - val_mae: 0.0835\n", "Epoch 30/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0096 - mae: 0.0774 - val_loss: 0.0105 - val_mae: 0.0833\n", "Epoch 31/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0094 - mae: 0.0772 - val_loss: 0.0106 - val_mae: 0.0836\n", "Epoch 32/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0099 - mae: 0.0789 - val_loss: 0.0105 - val_mae: 0.0834\n", "Epoch 33/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0096 - mae: 0.0766 - val_loss: 0.0105 - val_mae: 0.0833\n", "Epoch 34/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0094 - mae: 0.0769 - val_loss: 0.0106 - val_mae: 0.0836\n", "Epoch 35/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0101 - mae: 0.0798 - val_loss: 0.0105 - val_mae: 0.0833\n", "Epoch 36/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0107 - mae: 0.0821 - val_loss: 0.0105 - val_mae: 0.0834\n", "Epoch 37/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0097 - mae: 0.0782 - val_loss: 0.0105 - val_mae: 0.0835\n", "Epoch 38/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0097 - mae: 0.0779 - val_loss: 0.0105 - val_mae: 0.0835\n", "Epoch 39/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0097 - mae: 0.0773 - val_loss: 0.0105 - val_mae: 0.0833\n", "Epoch 40/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0101 - mae: 0.0788 - val_loss: 0.0105 - val_mae: 0.0835\n", "Epoch 41/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0102 - mae: 0.0794 - val_loss: 0.0105 - val_mae: 0.0833\n", "Epoch 42/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0102 - mae: 0.0786 - val_loss: 0.0105 - val_mae: 0.0835\n", "Epoch 43/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0096 - mae: 0.0780 - val_loss: 0.0106 - val_mae: 0.0836\n", "Epoch 44/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0086 - mae: 0.0736 - val_loss: 0.0105 - val_mae: 0.0833\n", "Epoch 45/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0091 - mae: 0.0760 - val_loss: 0.0105 - val_mae: 0.0833\n", "Epoch 46/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0100 - mae: 0.0792 - val_loss: 0.0105 - val_mae: 0.0833\n", "Epoch 47/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0095 - mae: 0.0776 - val_loss: 0.0105 - val_mae: 0.0835\n", "Epoch 48/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0100 - mae: 0.0780 - val_loss: 0.0105 - val_mae: 0.0834\n", "Epoch 49/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0091 - mae: 0.0758 - val_loss: 0.0105 - val_mae: 0.0836\n", "Epoch 50/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0104 - mae: 0.0809 - val_loss: 0.0105 - val_mae: 0.0834\n", "Epoch 51/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0104 - mae: 0.0817 - val_loss: 0.0105 - val_mae: 0.0835\n", "Epoch 52/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0099 - mae: 0.0778 - val_loss: 0.0105 - val_mae: 0.0834\n", "Epoch 53/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0095 - mae: 0.0769 - val_loss: 0.0105 - val_mae: 0.0833\n", "Epoch 54/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0092 - mae: 0.0766 - val_loss: 0.0106 - val_mae: 0.0836\n", "Epoch 55/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0098 - mae: 0.0791 - val_loss: 0.0105 - val_mae: 0.0833\n", "Epoch 56/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0095 - mae: 0.0770 - val_loss: 0.0106 - val_mae: 0.0836\n", "Epoch 57/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0094 - mae: 0.0768 - val_loss: 0.0105 - val_mae: 0.0833\n", "Epoch 58/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0096 - mae: 0.0784 - val_loss: 0.0105 - val_mae: 0.0834\n", "Epoch 59/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0092 - mae: 0.0762 - val_loss: 0.0105 - val_mae: 0.0834\n", "Epoch 60/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0092 - mae: 0.0756 - val_loss: 0.0105 - val_mae: 0.0836\n", "Epoch 61/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0094 - mae: 0.0759 - val_loss: 0.0105 - val_mae: 0.0835\n", "Epoch 62/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0101 - mae: 0.0799 - val_loss: 0.0106 - val_mae: 0.0837\n", "Epoch 63/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0097 - mae: 0.0785 - val_loss: 0.0105 - val_mae: 0.0833\n", "Epoch 64/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0094 - mae: 0.0762 - val_loss: 0.0105 - val_mae: 0.0833\n", "Epoch 65/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0090 - mae: 0.0754 - val_loss: 0.0105 - val_mae: 0.0835\n", "Epoch 66/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0102 - mae: 0.0785 - val_loss: 0.0106 - val_mae: 0.0835\n", "Epoch 67/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0097 - mae: 0.0781 - val_loss: 0.0106 - val_mae: 0.0835\n", "Epoch 68/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0091 - mae: 0.0764 - val_loss: 0.0105 - val_mae: 0.0833\n", "Epoch 69/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0094 - mae: 0.0774 - val_loss: 0.0105 - val_mae: 0.0833\n", "Epoch 70/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0092 - mae: 0.0767 - val_loss: 0.0105 - val_mae: 0.0832\n", "Epoch 71/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0102 - mae: 0.0810 - val_loss: 0.0106 - val_mae: 0.0836\n", "Epoch 72/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0097 - mae: 0.0792 - val_loss: 0.0105 - val_mae: 0.0832\n", "Epoch 73/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0096 - mae: 0.0777 - val_loss: 0.0106 - val_mae: 0.0837\n", "Epoch 74/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 6ms/step - loss: 0.0094 - mae: 0.0774 - val_loss: 0.0104 - val_mae: 0.0831\n", "Epoch 75/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0103 - mae: 0.0793 - val_loss: 0.0105 - val_mae: 0.0834\n", "Epoch 76/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0098 - mae: 0.0796 - val_loss: 0.0105 - val_mae: 0.0834\n", "Epoch 77/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 6ms/step - loss: 0.0098 - mae: 0.0793 - val_loss: 0.0105 - val_mae: 0.0832\n", "Epoch 78/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0092 - mae: 0.0763 - val_loss: 0.0106 - val_mae: 0.0839\n", "Epoch 79/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0096 - mae: 0.0780 - val_loss: 0.0105 - val_mae: 0.0833\n", "Epoch 80/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0095 - mae: 0.0779 - val_loss: 0.0106 - val_mae: 0.0837\n", "Epoch 81/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0099 - mae: 0.0780 - val_loss: 0.0106 - val_mae: 0.0837\n", "Epoch 82/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0094 - mae: 0.0781 - val_loss: 0.0105 - val_mae: 0.0832\n", "Epoch 83/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0099 - mae: 0.0779 - val_loss: 0.0105 - val_mae: 0.0835\n", "Epoch 84/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0094 - mae: 0.0765 - val_loss: 0.0106 - val_mae: 0.0838\n", "Epoch 85/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0089 - mae: 0.0746 - val_loss: 0.0105 - val_mae: 0.0833\n", "Epoch 86/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0097 - mae: 0.0799 - val_loss: 0.0105 - val_mae: 0.0833\n", "Epoch 87/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0095 - mae: 0.0762 - val_loss: 0.0106 - val_mae: 0.0837\n", "Epoch 88/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0094 - mae: 0.0763 - val_loss: 0.0104 - val_mae: 0.0831\n", "Epoch 89/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0094 - mae: 0.0758 - val_loss: 0.0106 - val_mae: 0.0837\n", "Epoch 90/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0099 - mae: 0.0780 - val_loss: 0.0105 - val_mae: 0.0831\n", "Epoch 91/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0103 - mae: 0.0812 - val_loss: 0.0106 - val_mae: 0.0839\n", "Epoch 92/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 6ms/step - loss: 0.0101 - mae: 0.0795 - val_loss: 0.0105 - val_mae: 0.0836\n", "Epoch 93/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0104 - mae: 0.0802 - val_loss: 0.0105 - val_mae: 0.0832\n", "Epoch 94/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0100 - mae: 0.0790 - val_loss: 0.0106 - val_mae: 0.0839\n", "Epoch 95/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0092 - mae: 0.0758 - val_loss: 0.0105 - val_mae: 0.0828\n", "Epoch 96/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0092 - mae: 0.0752 - val_loss: 0.0105 - val_mae: 0.0834\n", "Epoch 97/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 6ms/step - loss: 0.0097 - mae: 0.0781 - val_loss: 0.0104 - val_mae: 0.0830\n", "Epoch 98/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0093 - mae: 0.0778 - val_loss: 0.0105 - val_mae: 0.0835\n", "Epoch 99/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0094 - mae: 0.0765 - val_loss: 0.0105 - val_mae: 0.0831\n", "Epoch 100/100\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 0.0097 - mae: 0.0778 - val_loss: 0.0104 - val_mae: 0.0831\n", "\u001b[1m25/25\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 1ms/step \n", "\u001b[1m7/7\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 4ms/step\n"]}, {"data": {"image/png": "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********************************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", "text/plain": ["<Figure size 2000x1500 with 6 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Model Parameters:\n", "     Feature  True Weights  Learned Weights  Feature Importance\n", "0  Feature 1           2.0         1.975463            1.949847\n", "1  Feature 2          -1.0        -2.006497            4.102441\n", "2  Feature 3           0.5         0.576144            0.666166\n", "\n", "True Bias: 4.000\n", "Learned Bias: 1.808\n", "\n", "Training Loss: 0.0097\n", "Test Loss: 0.0104\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import tensorflow as tf\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import pandas as pd\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.model_selection import train_test_split\n", "\n", "# Set style for better visualizations\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "# Check if MPS is available\n", "device = tf.device('mps' if tf.config.list_physical_devices('GPU') else 'cpu')\n", "print(f\"Using device: {device}\")\n", "\n", "# Generate synthetic data with multiple features\n", "np.random.seed(0)\n", "n_samples = 1000\n", "n_features = 3\n", "\n", "# Generate features with different distributions\n", "X = np.zeros((n_samples, n_features))\n", "X[:, 0] = np.random.normal(0, 1, n_samples)  # Normal distribution\n", "X[:, 1] = np.random.exponential(2, n_samples)  # Exponential distribution\n", "X[:, 2] = np.random.uniform(-2, 2, n_samples)  # Uniform distribution\n", "\n", "# True parameters\n", "true_weights = np.array([2.0, -1.0, 0.5])\n", "true_bias = 4.0\n", "\n", "# Generate target variable with some noise\n", "y = np.dot(X, true_weights) + true_bias + np.random.randn(n_samples) * 0.1\n", "y = y.reshape(-1, 1)\n", "\n", "# Split the data\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)\n", "\n", "# Scale the features\n", "scaler = StandardScaler()\n", "X_train_scaled = scaler.fit_transform(X_train)\n", "X_test_scaled = scaler.transform(X_test)\n", "\n", "# Create the model\n", "model = tf.keras.Sequential([\n", "    tf.keras.layers.Dense(1, input_shape=(n_features,))\n", "])\n", "\n", "# Compile the model\n", "model.compile(\n", "    optimizer=tf.keras.optimizers.<PERSON>(learning_rate=0.01),\n", "    loss='mse',\n", "    metrics=['mae']\n", ")\n", "\n", "# Train the model\n", "history = model.fit(\n", "    X_train_scaled, y_train,\n", "    epochs=100,\n", "    batch_size=32,\n", "    validation_data=(X_test_scaled, y_test),\n", "    verbose=1\n", ")\n", "\n", "# Create a figure with multiple subplots\n", "plt.figure(figsize=(20, 15))\n", "\n", "# 1. Feature Distributions\n", "plt.subplot(3, 2, 1)\n", "for i in range(n_features):\n", "    sns.kdeplot(X[:, i], label=f'Feature {i+1}')\n", "plt.title('Feature Distributions')\n", "plt.legend()\n", "\n", "# 2. Training History\n", "plt.subplot(3, 2, 2)\n", "plt.plot(history.history['loss'], label='Training Loss')\n", "plt.plot(history.history['val_loss'], label='Validation Loss')\n", "plt.title('Model Loss During Training')\n", "plt.xlabel('Epoch')\n", "plt.ylabel('Loss')\n", "plt.legend()\n", "\n", "# 3. Actual vs Predicted (Training)\n", "y_train_pred = model.predict(X_train_scaled)\n", "plt.subplot(3, 2, 3)\n", "plt.scatter(y_train, y_train_pred, alpha=0.5)\n", "plt.plot([y_train.min(), y_train.max()], [y_train.min(), y_train.max()], 'r--', lw=2)\n", "plt.xlabel('Actual Values')\n", "plt.ylabel('Predicted Values')\n", "plt.title('Actual vs Predicted (Training)')\n", "\n", "# 4. Actual vs Predicted (Test)\n", "y_test_pred = model.predict(X_test_scaled)\n", "plt.subplot(3, 2, 4)\n", "plt.scatter(y_test, y_test_pred, alpha=0.5)\n", "plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)\n", "plt.xlabel('Actual Values')\n", "plt.ylabel('Predicted Values')\n", "plt.title('Actual vs Predicted (Test)')\n", "\n", "# 5. Residuals Plot\n", "residuals = y_test - y_test_pred\n", "plt.subplot(3, 2, 5)\n", "sns.scatterplot(x=y_test_pred.flatten(), y=residuals.flatten())\n", "plt.axhline(y=0, color='r', linestyle='--')\n", "plt.xlabel('Predicted Values')\n", "plt.ylabel('Residuals')\n", "plt.title('Residuals Plot')\n", "\n", "# 6. Feature Importance\n", "weights = model.layers[0].get_weights()[0].flatten()\n", "feature_importance = np.abs(weights * np.std(X, axis=0))\n", "plt.subplot(3, 2, 6)\n", "sns.barplot(x=[f'Feature {i+1}' for i in range(n_features)], y=feature_importance)\n", "plt.title('Feature Importance')\n", "plt.xticks(rotation=45)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Print model performance metrics\n", "train_loss = model.evaluate(X_train_scaled, y_train, verbose=0)\n", "test_loss = model.evaluate(X_test_scaled, y_test, verbose=0)\n", "\n", "# Create a DataFrame with true vs learned parameters\n", "params_df = pd.DataFrame({\n", "    'Feature': [f'Feature {i+1}' for i in range(n_features)],\n", "    'True Weights': true_weights,\n", "    'Learned Weights': weights,\n", "    'Feature Importance': feature_importance\n", "})\n", "\n", "print(\"\\nModel Parameters:\")\n", "print(params_df)\n", "print(f\"\\nTrue Bias: {true_bias:.3f}\")\n", "print(f\"Learned Bias: {model.layers[0].get_weights()[1][0]:.3f}\")\n", "print(f\"\\nTraining Loss: {train_loss[0]:.4f}\")\n", "print(f\"Test Loss: {test_loss[0]:.4f}\")\n", "\n", "# Additional correlation heatmap\n", "plt.figure(figsize=(10, 8))\n", "data = np.hstack([X, y])\n", "cols = [f'Feature {i+1}' for i in range(n_features)] + ['Target']\n", "correlation_matrix = pd.DataFrame(data, columns=cols).corr()\n", "sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0)\n", "plt.title('Correlation Heatmap')\n", "plt.tight_layout()\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 2}