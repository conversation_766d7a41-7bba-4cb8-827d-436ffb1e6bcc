[project]
name = "mle"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "box2d>=2.3.10",
    "graphviz>=0.20.3",
    "gymnasium>=1.1.1",
    "jupyter>=1.1.1",
    "lightgbm>=4.6.0",
    "matplotlib>=3.10.1",
    "mlx>=0.26.1",
    "nnfs>=0.5.1",
    "numpy>=2.1.3",
    "opencv-python>=*********",
    "pandas>=2.2.3",
    "patsy>=1.0.1",
    "polars>=1.28.1",
    "pyarrow>=20.0.0",
    "pygame>=2.6.1",
    "scikit-image>=0.25.2",
    "scikit-learn>=1.6.1",
    "scipy>=1.15.2",
    "seaborn>=0.13.2",
    "statsmodels>=0.14.4",
    "swig>=4.3.1",
    "tensorflow>=2.19.0",
    "tensorflow-metal>=1.2.0",
    "tqdm>=4.67.1",
    "xgboost>=3.0.2",
]
